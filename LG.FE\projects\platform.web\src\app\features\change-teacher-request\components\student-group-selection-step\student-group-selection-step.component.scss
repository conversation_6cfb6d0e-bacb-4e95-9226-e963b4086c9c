.student-group-selection-step {
  :host ::ng-deep {
    .p-card {
      transition: all 0.2s ease-in-out;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &.border-primary-500 {
        box-shadow: 0 0 0 1px var(--primary-500), 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }

    .p-radiobutton .p-radiobutton-box {
      border-color: var(--primary-500);

      &.p-highlight {
        background-color: var(--primary-500);
        border-color: var(--primary-500);
      }
    }

    .teaching-language-section,
    .student-selection-section,
    .group-selection-section {
      app-prime-students-selection,
      app-prime-student-group-selection {
        display: block;
        width: 100%;
      }

      .p-dropdown {
        width: 100%;

        .p-dropdown-label {
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }
      }
    }

    .border-left-3 {
      border-left: 3px solid !important;
    }
  }

  @media (max-width: 768px) {
    :host ::ng-deep {
      .p-card {
        margin-bottom: 1rem;

        .p-card-body {
          padding: 1rem;
        }
      }

      .text-2xl { font-size: 1.5rem !important; }
      .text-xl { font-size: 1.25rem !important; }
    }
  }
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.student-selection-section,
.group-selection-section {
  animation: fadeInUp 0.3s ease-out;
}
