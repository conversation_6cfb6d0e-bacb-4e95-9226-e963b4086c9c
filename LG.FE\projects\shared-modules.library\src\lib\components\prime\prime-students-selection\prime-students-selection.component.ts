import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  inject,
  Injector,
  Input,
  input,
  model,
  Output,
  signal,
  ViewChild,
  type OnInit,
  computed,
  DestroyRef,
  AfterViewInit,
  OnDestroy,
  WritableSignal,
  Signal
} from '@angular/core';
import { toObservable, takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { MultiSelect, MultiSelectModule } from 'primeng/multiselect';
import { Select, SelectModule } from 'primeng/select';
import { TooltipModule } from 'primeng/tooltip';
import { InputTextModule } from 'primeng/inputtext';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { ISearchStudentDto, IStudentTeachingLanguageDto, IGetStudentsRequest, IGetStudentsResponse, IGenderEnum } from '../../../GeneratedTsFiles';
import { PrimeProfilePhotoSingleComponent } from '../prime-profile-photo-single/prime-profile-photo-single.component';
import { untilDestroyed } from '../../../helpers/until-destroyed';
import { DataApiStateService, State } from '../../../services/data-api-state.service';
import { EventBusService, Events, EmitEvent, DefaultGetStudentsRequest } from '../../../services/event-bus.service';
import { AuthStateService } from '../../../services/auth-state.service';
import { IUserRole, nameOf } from '../../../models/general.model';
import { GeneralService } from '../../../services/general.service';
import { BaseDropdownComponent, DropdownItem } from '../../base/base-dropdown.component';
import { Observable } from 'rxjs';

@Component({
  selector: 'lib-prime-students-selection',
  imports: [
    CommonModule,
    FormsModule,
    SelectModule,
    ButtonModule,
    TooltipModule,
    MultiSelectModule,
    PrimeProfilePhotoSingleComponent,
    InputTextModule,
    IconFieldModule,
    InputIconModule,
  ],
  templateUrl: './prime-students-selection.component.html',
  styleUrl: './prime-students-selection.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PrimeStudentsSelectionComponent
  extends BaseDropdownComponent<ISearchStudentDto, IGetStudentsRequest, IGetStudentsResponse>
  implements AfterViewInit {

  // ===========================
  // VIEW REFERENCES
  // ===========================

  @ViewChild('selectStudentsList') select: Select | undefined;
  @ViewChild('selectStudentsListMultiple') selectMultiple: MultiSelect | undefined;

  // ===========================
  // INJECTED SERVICES
  // ===========================

  readonly generalService = inject(GeneralService);
  private readonly dataStateService = inject(DataApiStateService);
  private readonly authService = inject(AuthStateService);
  private readonly untilDestroyed = untilDestroyed();

  // ===========================
  // BACKWARD COMPATIBILITY INPUTS
  // ===========================

  /** Legacy properties maintained for backward compatibility */
  override selectedItem = signal<ISearchStudentDto | null>(null);
  @Input() override emptyMessageText = 'No students found.';

  /** Legacy items input - enhanced with pagination support */
  items = input<ISearchStudentDto[]>([]);

  // ===========================
  // INPUT SIGNALS
  // ===========================

  /** Style class for the component */
  styleClass = input('w-full full-width mb-2');
  selectedItemProperty = input('');
  baseProperty = input('');
  textForNameProperty = input('');
  resetSelectionSignal = input(false);

  // ===========================
  // OUTPUTS
  // ===========================

  /** Emitted when a student or students are selected */
  @Output() itemClicked = new EventEmitter<ISearchStudentDto | ISearchStudentDto[]>();

  /** Emitted when the "create new" option is clicked */
  @Output() newItemClicked = new EventEmitter<void>();

  // ===========================
  // SELECTION STATE (BACKWARD COMPATIBILITY)
  // ===========================

  /** Currently selected student (single selection mode) */
  selectedStudent = model<ISearchStudentDto>({} as ISearchStudentDto);

  /** Currently selected students (multiple selection mode) */
  selectedStudents = model<ISearchStudentDto[]>([]);

  // ===========================
  // COMPUTED PROPERTIES (ENHANCED)
  // ===========================

  /** Items to display in dropdown with legacy support */
  override readonly displayItems = computed(() => {
    if (this.enablePagination) {
      // Use the same logic as the parent's displayItems computed property
      if (!this.enablePagination) {
        return this.allItems();
      }

      const items = this.isSearchMode() ? this.searchResults() : this.allItems();
      return Array.isArray(items) ? items.filter(item => item != null) : [];
    } else {
      // Legacy mode: use provided items and map them to ISearchStudentDto
      return this.filterValidStudentItems(this.items()).map(item => this.mapToISearchStudentDto(item));
    }
  });

  /** Students state from the data service */
  students$ = computed(() => this.dataStateService.parentStudents.state() as State<IGetStudentsResponse>);

  /** Maximum number of languages to display before truncation */
  private readonly MAX_VISIBLE_LANGUAGES = 2;

  /** Custom search input value for immediate UI updates */
  searchInputValue = '';

  // ===========================
  // ABSTRACT METHOD IMPLEMENTATIONS
  // ===========================

  protected getApiEvent(): Events {
    return Events.StateLoadParentStudents;
  }

  protected buildApiRequest(baseRequest: IGetStudentsRequest): IGetStudentsRequest {
    const userId = this.authService.getUserClaims()?.id;

    const request: IGetStudentsRequest = {
      pageNumber: baseRequest.pageNumber,
      pageSize: baseRequest.pageSize,
      sortColumn: null,
      sortDirection: null,
      searchTerm: baseRequest.searchTerm || null,
      gender: IGenderEnum.None,
      registeredFrom: null,
      registeredTo: null,
      teachingLanguage: null,
      teachingLanguageLevel: null,
      speakingLanguage: null,
      isNativeSpeakingLanguage: null,
      includeBlocked: null,
      accountStatus: null,
      studentAgesMin: 0,
      studentAgesMax: 17,
      teacherId: this.authService.getUserRole() === IUserRole.TEACHER ? userId : null,
      parentId: this.authService.getUserRole() === IUserRole.PARENT ? userId : null,
    };

    return new DefaultGetStudentsRequest(request) as IGetStudentsRequest;
  }

  protected createStateSelector(): Observable<State<IGetStudentsResponse>> {
    return toObservable(
      computed(() => this.dataStateService.parentStudents.state()),
      { injector: this.injector }
    );
  }

  // ===========================
  // LIFECYCLE METHODS
  // ===========================

  override ngOnInit(): void {
    // Set up configuration from inputs
    this.idProperty = 'userId' as keyof ISearchStudentDto;

    super.ngOnInit();
    this.setupResetSubscriptions();
  }

  ngAfterViewInit(): void {
    // Setup any additional view-specific logic if needed
  }

  // ===========================
  // BACKWARD COMPATIBILITY SETUP
  // ===========================

  private setupResetSubscriptions(): void {
    // Subscribe to reset selection signal
    toObservable(this.resetSelectionSignal, {
      injector: this.injector
    }).pipe(this.untilDestroyed()).subscribe({
      next: (reset) => {
        console.log('🔄 Students resetSelectionSignal received:', reset);
        if (reset) {
          console.log('🔄 Students triggering clearSelectionUIOnly');
          this.clearSelectionUIOnly();
        }
      }
    });

    // Subscribe to base component selection changes for backward compatibility
    toObservable(this.selectedItem, { injector: this.injector })
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(item => {
        if (item && this.selectionMode === 'single') {
          this.selectedStudent.set(item as ISearchStudentDto);
          this.itemClicked.emit(item as ISearchStudentDto);
        }
      });

    toObservable(this.selectedItems, { injector: this.injector })
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(items => {
        if (this.selectionMode === 'multiple') {
          this.selectedStudents.set(items as ISearchStudentDto[]);
          this.itemClicked.emit(items as ISearchStudentDto[]);
        }
      });
  }

  // ===========================
  // EVENT HANDLERS (ENHANCED WITH BASE COMPONENT)
  // ===========================

  /**
   * Handle selection change event using base component method
   */
  onSelectionChange(event: { value: ISearchStudentDto | ISearchStudentDto[] }): void {
    this.handlePrimeSelectionChange(
      event,
      this.selectedStudent,
      this.selectedStudents,
      this.itemClicked
    );
  }

  /**
   * Handle custom search input changes with debouncing
   */
  override onSearchInputChange(event: Event | string): void {
    // Handle both Event objects and string values
    const value = typeof event === 'string' ? event : (event.target as HTMLInputElement).value;

    // Prevent event bubbling to avoid dropdown closing (only for Event objects)
    if (typeof event !== 'string') {
      event.stopPropagation();
    }

    this.searchInputValue = value;

    console.log('🔍 Search input changed:', value);

    // Delegate to base component
    super.onSearchInputChange(value);
  }

  /**
   * Clear the search input (but keep dropdown open)
   */
  override clearSearchInput(event?: Event): void {
    // Prevent event bubbling to avoid dropdown closing
    if (event) {
      event.stopPropagation();
    }

    this.searchInputValue = '';

    // Delegate to base component
    super.clearSearchInput();

    console.log('🧹 Search input cleared');
  }

  /**
   * Load more data (next page)
   */
  onLoadMoreClicked(): void {
    super.loadMoreData();
  }


  // ===========================
  // PRIVATE HELPER METHODS
  // ===========================
  /**
   * Filters out invalid items from the array (student-specific implementation)
   * @param items Array of student items to filter
   * @returns Filtered array of valid student items
   */
  private filterValidStudentItems(items: ISearchStudentDto[]): ISearchStudentDto[] {
    return this.filterValidItems(items);
  }

  /**
   * Maps ISearchStudentDto to ISearchStudentDto
   * @param student Student DTO to map
   * @returns ISearchStudentDto with required id property
   */
  private mapToISearchStudentDto(student: ISearchStudentDto): ISearchStudentDto {
    return {
      ...student,
      id: student.userId || '', // Map userId to id for DropdownItem compatibility
    } as ISearchStudentDto;
  }

  /**
   * Get student's full name for display
   * @param student Student object
   * @returns Full name string
   */
  getStudentFullName(student: ISearchStudentDto): string {
    if (!student) return 'Unknown Student';

    const firstName = student.firstName || '';
    const lastName = student.lastName || '';

    return `${firstName} ${lastName}`.trim() || 'Unknown Student';
  }


  /**
   * Get visible languages for display (with truncation)
   * @param student Student object
   * @returns Array of visible languages
   */
  getVisibleLanguages(student: IStudentTeachingLanguageDto[]): IStudentTeachingLanguageDto[] {
    if (!student || !Array.isArray(student)) {
      return [];
    }

    return student.slice(0, this.MAX_VISIBLE_LANGUAGES);
  }

  /**
   * Check if languages should be truncated
   * @param languages Array of languages
   * @returns True if should truncate
   */
  shouldTruncateLanguages(languages: IStudentTeachingLanguageDto[]): boolean {
    if (!languages || !Array.isArray(languages)) {
      return false;
    }

    return languages.length > this.MAX_VISIBLE_LANGUAGES;
  }

  /**
   * Get remaining languages count for display
   * @param languages Array of languages
   * @returns Number of remaining languages
   */
  getRemainingLanguagesCount(languages: IStudentTeachingLanguageDto[]): number {
    if (!languages || !Array.isArray(languages)) {
      return 0;
    }

    return Math.max(0, languages.length - this.MAX_VISIBLE_LANGUAGES);
  }

  /**
   * Get tooltip text for remaining languages
   * @param languages Array of languages
   * @returns Tooltip text
   */
  getRemainingLanguagesTooltip(languages: IStudentTeachingLanguageDto[]): string {
    if (!languages || !Array.isArray(languages)) {
      return '';
    }

    const remainingLanguages = languages.slice(this.MAX_VISIBLE_LANGUAGES);
    return remainingLanguages.map(lang => lang.teachingLanguageName || 'Unknown').join(', ');
  }

  /**
   * Handle multiple selection change event
   */
  onSelectMultipleChange(event: { value: ISearchStudentDto[] }): void {
    console.log('Multiple selection changed:', event);
    this.selectedStudents.set(event.value);
    this.itemClicked.emit(event.value);
  }

  /**
   * Handle clear selection event from PrimeNG dropdown
   * Simply clears the UI - the parent will handle temp filter clearing via the empty initialSelectedId
   */
  onClearSelection(): void {
    console.log('🗑️ Clear selection clicked for students - clearing UI only');

    // Clear the UI state only
    this.handlePrimeClearSelectionUIOnly(
      this.selectedStudent,
      this.selectedStudents
    );

    // ✅ Don't emit any events - let the parent handle temp filter clearing
    // The parent will see that studentId() returns empty and update accordingly

    console.log('🗑️ Student UI cleared - parent will handle temp filter via binding');
  }

  /**
   * Clear all selected students using base component method
   */
  clearAllStudents(): void {
    this.clearAllSelections(this.selectedStudents, this.itemClicked);
  }

  /**
   * Select all visible students
   */
  selectAllStudents(): void {
    const allStudents = this.displayItems() as ISearchStudentDto[];
    this.selectedStudents.set(allStudents);
    this.itemClicked.emit(allStudents);
  }

  /**
   * Override dropdown show logic to preserve initial selection state or load fresh data
   * This prevents the base class from resetting the search term when dropdown opens with initial selection
   * But ensures fresh data loading when no initial selection is present
   */
  protected override handleDropdownShowLogic(): void {
    // Check if we have an initial selection that should be preserved
    if (this.initialSelectedId && this.enableInitialItemSelection) {
      console.log('🎯 Preserving initial student selection on dropdown open:', this.initialSelectedId);
      // Don't reset - keep the search term and search mode for initial selection
      this.focusSearchInputIfEnabled();
      return;
    }

    // No initial selection - load fresh data from page 1
    console.log('🔄 No initial selection - loading fresh data for students dropdown');

    // Reset to ensure clean state
    this.resetPagination();

    // Load fresh data from page 1
    if (this.enablePagination && this.isInitialized()) {
      console.log('🔄 Loading initial data for fresh student search');
      this.loadInitialData();
    }

    // Focus search input if enabled
    this.focusSearchInputIfEnabled();
  }

  /**
   * Clear selection UI only without emitting events (for reset functionality)
   */
  override clearSelectionUIOnly(): void {
    console.log('🔄 clearSelectionUIOnly called for students - performing complete reset');

    // Step 1: Clear the dropdown UI first
    if (this.selectionMode === 'single') {
      console.log('🔄 Clearing single select dropdown UI');
      // Clear the PrimeNG dropdown first
      this.select?.clear();
      // Reset the model to undefined to ensure proper clearing
      this.selectedStudent.set(undefined as any);
      // Also clear the base component selection
      this.selectedItem.set(null);
    } else {
      console.log('🔄 Clearing multi select dropdown UI');
      this.selectedStudents.set([]);
      if (this.selectMultiple) {
        this.selectMultiple.updateModel([]);
      }
      // Also clear the base component selection
      this.selectedItems.set([]);
    }

    // Step 2: Clear initial selection properties to ensure fresh behavior
    console.log('🔄 Clearing initial selection properties');
    this.initialSelectedId = null;
    this.enableInitialItemSelection = false;

    // Step 3: Reset all base component state (search, pagination, data, etc.)
    console.log('🔄 Resetting pagination and search state');
    this.resetPagination();

    // Step 4: Clear any cached data and reset to fresh state
    console.log('🔄 Clearing cached data for fresh search');
    this.allItems.set([]);
    this.searchResults.set([]);
    this.isInitialized.set(true); // Keep initialized but reset data

    console.log('🔄 Students dropdown reset complete - ready for fresh search');
    console.log('🔄 Final state check:', {
      selectedStudent: this.selectedStudent(),
      selectedItem: this.selectedItem(),
      allItems: this.allItems().length,
      searchResults: this.searchResults().length,
      initialSelectedId: this.initialSelectedId,
      enableInitialItemSelection: this.enableInitialItemSelection
    });

    // Do NOT emit itemClicked when resetting - this should only reset UI state
    // The actual filter changes will be applied when user clicks "Apply filters"
  }

}
