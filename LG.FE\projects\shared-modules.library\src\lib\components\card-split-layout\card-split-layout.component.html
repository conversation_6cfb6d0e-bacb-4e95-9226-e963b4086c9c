<div class="{{containerClass}}">
    <div class="flex flex-wrap container-960-max {{wrapperClass}}  lg:flex-row"
        [ngStyle]="{'max-width': maxWidth}" [ngClass]="{'justify-center': !showRightSide}">
        
        <ng-content select="[center]"></ng-content>
        <div *ngIf="showLeftSide" [class]="leftSideClass" 
        [ngClass]="{'only-left-side': !showRightSide, 'no-padding': !showRightSide}"
            [ngStyle]="{'margin': showRightSide ? '0' : '0 auto'}">
            <ng-content select="[left]"></ng-content>
        </div>

        <div *ngIf="showRightSide" [class]="rightSideContainerClass"
            [ngStyle]="{'background-image': 'url(' + rightBgImage + ')'}">
            <ng-content select="[right]"></ng-content>
        </div>

    </div>
</div>