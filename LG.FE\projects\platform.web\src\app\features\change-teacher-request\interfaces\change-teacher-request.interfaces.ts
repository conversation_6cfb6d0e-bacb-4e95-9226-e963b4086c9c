/**
 * Enums for Teacher Change Request functionality
 */

/**
 * Types of teacher change requests available
 */
export enum TeacherChangeType {
  /** Complete teacher change affecting all lessons */
  COMPLETE_CHANGE = 'complete_change',
  /** Teacher change for specific student or group only */
  STUDENT_GROUP_CHANGE = 'student_group_change'
}

/**
 * Steps in the teacher change request wizard
 */
export enum ChangeTeacherStep {
  /** Step 1: Select type of teacher change */
  SELECTION = 1,
  /** Step 2: Select student or group (only for STUDENT_GROUP_CHANGE) */
  STUDENT_GROUP_SELECTION = 2,
  /** Step 3: Provide change details */
  DETAILS = 3,
  /** Step 4: Review & Confirm request */
  REVIEW_CONFIRM = 4
}


/**
 * Interface for wizard navigation
 */
export interface IWizardNavigation {
  /** Current step */
  currentStep: ChangeTeacherStep;
  /** Whether can go to previous step */
  canGoPrevious: boolean;
  /** Whether can go to next step */
  canGoNext: boolean;
  /** Whether can submit the request */
  canSubmit: boolean;
  /** Total number of steps */
  totalSteps: number;
}
