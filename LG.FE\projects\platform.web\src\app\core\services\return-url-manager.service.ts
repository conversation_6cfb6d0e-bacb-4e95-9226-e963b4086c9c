import { Injectable, inject } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { AuthStateService } from 'SharedModules.Library';
import { filter } from 'rxjs/operators';

/**
 * Configuration for return URL management patterns.
 * This can be easily extended or modified.
 */
export const RETURN_URL_MANAGEMENT_CONFIG = {
  // Routes that should clear return URL (user choosing different auth flow)
  clearReturnUrlPatterns: [
    /^\/auth\/register/,
    /^\/register/,
    /^\/pages\/register/,
    /^\/auth\/forgot-password/,
    /^\/reset-password/,
    /^\/auth\/social-auth/,
    /^\/complete-registration/,
  ],
  
  // Routes that should preserve return URL (login-related flows)
  preserveReturnUrlPatterns: [
    /^\/auth\/login/,
    /^\/auth\/otp/,
    /^\/set-password-first-time/,
    /^\/signin/,
  ],
  
  // Enable debug logging
  enableDebugLogging: true,
} as const;

/**
 * Service that automatically manages return URL clearing based on navigation patterns.
 * This service listens to all router navigation events and clears return URLs
 * when users navigate to auth flows that indicate they're not trying
 * to complete the original login intent.
 * 
 * This works for ALL navigation methods:
 * - Router links
 * - Programmatic navigation
 * - Direct URL entry
 * - Browser back/forward
 * - External links
 */
@Injectable({
  providedIn: 'root'
})
export class ReturnUrlManagerService {
  private readonly router = inject(Router);
  private readonly authService = inject(AuthStateService);

  constructor() {
    this.initializeNavigationListener();
  }

  /**
   * Initialize the navigation listener that monitors all route changes
   */
  private initializeNavigationListener(): void {
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: NavigationEnd) => {
      this.handleNavigation(event.url);
    });
  }

  /**
   * Handle navigation to determine if return URL should be cleared
   * @param currentUrl The URL being navigated to
   */
  private handleNavigation(currentUrl: string): void {
    // Only process if user is not authenticated and has a return URL
    if (this.authService.isAuth() || !this.authService.hasReturnUrl()) {
      return;
    }

    // Check if current route should clear return URL
    if (this.shouldClearReturnUrl(currentUrl)) {
      this.authService.removeReturnUrl();
      console.debug(`ReturnUrlManagerService: Cleared return URL for navigation to ${currentUrl}`);
    }
  }

  /**
   * Determine if the current URL should trigger return URL clearing
   * @param url The URL to check
   * @returns True if return URL should be cleared
   */
  private shouldClearReturnUrl(url: string): boolean {
    // Routes that should clear return URL (user choosing different auth flow)
    const routesThatClearReturnUrl = RETURN_URL_MANAGEMENT_CONFIG.clearReturnUrlPatterns;

    // Routes that should preserve return URL (login-related flows)
    const routesThatPreserveReturnUrl = RETURN_URL_MANAGEMENT_CONFIG.preserveReturnUrlPatterns;

    // Check if current route should preserve return URL (login flows)
    const shouldPreserveReturnUrl = routesThatPreserveReturnUrl.some(
      pattern => pattern.test(url)
    );

    if (shouldPreserveReturnUrl) {
      return false; // Don't clear return URL for login-related flows
    }

    // Check if current route should clear return URL (non-login auth flows)
    const shouldClearReturnUrl = routesThatClearReturnUrl.some(
      pattern => pattern.test(url)
    );

    return shouldClearReturnUrl;
  }

  /**
   * Manually clear return URL (for programmatic use if needed)
   */
  public clearReturnUrl(): void {
    this.authService.removeReturnUrl();
  }

  /**
   * Check if return URL exists (for programmatic use if needed)
   */
  public hasReturnUrl(): boolean {
    return this.authService.hasReturnUrl();
  }
}
