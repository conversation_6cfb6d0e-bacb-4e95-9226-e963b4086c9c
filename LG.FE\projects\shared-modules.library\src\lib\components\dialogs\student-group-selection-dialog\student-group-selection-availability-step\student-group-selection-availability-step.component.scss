:host {
  display: block;
}

:host ::ng-deep {
  .close-panel-btn {
    .p-button {
      width: 1.75rem;
      height: 1.75rem;
      padding: 0;
      background: transparent;
      border: none;
      transition: all 0.2s ease;
      
      .p-button-icon {
        font-size: 0.875rem;
        color: var(--green-700);
        margin: 0;
      }

      &:hover {
        background: rgba(34, 197, 94, 0.1);
        .p-button-icon {
          color: var(--green-800);
        }
      }

      &:focus {
        box-shadow: 0 0 0 2px var(--green-100);
      }
    }
  }
}