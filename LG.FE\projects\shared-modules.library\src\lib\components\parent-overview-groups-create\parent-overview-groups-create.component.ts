// ... existing code ...
import { ChangeDetectionStrategy, Component, computed, DestroyRef, EventEmitter, inject, Injector, input, linkedSignal, On<PERSON><PERSON>roy, OnInit, signal, Signal } from '@angular/core';

import { ButtonModule } from 'primeng/button';
import { AbstractControl, FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Router } from '@angular/router';
import { InputTextModule } from 'primeng/inputtext';
import { SelectChangeEvent, SelectModule } from 'primeng/select';
import { toObservable } from '@angular/core/rxjs-interop';
import { TextareaModule } from 'primeng/textarea';
import { ActivatedRoute } from '@angular/router';
import { SmartStudentSelectorComponent } from './components/smart-student-selector/smart-student-selector.component';
import { ParentOverviewGroupCreateFormComponent } from './components/parent-overview-group-create-form/parent-overview-group-create-form.component';
import { StudentGroupSelectionSuggestionTextStepComponent } from '../dialogs/student-group-selection-dialog/student-group-selection-suggestion-text-step/student-group-selection-suggestion-text-step.component';
import { PrimeStudentsSelectionComponent } from '../prime/prime-students-selection/prime-students-selection.component';
import { StudentGroupService } from '../../services/student-group.service';
import { StudentGroupFormService } from '../../services/student-group-form.service';
import { AvailabilityPickerDaysComponent } from '../availability-picker-days/availability-picker-days.component';
import { AvailabilityTimezoneSelectorComponent } from '../availability-picker-days/availability-timezone-selector/availability-timezone-selector.component';
import { PrimeReactiveFormInputComponent } from '../prime/prime-reactive-form-input/prime-reactive-form-input.component';
import { PrimeDropdownComponent } from '../prime/prime-dropdown/prime-dropdown.component';
import { FormFieldValidationMessageComponent } from '../prime/form-field-validation-message/form-field-validation-message.component';
import { ICreateStudentGroupRequest, IEditStudentGroupRequest } from '../../GeneratedTsFiles';
import { GeneralService } from '../../services/general.service';
import { AuthStateService } from '../../services/auth-state.service';
import { HandleApiResponseService } from '../../services/handle-api-response.service';
import { DataApiStateService } from '../../services/data-api-state.service';
import { ToastService } from '../../services/toast.service';
import { EventBusService } from '../../services/event-bus.service';
import { FormErrorScrollerService } from '../../services/form-error-scroller.service';
import { ApiLoadingStateService } from '../../services/api-loading-state.service';
import { GroupDialogState } from '../../models/general.model';
import { CardSplitLayoutComponent } from '../card-split-layout/card-split-layout.component';


// Type helper to create FormControl types from interface
type FormControlsOf<T> = {
  [K in keyof T]: K extends 'availability'
  ? FormControl<T[K] | null>
  : T[K] extends object
  ? T[K] extends any[]
  ? FormControl<T[K] | null>
  : FormGroup<FormControlsOf<T[K]>>
  : FormControl<T[K] | null>;
};

@Component({
  selector: 'app-parent-overview-groups-create',
  imports: [
    CommonModule,
    CardSplitLayoutComponent,
    ButtonModule,
    StudentGroupSelectionSuggestionTextStepComponent,
    FormsModule,
    ReactiveFormsModule,
    InputTextModule,
    TextareaModule,
    SelectModule,
    AvailabilityPickerDaysComponent,
    AvailabilityTimezoneSelectorComponent,
    PrimeStudentsSelectionComponent,
    PrimeReactiveFormInputComponent,
    PrimeDropdownComponent,
    FormFieldValidationMessageComponent,
    SmartStudentSelectorComponent,
    ParentOverviewGroupCreateFormComponent,
  ],
  templateUrl: './parent-overview-groups-create.component.html',
  styleUrl: './parent-overview-groups-create.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})

export class ParentOverviewGroupsCreateComponent implements OnInit, OnDestroy {
  private readonly destroyRef = inject(DestroyRef);
  groupForm!: FormGroup<FormControlsOf<ICreateStudentGroupRequest>>;
  editGroupForm!: FormGroup<FormControlsOf<IEditStudentGroupRequest>>;

  // Injected services
  readonly services = {
    general: inject(GeneralService),
    auth: inject(AuthStateService),
    api: inject(HandleApiResponseService),
    studentGroup: inject(StudentGroupService),
    dataState: inject(DataApiStateService),
    toast: inject(ToastService),
    eventBus: inject(EventBusService),
    formErrorScroller: inject(FormErrorScrollerService),
    apiLoadingStateService: inject(ApiLoadingStateService),
    router: inject(Router)
  };

  private readonly injector = inject(Injector);

  readonly EditGroupStateEnum = GroupDialogState;
  protected readonly GroupDialogState = GroupDialogState;
  editGroupState = signal<GroupDialogState>(GroupDialogState.CreateGroupSuggestionStep);

  isEditMode = signal<boolean>(false);
  groupId = signal<string | null>(null);

  constructor(private route: ActivatedRoute) { }

  ngOnInit(): void {
    this.handleQueryParams();

  }

  ngOnDestroy(): void {
    // Router subscriptions are automatically cleaned up by takeUntilDestroyed
  }



  private handleQueryParams(): void {
    this.route.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((params: any) => {
      const groupIdFromParams = params['groupId'] || null;
      this.groupId.set(groupIdFromParams);

      if (groupIdFromParams) {
        this.isEditMode.set(true);
        this.editGroupState.set(GroupDialogState.EditGroup);
      } else {
        this.isEditMode.set(false);
        this.editGroupState.set(GroupDialogState.CreateGroupSuggestionStep);
      }

      if (this.isEditMode()) {
      } else {
      }
    });
  }



  private handleGetGroupError = (error: any): void => {
    console.error('Failed to load group details', error);
    this.services.toast.show({ severity: 'error', summary: 'Error', detail: 'Failed to load group details' });
    this.services.router.navigate(['/dashboard/parent/groups']);
  }
}