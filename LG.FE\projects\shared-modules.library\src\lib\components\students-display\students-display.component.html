@if (studentsData().length > 0) {
  <!-- Avatar Group Layout -->
  @if (mergedConfig().layout === 'avatar-group') {
    <p-avatarGroup styleClass="students-avatar-group">
      @for (student of visibleStudents(); track trackByStudentId) {
        <p-avatar 
          [image]="student.profilePhotoUrl" 
          [label]="getStudentInitials(student)" 
          shape="circle" 
          [size]="mergedConfig().size === 'normal' ? 'normal' : mergedConfig().size === 'large' ? 'xlarge' : 'normal'"
          [pTooltip]="getStudentFullName(student) + (mergedConfig().showAge && getStudentAge(student) ? ' (' + getStudentAge(student) + ' years)' : '')"
          tooltipPosition="top">
        </p-avatar>
      }
      @if (showMoreIndicator()) {
        <p-avatar 
          [label]="'+' + remainingCount()" 
          shape="circle" 
          [size]="mergedConfig().size === 'normal' ? 'normal' : mergedConfig().size === 'large' ? 'xlarge' : 'normal'"
          styleClass="bg-primary"
          [pTooltip]="getRemainingStudentsTooltip()"
          tooltipPosition="top">
        </p-avatar>
      }
    </p-avatarGroup>
  }
  <!-- Other Layouts -->
  @else {
    <div [class]="containerClasses()">
      @for (student of visibleStudents(); track trackByStudentId) {
        <div [class]="itemClasses()">
          <!-- Student Image -->
          @if (mergedConfig().showImages) {
            <lib-prime-profile-photo-single 
              [userId]="student.userId"
              [src]="student.profilePhotoUrl!"
              [width]="imageSize()"
              [height]="imageSize()"
              customClass="student-photo">
            </lib-prime-profile-photo-single>
          }
          
          <!-- Student Info -->
          <div class="student-info">
            <!-- Student Name -->
            @if (mergedConfig().showNames) {
              @if (mergedConfig().layout === 'compact') {
                <p-tag 
                  [value]="getStudentFullName(student)" 
                  severity="info" 
                  [rounded]="true"
                  styleClass="text-xs">
                </p-tag>
              } @else {
                <div class="student-name font-medium">
                  {{ getStudentFullName(student) }}
                  @if (mergedConfig().showAge && getStudentAge(student)) {
                    <span class="student-age text-500 font-normal text-xs ml-1">({{ getStudentAge(student) }} years)</span>
                  }
                </div>
              }
            }
            
            <!-- User Languages (only for extended types like ISearchStudentDto) -->
            @if (mergedConfig().showLanguages && hasLanguageInfo(student)) {
              <div class="student-languages flex gap-1 mt-1">
                @for (language of getVisibleLanguages(student); track $index) {
                  <span class="language-tag text-xs px-2 py-1 border-round-sm bg-primary-50 text-primary-700">
                    {{ language.teachingLanguageName }}
                  </span>
                }
                @if (shouldTruncateLanguages(student)) {
                  <span class="language-tag text-xs px-2 py-1 border-round-sm bg-gray-50 text-gray-700"
                    [pTooltip]="getRemainingLanguagesTooltip(student)">
                    +{{ getRemainingLanguagesCount(student) }}
                  </span>
                }
              </div>
            }
          </div>
        </div>
      }
      
      <!-- More Indicator -->
      @if (showMoreIndicator()) {
        @if (mergedConfig().layout === 'compact') {
          <p-tag 
            [value]="'+' + remainingCount() + ' more'" 
            severity="secondary" 
            [rounded]="true"
            styleClass="text-xs"
            [pTooltip]="getRemainingStudentsTooltip()"
            tooltipPosition="top">
          </p-tag>
        } @else {
          <div class="more-indicator flex align-items-center" 
            [pTooltip]="getRemainingStudentsTooltip()"
            tooltipPosition="top">
            <span class="text-500 text-sm">+{{ remainingCount() }} more</span>
          </div>
        }
      }
    </div>
  }
} @else {
  <!-- Empty State -->
  <div class="empty-state text-500 text-sm">
    No students available
  </div>
}
