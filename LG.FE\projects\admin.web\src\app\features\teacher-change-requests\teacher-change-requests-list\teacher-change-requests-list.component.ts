// ============================================================================
// TEACHER CHANGE REQUESTS LIST COMPONENT
// ============================================================================

// === ANGULAR IMPORTS ===
import {
  Component,
  OnInit,
  OnDestroy,
  AfterViewInit,
  inject,
  signal,
  computed,
  ChangeDetectionStrategy,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { Params } from '@angular/router';

// === PRIMENG IMPORTS ===
import { TableModule, Table } from 'primeng/table';
import { MultiSelectModule } from 'primeng/multiselect';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { TooltipModule } from 'primeng/tooltip';
import { TagModule } from 'primeng/tag';
import { RippleModule } from 'primeng/ripple';

// === SHARED LIBRARY IMPORTS ===
import {
  IGetTeacherChangeRequestRequest,
  IGetTeacherChangeRequestResponse,
  ISearchTeacherChangeRequestDto,
  ITeacherChangeRequestStatusEnum,
  EnumDropdownOptionsService,
  GeneralService,
  IDataGridFields,
  IEnumDropdownOptions,
  FiltersDrawerSidebarComponent,
  IFiltersDrawerConfig,
  IFiltersDrawerActionEvent,
  AppliedFiltersTagsComponent,
  IFilterActionEvent,
  IAppliedFilterTag,
  ITeacherChangeRequest,
  IDataGridResponse,
  IBasedDataGridResponse,
} from 'SharedModules.Library';

// === COMPONENT IMPORTS ===
import { DataGridHeaderFooterComponent } from '../../../shared/components/data-grid-header-footer/data-grid-header-footer.component';

import {
  ITeacherChangeRequestsFilterChangeEvent,
  ITeacherChangeRequestsFilterState,
  ITeacherChangeRequestsFilterConfig,
  TeacherChangeRequestsListFiltersComponent,
} from './teacher-change-requests-list-filters/teacher-change-requests-list-filters.component';

// === SERVICE IMPORTS ===
import { TeacherChangeRequestsListComponentHelperService } from './teacher-change-requests-list-component-helper.service';

// === BASE COMPONENT ===
import { BaseDataGridComponent, IBaseDataGridConfig } from 'SharedModules.Library';

// ============================================================================
// COMPONENT DEFINITION
// ============================================================================

/**
 * Teacher Change Requests List Component
 *
 * Displays teacher change requests in a data grid with:
 * - Pagination and sorting
 * - Status-based filtering
 * - Applied filters display
 * - Search functionality
 * - Export capabilities
 *
 * Extends BaseDataGridComponent for common data grid functionality
 */
@Component({
  selector: 'app-teacher-change-requests-list',
  standalone: true,
  imports: [
    CommonModule,
    TableModule,
    MultiSelectModule,
    ButtonModule,
    FormsModule,
    InputTextModule,
    TooltipModule,
    TagModule,
    RippleModule,
    DataGridHeaderFooterComponent,
    AppliedFiltersTagsComponent,
    TeacherChangeRequestsListFiltersComponent,
    FiltersDrawerSidebarComponent,
  ],
  templateUrl: './teacher-change-requests-list.component.html',
  styleUrls: ['./teacher-change-requests-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TeacherChangeRequestsListComponent
  extends BaseDataGridComponent<
    IGetTeacherChangeRequestRequest,
    IGetTeacherChangeRequestResponse
  >
  implements OnInit, OnDestroy, AfterViewInit {
  private teacherChangeRequestsHelperService = inject(TeacherChangeRequestsListComponentHelperService);
  enumDropdownOptionsService = inject(EnumDropdownOptionsService);
  protected override generalService = inject(GeneralService);
  protected override router = inject(Router);

  // ============================================================================
  // COMPONENT PROPERTIES
  // ============================================================================

  @ViewChild('table') override table!: Table;
  @ViewChild('teacherChangeRequestsFilters') teacherChangeRequestsFiltersComponent?: TeacherChangeRequestsListFiltersComponent;

  // Status options for filtering
  statusOptions = signal<IEnumDropdownOptions[]>([]);

  // Table columns - initialized from helper service like groups list
  override cols: IDataGridFields[] = [];

  // ============================================================================
  // COMPUTED PROPERTIES
  // ============================================================================

  /**
   * Get search term from query params
   */
  searchTerm = computed(() => this.queryParams().searchTerm || '');

  // Available columns for the data grid - computed from initialized columns
  availableColumns = computed<IDataGridFields[]>(() => this.cols);


  // ============================================================================
  // CONFIGURATION
  // ============================================================================

  getConfig(): IBaseDataGridConfig<IGetTeacherChangeRequestRequest> {
    return {
      defaultRequest: this.teacherChangeRequestsHelperService.createDefaultTeacherChangeRequestsRequest(),
      apiEndpoint: ITeacherChangeRequest.getChangeRequests,
      errorPrefix: 'Failed to load teacher change requests',
      mapUrlParamsToRequest: (params: Params) =>
        this.teacherChangeRequestsHelperService.mapQueryParamsToTeacherChangeRequestsRequest(params),
      createDefaultRequest: () =>
        this.teacherChangeRequestsHelperService.createDefaultTeacherChangeRequestsRequest(),
      appliedFiltersConfig: {
        convertToFilterTags: (
          request: IGetTeacherChangeRequestRequest,
          _urlParams: Record<string, string>
        ) => {
          // For now, return empty array - we'll implement this later
          const filters: IAppliedFilterTag[] = [];

          // Add status filter if present
          if (request.status !== null && request.status !== undefined) {
            filters.push({
              id: 'status',
              label: `Status: ${this.getStatusLabel(request.status)}`,
              icon: 'pi pi-tag',
              type: 'custom',
              removeData: { filterName: 'status' }
            });
          }

          // Add sort filter if different from default
          if (request.sortColumn && request.sortColumn !== this.teacherChangeRequestsHelperService.getDefaultSortColumn()) {
            const column = this.availableColumns().find(col => col.field === request.sortColumn);
            const displayName = column?.header || request.sortColumn;
            filters.push({
              id: 'sort',
              label: `Sort: ${displayName} (${request.sortDirection === 'asc' ? 'Ascending' : 'Descending'})`,
              icon: 'pi pi-sort-alt',
              type: 'sort',
              removeData: { filterName: 'sortColumn' }
            });
          }

          return filters;
        },
      },
    };
  }

  // ============================================================================
  // LIFECYCLE METHODS
  // ============================================================================

  override ngOnInit(): void {
    // Initialize table columns from helper service like groups list
    this.cols = this.teacherChangeRequestsHelperService.initializeTableColumns();

    // Filter out hidden columns for initial selection
    const visibleColumns = this.cols.filter((col) => !col.hide);
    this.selectedColumns.set(visibleColumns);

    this.initializeStatusOptions();
    super.ngOnInit();
  }

  ngAfterViewInit(): void {
    // No super call needed as BaseDataGridComponent doesn't implement AfterViewInit
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
  }

  // ============================================================================
  // INITIALIZATION METHODS
  // ============================================================================

  private initializeStatusOptions(): void {
    this.statusOptions.set(this.enumDropdownOptionsService.teacherChangeRequestStatusEnumOptions);
  }

  // ============================================================================
  // FILTER HANDLING
  // ============================================================================

  override onFilterChange(event: ITeacherChangeRequestsFilterChangeEvent): void {
    console.debug('Filter change detected:', event.filterName, event.value);
  }

  override onFilterAction(
    event: IFilterActionEvent<IGetTeacherChangeRequestRequest>
  ): void {
    if (event.action === 'search') {
      this.saveScrollPosition();
      this.queryParams.set(event.filters);
      this.generalService.updateQueryParams(this.queryParams(), {
        replaceUrl: true,
      });
      this.restoreScrollPosition();
    } else if (event.action === 'reset') {
      this.resetFilters();
    }
  }

  // ============================================================================
  // FILTERS DRAWER CONFIGURATION
  // ============================================================================

  filtersDrawerConfig: IFiltersDrawerConfig = {
    headerText: 'Teacher Change Requests Filters',
    headerIcon: 'pi pi-filter',
    position: 'right',
    width: '450px',
    showApplyButton: true,
    showResetButton: true,
    applyButtonLabel: 'Apply Filters',
    resetButtonLabel: 'Reset All',
  };

  /**
   * Handles filters drawer action events - with temporary filter support
   */
  override onFiltersDrawerAction(event: IFiltersDrawerActionEvent): void {
    if (event.action === 'apply') {
      this.teacherChangeRequestsFiltersComponent?.emitSearchAction();
      this.closeFiltersDrawer();
    } else if (event.action === 'reset') {
      // Reset only clears temporary filter state, doesn't apply the reset
      // The reset will only be applied when user clicks "Apply Filters"
      this.teacherChangeRequestsFiltersComponent?.resetFiltersToDefault();
      // Do NOT close the drawer - user should be able to see the reset state
      // and decide whether to apply it or make further changes
    }
  }

  onFiltersDrawerVisibilityChanged(isVisible: boolean): void {
    console.log('Filters drawer visibility changed:', isVisible);
  }

  trackByField(_index: number, item: IDataGridFields): string {
    return item.field;
  }

  exportTable(): void {
    const response = this.dataResponse();
    if (response)
      this.teacherChangeRequestsHelperService.exportTeacherChangeRequestsTable(
        this.table,
        this.selectedColumns(),
        response
      );
  }

  // ============================================================================
  // DISPLAY HELPERS
  // ============================================================================

  formatUtcDateToAdminLocalized(date: string | Date): string {
    return date
      ? new Date(date).toLocaleDateString('en-GB', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
        })
      : '';
  }

  getFullName(firstName?: string, lastName?: string): string {
    if (!firstName && !lastName) return '';
    return `${firstName || ''} ${lastName || ''}`.trim();
  }

  getStatusSeverity(status: ITeacherChangeRequestStatusEnum): 'success' | 'info' | 'warn' | 'danger' {
    switch (status) {
      case ITeacherChangeRequestStatusEnum.Approved:
        return 'success';
      case ITeacherChangeRequestStatusEnum.Pending:
        return 'info';
      case ITeacherChangeRequestStatusEnum.Rejected:
        return 'danger';
      case ITeacherChangeRequestStatusEnum.Cancelled:
        return 'warn';
      default:
        return 'info';
    }
  }

  getStatusLabel(status: ITeacherChangeRequestStatusEnum): string {
    return this.enumDropdownOptionsService.teacherChangeRequestStatusEnumOptions
      .find(option => option.value === status)?.label || 'Unknown';
  }

  // ============================================================================
  // ADDITIONAL COMPONENT METHODS
  // ============================================================================

  /**
   * Track by function for table rows
   */
  trackByRequestId(_index: number, item: ISearchTeacherChangeRequestDto): string {
    return item.requestId;
  }

  // ============================================================================
  // FILTERS STATE MANAGEMENT
  // ============================================================================

  /**
   * Computed property for filters state
   */
  filtersState = computed<ITeacherChangeRequestsFilterState>(() => ({
    queryParams: this.queryParams(),
    isFilterOpen: this.isFiltersDrawerVisible()
  }));

  /**
   * Computed property for filters configuration
   */
  filtersConfig = computed<ITeacherChangeRequestsFilterConfig>(() => ({
    enableAutoSearch: false,
    searchDebounceMs: 300
  }));

  // ============================================================================
  // MISSING METHODS FROM TEMPLATE
  // ============================================================================

  /**
   * Handle filters button click
   */
  onFiltersButtonClick(): void {
    this.openFiltersDrawer();
  }

  /**
   * Handle search input changes
   */
  override onSearchChange(searchTerm: string): void {
    const currentParams = { ...this.queryParams() };
    currentParams.searchTerm = searchTerm || null;
    currentParams.pageNumber = 1; // Reset to first page when searching

    this.queryParams.set(currentParams);
    this.updateUrlParams();
  }

  /**
   * Handle search input event from template
   */
  onSearchInputChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.onSearchChange(target.value);
  }

  /**
   * Handle column selection changes
   */
  override onColumnsChange(selectedColumns: IDataGridFields[]): void {
    this.selectedColumns.set(selectedColumns);
  }

  /**
   * Handle column reorder event
   */
  onColumnReorder(event: { columns?: IDataGridFields[] }): void {
    if (event.columns?.length) {
      const reorderedColumns = event.columns.filter(
        (col: IDataGridFields) => col?.field && col?.header
      ) as IDataGridFields[];
      this.selectedColumns.set([...reorderedColumns]);
    }
  }

  /**
   * Update search term from input event
   */
  override updateSearchTerm(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.onSearchChange(target.value);
  }

  /**
   * Clear search term
   */
  override clearSearchTerm(): void {
    this.onSearchChange('');
  }

  /**
   * Override applied filter remove handler
   */
  override onAppliedFilterRemove(event: { filter: IAppliedFilterTag; event?: MouseEvent }): void {
    const filterName = event.filter.removeData?.filterName;
    if (filterName) {
      const currentParams = { ...this.queryParams() };

      // Remove the specific filter
      if (filterName === 'status') {
        currentParams.status = null;
      } else if (filterName === 'sortColumn') {
        currentParams.sortColumn = this.teacherChangeRequestsHelperService.getDefaultSortColumn();
        currentParams.sortDirection = this.teacherChangeRequestsHelperService.getDefaultSortDirection();
      }

      this.queryParams.set(currentParams);
      this.updateUrlParams();
    }
  }

  /**
   * Override applied filters clear all handler
   */
  override onAppliedFiltersClearAll(_event: MouseEvent): void {
    const defaultRequest = this.teacherChangeRequestsHelperService.createDefaultTeacherChangeRequestsRequest() as IGetTeacherChangeRequestRequest;
    this.queryParams.set(defaultRequest);
    this.updateUrlParams();
  }
}
