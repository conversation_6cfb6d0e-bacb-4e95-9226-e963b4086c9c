import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, input, <PERSON><PERSON><PERSON><PERSON>, type OnInit, ViewChild } from '@angular/core';
import { MenuItem } from 'primeng/api';
import { AvatarModule } from 'primeng/avatar';
import { AvatarGroupModule } from 'primeng/avatargroup';
import { ButtonModule } from 'primeng/button';

import { TooltipModule } from 'primeng/tooltip';
import { Popover, PopoverModule } from 'primeng/popover';
import { PrimeProfilePhotoSingleComponent } from '../prime/prime-profile-photo-single/prime-profile-photo-single.component';
import { IStudentGroupDto } from '../../GeneratedTsFiles';
import { MenuModule } from 'primeng/menu';
import { GeneralService } from '../../services/general.service';

@Component({
  selector: 'app-student-group-row-display',
  imports: [
    CommonModule,
    AvatarModule,
    ButtonModule,
    MenuModule,
    TooltipModule,
    PopoverModule,
    AvatarGroupModule,
    PrimeProfilePhotoSingleComponent,
  ],
  templateUrl: './student-group-row-display.component.html',
  styleUrl: './student-group-row-display.component.scss',
})
export class StudentGroupRowDisplayComponent implements OnInit, OnDestroy {
  @ViewChild('op') op!: Popover;
  studentCurrentTime: { [key: string]: string } = {};
  private intervals: { [key: string]: any } = {};

  generalService = inject(GeneralService);
  studentGroupItem = input({} as any);
  studentGroupItemAttribute = input('basicProfileInfoDto');
  topRowClass = input('flex flex-row align-items-center justify-content-start');

  items: MenuItem[] = [];
  randomColors: { [studentId: string]: string } = {};

  private colorMapping: { [key: string]: string } = {
    'A': '#FF5733',
    'B': '#33FF57',
    'C': '#3357FF',
    'D': '#F1C40F',
    'E': '#8E44AD',
    'F': '#E67E22',
    'G': '#2ECC71',
    'H': '#3498DB',
    'I': '#9B59B6',
    'J': '#F39C12',
    'K': '#D35400',
    'L': '#C0392B',
    'M': '#7D3C98',
    'N': '#1ABC9C',
    'O': '#2C3E50',
    'P': '#2980B9',
    'Q': '#8E44AD',
    'R': '#E74C3C',
    'S': '#F1C40F',
    'T': '#34495E',
    'U': '#2ECC71',
    'V': '#E67E22',
    'W': '#D35400',
    'X': '#C0392B',
    'Y': '#7D3C98',
    'Z': '#3498DB',
  };
  

  ngOnInit(): void {
    this.generateColors();
  }

  ngOnDestroy() {
    // Clear all intervals when the component is destroyed
    Object.values(this.intervals).forEach(clearInterval);
  }


  get students() {
    let v;
    if (this.studentGroupItemAttribute()) {
      v = (this.studentGroupItem()[(this.studentGroupItemAttribute()) as string] as IStudentGroupDto[]);
    } else {
      v = this.studentGroupItem();
    }
    return v;
  }
  private generateColors() {
    let v;
    if (this.studentGroupItemAttribute()) {
      v = this.studentGroupItem()[(this.studentGroupItemAttribute()) as string] as IStudentGroupDto[];
    } else {
      v = this.studentGroupItem();
    }
  
    console.log(v);
    this.students.forEach((student: any) => {
      const firstLetter = student.firstName.charAt(0).toUpperCase();
      this.randomColors[student.id] = this.colorMapping[firstLetter] || this.generateRandomColor();
    });
  }
  

  private generateRandomColor(): string {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
      color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
  }

  getContrastingColor(hex: string): string {
    // Convert hex to RGB
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);

    // Calculate brightness
    const brightness = (r * 299 + g * 587 + b * 114) / 255000;
    return brightness > 0.5 ? '#000000' : '#FFFFFF'; // Return black or white
  }

  onDivClick(event: any) {
    event.preventDefault();
    event.stopPropagation();
  }

  toggle(event: any) {
    this.op.toggle(event);
  }

  startUpdatingTime(timezone: string, student: any) {
    this.updateTime(timezone); // Initial call to set the time immediately

    this.intervals[student.firstName] = setInterval(() => {
      this.updateTime(timezone);
    }, 1000); // Update every second
  }

  stopUpdatingTime(student: any) {
    clearInterval(this.intervals[student.firstName]);
    delete this.intervals[student.firstName];
  }

  private updateTime(timezone: string) {
    this.studentCurrentTime[timezone] = this.generalService.getLocalTime(timezone);
  }
}
