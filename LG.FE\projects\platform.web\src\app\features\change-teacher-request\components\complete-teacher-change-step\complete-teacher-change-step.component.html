<div class="complete-teacher-change-step">
  <!-- Step Header -->
  <div class="step-header mb-3">
    <p class="text-600 text-sm line-height-3">
      Select a new teacher and provide the reason for the change. This will affect all your lessons and sessions.
    </p>
  </div>

  <!-- Teacher Selection Content -->
  <div class="teacher-change-content">

    <!-- Teacher Selection Section -->
    <div class="form-section mb-3">

      @if (isLoading()) {
        <!-- Loading State -->
        <p-card class="teacher-selection-card shadow-1">
          <div class="p-3">
            <div class="flex align-items-center gap-2 mb-3">
              <p-skeleton width="1.25rem" height="1.25rem" class="border-circle"></p-skeleton>
              <p-skeleton width="8rem" height="1.25rem"></p-skeleton>
            </div>
            <p-skeleton width="100%" height="0.875rem" class="mb-2"></p-skeleton>
            <p-skeleton width="75%" height="0.875rem" class="mb-3"></p-skeleton>

            @for (item of [1,2,3]; track $index) {
              <div class="flex align-items-center gap-2 mb-2 p-2 border-1 surface-border border-round">
                <p-skeleton width="0.875rem" height="0.875rem" class="border-circle"></p-skeleton>
                <p-skeleton width="2.5rem" height="2.5rem" class="border-circle"></p-skeleton>
                <div class="flex-1">
                  <p-skeleton width="6rem" height="1rem" class="mb-1"></p-skeleton>
                  <p-skeleton width="8rem" height="0.75rem" class="mb-1"></p-skeleton>
                  <p-skeleton width="7rem" height="0.75rem"></p-skeleton>
                </div>
              </div>
            }
          </div>
        </p-card>
      } @else if (teacherOptions().length > 0) {
        <!-- Teacher Options -->
        <p-card class="teacher-selection-card shadow-1">
          <div class="teacher-selection-content">
            <div class="flex align-items-center gap-2 mb-3">
              <i class="pi pi-users text-primary"></i>
              <h5 class="text-900 font-semibold m-0 text-base">Available Teachers</h5>
            </div>

            @for (teacher of teacherOptions(); track teacher.userId) {
              <div class="teacher-option mb-2 p-2 border-1 surface-border border-round hover:surface-hover cursor-pointer transition-all transition-duration-200"
                   [class.surface-100]="selectedTeacherId() === teacher.userId"
                   [class.border-primary]="selectedTeacherId() === teacher.userId"
                   [class.shadow-1]="selectedTeacherId() === teacher.userId"
                   (click)="onTeacherRadioChange(teacher.userId)">
                <div class="flex align-items-center gap-2">
                  <p-radioButton
                  class="hidden"
                    [value]="teacher.userId"
                    [ngModel]="selectedTeacherId()"
                    (ngModelChange)="onTeacherRadioChange($event)"
                    [ngModelOptions]="{standalone: true}">
                  </p-radioButton>

                  <!-- Teacher Profile Photo -->
                  <div class="flex-shrink-0">
                    <lib-prime-profile-photo-single
                      [src]="teacher.profilePhotoUrl"
                      [width]="48"
                      [height]="48"
                      class="border-circle">
                    </lib-prime-profile-photo-single>
                  </div>

                  <!-- Teacher Information -->
                  <div class="teacher-info flex-1 min-w-0">
                    <div class="flex align-items-center justify-content-between gap-2 mb-1">
                      <h6 class="text-900 font-semibold m-0 text-base">
                        {{ teacher.firstName }} {{ teacher.lastName }}
                      </h6>
                      @if (selectedTeacherId() === teacher.userId) {
                        <p-tag
                          value="Selected"
                          severity="success"
                          icon="pi pi-check"
                          class="text-xs p-tag-sm">
                        </p-tag>
                      }
                    </div>

                    <div class="flex flex-column gap-1">
                      <!-- <div class="flex align-items-center gap-2 text-sm text-600">
                        <i class="pi pi-user text-xs"></i>
                        @if (teacher.discriminator) {
                          <p-tag
                            [value]="teacher.discriminator"
                            severity="info"
                            class="text-xs">
                          </p-tag>
                        }
                      </div> -->

                    @if (teacher.timeZoneDisplayName) {
                      <div class="flex align-items-center gap-1 text-xs text-600">
                        <i class="pi pi-clock" style="font-size: 0.625rem;"></i>
                        <span>{{ teacher.timeZoneDisplayName }}</span>
                      </div>
                    }
                    </div>
                  </div>
                </div>
              </div>
            }

            @if (selectedTeacherId()) {
              <div class="mt-3 flex justify-content-center">
                <p-button
                  label="Clear Selection"
                  icon="pi pi-times"
                  [outlined]="true"
                  severity="secondary"
                  size="small"
                  class="p-button-sm text-xs"
                  (click)="onTeacherCleared()">
                </p-button>
              </div>
            }
          </div>
        </p-card>
      } @else {
        <!-- No Teachers Available -->
        <p-card class="teacher-selection-card shadow-1">
          <div class="text-center p-3">
            <div class="flex justify-content-center mb-2">
              <div class="flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-orange-50 text-orange-500">
                <i class="pi pi-info-circle text-lg"></i>
              </div>
            </div>
            <h6 class="text-900 font-semibold mb-2 text-sm">No Teachers Available</h6>
            <p class="text-600 text-xs line-height-3 max-w-15rem mx-auto mb-3">
              There are currently no teachers available for change. Please contact support for assistance.
            </p>
            <p-button
              label="Contact Support"
              icon="pi pi-headphones"
              [outlined]="true"
              severity="help"
              size="small"
              class="p-button-sm text-xs">
            </p-button>
          </div>
        </p-card>
      }
    </div>

    <!-- Reason Section -->
    <div class="form-section mb-3">
      <p-card class="reason-card shadow-1">
        <div class="reason-content">
          <div class="flex align-items-center gap-2 mb-3">
            <i class="pi pi-comment text-primary"></i>
            <h5 class="text-900 font-semibold m-0 text-base">Reason for Change</h5>
          </div>

          <form [formGroup]="form">
            <div class="field">
              <label for="reason" class="block text-900 font-medium mb-2 text-sm">
                Please explain why you want to change your teacher *
              </label>
              <textarea
                id="reason"
                formControlName="reason"
                pTextarea
                rows="4"
                placeholder="Please provide a detailed reason for requesting a teacher change (minimum 10 characters)..."
                class="w-full"
                [class.ng-invalid]="form.get('reason')?.invalid && form.get('reason')?.touched"
                [class.ng-valid]="form.get('reason')?.valid">
              </textarea>

              <!-- Validation Messages -->
              @if (form.get('reason')?.invalid && form.get('reason')?.touched) {
                <div class="field-error mt-1">
                  @if (form.get('reason')?.errors?.['required']) {
                    <small class="p-error text-xs">Reason is required</small>
                  }
                  @if (form.get('reason')?.errors?.['minlength']) {
                    <small class="p-error text-xs">Reason must be at least 10 characters long</small>
                  }
                  @if (form.get('reason')?.errors?.['maxlength']) {
                    <small class="p-error text-xs">Reason cannot exceed 500 characters</small>
                  }
                </div>
              }

              <!-- Character Counter -->
              <div class="flex justify-content-end mt-1">
                <small class="text-600 text-xs">
                  {{ form.get('reason')?.value?.length || 0 }}/1000 characters
                </small>
              </div>
            </div>
          </form>
        </div>
      </p-card>
    </div>

  </div>


</div>
