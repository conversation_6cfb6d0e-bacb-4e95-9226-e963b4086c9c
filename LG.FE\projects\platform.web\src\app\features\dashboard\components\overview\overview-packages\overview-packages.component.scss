@import 'projects/shared-modules.library/src/lib/styles/mixins.scss';

:host {
  display: block;
}

// Filters button styling
.p-button.p-button-outlined {

    ::ng-deep .p-badge {
      position: absolute;
      top: -8px;
      right: -8px;
      min-width: 18px;
      height: 18px;
      line-height: 18px;
      font-size: 0.75rem;
      border-radius: 50%;
    }
}

// Applied filters section styling
.surface-card {
  border-radius: 8px;

  &.shadow-1 {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  &.shadow-2 {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
}

// Search and filters container
.surface-section {
  .flex.gap-2 {
  
  }
}

// Content area minimum height
.min-h-20rem {
  min-height: 20rem;
}

// Paginator styling
::ng-deep .p-paginator {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 8px;
  padding: 0.75rem 1rem;
}

// Package grid layout with smooth transitions
.grid {
  margin: 0;
  transition: opacity 0.2s ease-in-out;

  .col-12,
  .md\\:col-6,
  .xxl\\:col-3 {
    padding: 0.5rem;
    transition: transform 0.2s ease-in-out, opacity 0.2s ease-in-out;

    &:hover {
      transform: translateY(-2px);
    }
  }
}

// Empty state styling
.empty-state-container {
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

// Error state styling
.error-state-container {
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .error-content {
    text-align: center;
    max-width: 400px;
  }
}

// Loading skeleton adjustments with smooth animations
:host ::ng-deep lib-skeleton-loader {
  .p-skeleton {
    border-radius: 8px;
    animation: skeleton-loading 1.5s ease-in-out infinite alternate;
  }
}

// Smooth skeleton loading animation
@keyframes skeleton-loading {
  0% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

// Content loading overlay for smooth transitions
.content-loading-overlay {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(1px);
    z-index: 10;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
    pointer-events: none;
  }

  &.loading::before {
    opacity: 1;
  }
}

// Responsive adjustments
@include breakpoint(mobile) {
  .surface-section {
    .flex.flex-column.lg\\:flex-row {
      gap: 1rem;
      
      .flex-1 {
        max-width: 100%;
      }
    }
  }

  ::ng-deep .p-paginator {
    padding: 0.5rem;

    .p-paginator-pages {
      display: none;
    }
  }

  .grid {
    .col-12 {
      padding: 0.25rem;
    }
  }
}

@include breakpoint(tablet) {
  .grid {
    .md\\:col-6 {
      padding: 0.375rem;
    }
  }
}

// Mini flag styling (for language flags in package cards)
::ng-deep .mini-flag {
  width: 24px;
  height: 16px;
  object-fit: cover;
  border-radius: 2px;
  border: 1px solid var(--surface-border);
}
