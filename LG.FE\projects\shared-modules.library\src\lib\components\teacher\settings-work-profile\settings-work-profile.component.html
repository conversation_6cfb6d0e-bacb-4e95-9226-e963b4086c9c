@let chooseButtonUploadLabel = 'Choose for upload';
@let chooseButtonStyleClass = 'p-2 text-sm';
@if (formGetDataLoaded()) {
<form [formGroup]="form" (ngSubmit)="onSubmit()" class="p-fluid work_profile_form">


    <div class="surface-card p-2 shadow-0 border-1 border-gray-200 border-round mb-2">
        <div class="field mb-0 pb-0 col-12">
            <div class="font-medium text-xl text-900 mb-3"><i class="pi pi-briefcase"></i> Work Profile Information
            </div>
            <div class="text-500 mb-0">Please provide your work profile information.</div>
        </div>
        <div formGroupName="workProfile">
            <!-- Teaching Languages Section (unchanged) -->

            <div class="field mb-0 col-12" id="workProfile.studentAgesExperience">
                <label class="block font-medium mt-3 mb-2 primary-purple-color text-900 text-left text-md w-full"
                    for="postCode" [innerHTML]="generalService.getDisplayLabel('Teaching Experience', true)">
                </label>
                <span class="text-sm text-gray-500 mb-1 block">Choose students age ranges that you are experienced in
                    teaching.</span>
                <p-multiselect name="workProfile.studentAgesExperience" [options]="studentAgesOptions"
                    [formControl]="studentAgesExperienceControl" (onChange)="onStudentAgesChange($event)"
                    placeholder="Select student age ranges" styleClass="w-full" optionLabel="label" optionValue="value"
                    [selectionLimit]="5" [showToggleAll]="false">
                </p-multiselect>
                <app-form-field-validation-message messageClass="text-red-500 mb-2"
                    [control]="form.get('workProfile.studentAgesExperience')" propertyName="Student Ages Experience" />
            </div>

            <!-- Student Ages Preference -->
            <div class="field mb-0 col-12" id="workProfile.studentAgesPreference">
                <label class="block font-medium mt-3 mb-2 primary-purple-color text-900 text-left text-md w-full"
                    for="postCode" [innerHTML]="generalService.getDisplayLabel('Preferred Student Ages', true)">
                </label>
                <span class="text-sm text-gray-500 mb-1 block">Choose the age ranges of students you prefer to
                    teach.</span>
                <p-multiselect formControlName="studentAgesPreference" name="workProfile.studentAgesPreference"
                    [options]="studentAgesPreferenceOptions" (onChange)="onStudentAgesPreferenceChange($event)"
                    placeholder="Select preferred student age ranges" styleClass="w-full" optionLabel="label"
                    optionValue="value" [selectionLimit]="5" [showToggleAll]="false">
                </p-multiselect>
                <app-form-field-validation-message messageClass="text-red-500 mb-2"
                    [control]="form.get('workProfile.studentAgesPreference')" propertyName="Preferred Student Ages" />
            </div>

            <!-- Degrees File Upload -->
            <div class="field mb-0 col-12" id="workProfile.degrees">
                <label class="block font-medium mt-3 mb-2 primary-purple-color text-900 text-left text-md w-full"
                    for="postCode" [innerHTML]="generalService.getDisplayLabel('Degrees (Multiple Files)', true)">
                </label>
                <span class="text-sm text-gray-500 mb-1 block">Upload your degrees (Accepted types: .doc, .docx, .docm,
                    .dotx, .dotm, .pdf)</span>

                <p-fileupload [chooseLabel]="chooseButtonUploadLabel" [chooseStyleClass]="chooseButtonStyleClass"
                    [showCancelButton]="false" mode="advanced" [showUploadButton]="false" [multiple]="true"
                    (onSelect)="onDegreesFileSelect($event)" [auto]="false" [files]="uploadedDegreeFiles">

                    <!-- Empty State -->
                    <ng-template pTemplate="empty">
                        <div class="flex align-items-center justify-content-center p-4 text-gray-500">
                            <i class="pi pi-upload mr-2"></i>
                            <span>Drag and drop files here to upload your degrees.</span>
                        </div>
                    </ng-template>

                    <!-- Uploaded Files Content -->
                    <ng-template pTemplate="content" let-files>
                        <ng-container
                            *ngTemplateOutlet="fileCardTemplate; context: { files: uploadedDegreeFiles, removeHandler: removeDegreeFile.bind(this) }">
                        </ng-container>
                    </ng-template>
                </p-fileupload>
                <app-form-field-validation-message messageClass="text-red-500 mb-2"
                    [control]="form.get('workProfile.degrees')" propertyName="Degrees File" />
            </div>

            <!-- CV File Upload -->
            <div class="field mb-0 pb-0 col-12" id="workProfile.cv">
                <label class="block font-medium mt-3 mb-2 primary-purple-color text-900 text-left text-md w-full"
                    for="postCode" [innerHTML]="generalService.getDisplayLabel('CV', true)">
                </label>
                <span class="text-sm text-gray-500 mb-1 block">Upload your CV (Accepted types: .doc, .docx, .docm,
                    .dotx, .dotm, .pdf)</span>
                <p-fileupload [showCancelButton]="false" [chooseStyleClass]="chooseButtonStyleClass"
                    [chooseLabel]="chooseButtonUploadLabel" name="workProfile.cv" mode="advanced"
                    [showUploadButton]="false" [multiple]="false" (onSelect)="onCvFileSelect($event)" [auto]="false"
                    [files]="uploadedCvFile ? [uploadedCvFile] : []">
                    <ng-template pTemplate="empty">
                        <div>Drag and drop a file here to upload your CV.</div>
                    </ng-template>
                    <ng-template pTemplate="content" let-files>

                        <ng-container
                            *ngTemplateOutlet="fileCardTemplate; context: { files: uploadedCvFile ? [uploadedCvFile] : [], removeHandler: removeCvFile.bind(this) }">
                        </ng-container>

                    </ng-template>
                </p-fileupload>
                <app-form-field-validation-message messageClass="text-red-500 mb-2"
                    [control]="form.get('workProfile.cv')" propertyName="CV File" />
            </div>

            <!-- Cover Letter File Upload -->
            <div class="field mb-0 pb-0 col-12" id="workProfile.coverLetter">
                <label class="block font-medium mt-3 mb-2 primary-purple-color text-900 text-left text-md w-full"
                    for="postCode" [innerHTML]="generalService.getDisplayLabel('Cover Letter', true)">
                </label>
                <span class="text-sm text-gray-500 mb-1 block">Upload your Cover Letter (Accepted types: .doc, .docx,
                    .docm,
                    .dotx, .dotm, .pdf)</span>
                <p-fileupload [chooseLabel]="chooseButtonUploadLabel" [chooseStyleClass]="chooseButtonStyleClass"
                    [showCancelButton]="false" mode="advanced" [showUploadButton]="false" [multiple]="false"
                    (onSelect)="onCoverLetterFileSelect($event)" [auto]="false"
                    [files]="uploadedCoverLetterFile ? [uploadedCoverLetterFile] : []">
                    <ng-template pTemplate="empty">
                        <div>Drag and drop a file here to upload your cover letter.</div>
                    </ng-template>
                    <ng-template pTemplate="content" let-files>
                        <ng-container
                            *ngTemplateOutlet="fileCardTemplate; context: { files: uploadedCoverLetterFile ? [uploadedCoverLetterFile] : [], removeHandler: deleteUploadedCoverLetterFile.bind(this) }">
                        </ng-container>
                    </ng-template>
                </p-fileupload>
                <app-form-field-validation-message messageClass="text-red-500 mb-2"
                    [control]="form.get('workProfile.coverLetter')" propertyName="Cover Letter File" />
            </div>

            <!-- Teaching Methods -->
            <div class="field mb-0 pb-0 col-12">
                <label class="block font-medium mt-3 mb-2 primary-purple-color text-900 text-left text-md w-full"
                    for="postCode" [innerHTML]="generalService.getDisplayLabel('Teaching Methods', true)">
                </label>
                <textarea pTextarea formControlName="teachingMethods" class="w-full mb-0"
                    placeholder="Teaching Methods"></textarea>
                <app-form-field-validation-message messageClass="text-red-500 mb-2"
                    [control]="form.get('workProfile.teachingMethods')" propertyName="Teaching Methods" />
            </div>

            <!-- Years of Teaching -->
            <div class="field mb-0 pb-0 col-12">
                <label class="block font-medium mt-3 mb-2 primary-purple-color text-900 text-left text-md w-full"
                    for="country" [innerHTML]="generalService.getDisplayLabel('Years Of Teaching', true)">
                </label>
                <app-prime-reactive-form-input [showLabelAbove]="true" formControlName="yearsOfTeaching"
                    [controlPath]="'workProfile.yearsOfTeaching'" [parentForm]="form" type="number"
                    placeholder="Your years of teaching" [required]="true" data-name="yearsOfTeaching" pAutoFocus
                    [inputClass]="'w-full'" />
                <app-form-field-validation-message messageClass="text-red-500 mb-2"
                    [control]="form.get('workProfile.yearsOfTeaching')" propertyName="Years of Teaching" />
            </div>
        </div>
        <!-- end of workProfile -->

    </div>


    <div class="surface-card p-2 shadow-0 border-1 border-gray-200 border-round">
        <div class="field mb-0 pb-0 col-12">
            <div class="font-medium text-xl text-900 mb-3"><i class="pi pi-credit-card"></i> Payments Information</div>
            <!-- <div class="text-500 mb-0">At least one payment method is required.</div> -->
        </div>
        <div formGroupName="payments">

            <div class="field mb-0 pb-0 col-12">
                <div class="grid ">
                    <div class="col-12 sm:col-6">
                        <app-prime-reactive-form-input label="Stripe Account" [controlPath]="'payments.stripeAccount'"
                            [showLabelAbove]="true" formControlName="stripeAccount" [parentForm]="form" type="text"
                            placeholder="stripeAccount" [required]="false" pAutoFocus [inputClass]="'w-full'">
                        </app-prime-reactive-form-input>
                    </div>
                </div>
            </div>
            <div class="col-12" *ngIf="form.get('payments')?.touched">

                <app-form-field-validation-message messageClass="text-red-500 mb-2"
                    [control]="form.get('workProfile.stripeAccount')" propertyName="stripeAccount" />
            </div>

        </div>
    </div>

    <div id="invalid-controls">
        <app-form-field-validation-message *ngIf="invalidFields.length > 0" [severity]="Severity.Warning"
            styleClass="mb-2" messageClass="mb-2"
            [text]="'Please complete all mandatory fields in the form.'"></app-form-field-validation-message>

        <div *ngIf="invalidFields.length > 0" class="invalid-fields-message">
            <p>The following fields are invalid:</p>
            <ul class="mt-1 list-none p-0 mx-0">
                <li *ngFor="let field of invalidFields" class="flex align-items-center py-1">
                    <span class="border-round bg-red-500 mr-3 flex-shrink-0"
                        style="width: 0.725rem; height: 0.725rem;"></span>
                    <span class="text-sm font-medium text-90">{{ field }}</span>
                </li>
            </ul>
            <hr>
        </div>
    </div>
    <div class="flex align-items-center justify-content-center w-full mt-2">
        <p-button [disableWhileRequest]="submitRequest$" type="submit" label="Save" class="w-full mt-3"
            styleClass="w-full"></p-button>
    </div>
</form>



} @else {

<lib-skeleton-loader shape="circle" layout="form" width="w-full" height="5rem" [fieldsCount]="5"
    styleClass="mr-2 w-full sm:w-full"></lib-skeleton-loader>

}



@if (formPostDataIsUploading()) {
<div class="flex flex-column gap-3 mt-2 p-1 sm:p-3 max-w-md mx-auto animate-fade-in">
    <!-- Icon and Message -->
    <div class="flex align-items-center gap-3">
        <i class="pi pi-cloud-upload text-blue-600 text-4xl animate-bounce-slow"></i>
        <span class="text-xl font-bold text-gray-800 tracking-tight">
            Uploading Your Data...
        </span>
    </div>

    <!-- Progress Bar -->
    @if (progressUploadValue() > 0) {
    <div class="relative">
        <p-progressbar [value]="progressUploadValue()" [showValue]="false" mode="determinate"
            [style]="{ 'height': '10px', 'border-radius': '999px' }" styleClass="bg-gray-200 overflow-hidden">
            <div class="absolute inset-0 bg-gradient-to-r from-blue-500 to-teal-500 h-full transition-all duration-300"
                [style.width]="progressUploadValue() + '%'" style="border-radius: 999px;"></div>
        </p-progressbar>
        <div class="flex justify-content-between mt-2 text-sm font-medium text-gray-600">
            <span>{{ progressUploadValue() }}% Complete</span>
            <span class="text-blue-600 font-semibold">In Progress</span>
        </div>
    </div>
    }

    <!-- Additional Info -->
    <div class="text-sm text-gray-500 italic text-center">
        Hang tight, we’re getting your data ready!
    </div>

    <!-- Optional Cancel Button -->

</div>
}

<!-- Reusable File Card Template -->
<ng-template #fileCardTemplate let-files="files" let-removeHandler="removeHandler">
    <div class="grid">
        @for (file of files; track file; let i = $index) {
        <div class="col-12 xl:col-12 xxl:col-6">
            <div class="surface-card p-3 shadow-2 border-round mb-3 flex flex-column">
                <!-- File Info -->
                <div class="flex align-items-center justify-content-between mb-2">
                    <div class="flex align-items-center">

                        <i [ngClass]="generalService.getFileIconAndColor(file.type).icon"
                            [style.color]="generalService.getFileIconAndColor(file.type).color" class="mr-1"
                            [pTooltip]="file.type" tooltipPosition="top"></i>
                        <span class="font-medium text-900 text-sm file-name">{{ file.name }}</span>
                    </div>
                    <div class="flex align-items-center gap-2">
                        <p-button icon="pi pi-trash" severity="danger" size="small"
                            styleClass="p-button-rounded p-button-text" (click)="removeHandler(file, i)">
                        </p-button>
                        <p-button icon="pi pi-download" severity="info" size="small"
                            styleClass="p-button-rounded p-button-text" (click)="download(file, i)">
                        </p-button>
                    </div>

                </div>
                <!-- File Size -->
                <div class="text-600 text-sm">
                    Size: {{ file.size * 0.000001 | number: '1.0-2' }} MB
                </div>
            </div>
        </div>
        }
        @if (!files.length) {
        <div class="col-12">
            <p class="text-center text-500 m-0">No files uploaded yet.</p>
        </div>
        }
    </div>
</ng-template>