.teacher-change-review-step {
  // Modern compact accordion styles
  .teacher-change-accordion {
    
      .p-accordionpanel:not(.p-disabled).p-accordionpanel-active > .p-accordionheader:hover {
        background: transparent;
      }
    .p-accordion-tab {
      margin-bottom: 1rem;

      .p-accordion-header {
        border-radius: 0.5rem;
        border: 1px solid var(--surface-border);

        .p-accordion-header-link {
          padding: 0.75rem 1rem;
          border-radius: 0.5rem;

          &:focus {
            box-shadow: 0 0 0 0.2rem var(--primary-200);
          }
        }
      }

      .p-accordion-content {
        border: 1px solid var(--surface-border);
        border-top: none;
        border-radius: 0 0 0.5rem 0.5rem;
        padding: 0;
      }


      &.p-accordion-tab-active {
        .p-accordion-header {
          border-radius: 0.5rem 0.5rem 0 0;
        }
      }
    }
  }

  // Selected teacher summary card
  .selected-teacher-summary {
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  // Impact summary stats
  .impact-summary {
    .border-round {
      transition: all 0.2s ease;

      &:hover {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }
  }

  // Package items
  .package-item {
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--surface-50);
    }
  }

  // Student cards
  .student-card {
    transition: all 0.2s ease;

    &:hover {
      background: var(--surface-hover);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }

  // Lesson items
  .lesson-item {
    transition: all 0.2s ease;

    &:hover {
      background: var(--surface-hover);
    }
  }

  // Error content
  .error-content {
    .pi-exclamation-triangle {
      color: var(--orange-500);
    }

    h5 {
      color: var(--text-color);
    }

    p {
      color: var(--text-color-secondary);
    }
  }

  // Loading content
  .loading-content {
    .p-skeleton {
      border-radius: var(--border-radius);
    }
  }
}

// Responsive adjustments
@media screen and (max-width: 768px) {
  .teacher-change-review-step {
    .selected-teacher-summary,
    .selection-summary,
    .reason-summary {
      padding: 0.75rem !important;
    }

    .impact-summary {
      .grid {
        .col-4 {
          padding: 0.25rem;
        }
      }
    }
  }
}