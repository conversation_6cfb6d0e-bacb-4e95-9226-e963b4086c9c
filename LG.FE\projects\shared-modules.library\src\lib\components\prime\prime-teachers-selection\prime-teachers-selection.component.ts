import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  inject,
  Injector,
  Input,
  input,
  model,
  Output,
  signal,
  ViewChild,
  type OnInit,
  computed,
  DestroyRef,
  AfterViewInit,
  OnDestroy,
  WritableSignal,
  Signal
} from '@angular/core';
import { toObservable, takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { MultiSelect, MultiSelectModule } from 'primeng/multiselect';
import { Select, SelectModule } from 'primeng/select';
import { TooltipModule } from 'primeng/tooltip';
import { InputTextModule } from 'primeng/inputtext';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { ISearchTeacherDto, ITeacherTeachingLanguageDto, IGetTeachersRequest, IGetTeachersResponse, IGenderEnum } from '../../../GeneratedTsFiles';
import { PrimeProfilePhotoSingleComponent } from '../prime-profile-photo-single/prime-profile-photo-single.component';
import { untilDestroyed } from '../../../helpers/until-destroyed';
import { DataApiStateService, State } from '../../../services/data-api-state.service';
import { EventBusService, Events, EmitEvent, DefaultGetTeachersRequest } from '../../../services/event-bus.service';
import { AuthStateService } from '../../../services/auth-state.service';
import { IUserRole, nameOf } from '../../../models/general.model';
import { GeneralService } from '../../../services/general.service';
import { BaseDropdownComponent, DropdownItem } from '../../base/base-dropdown.component';
import { Observable } from 'rxjs';

@Component({
  selector: 'lib-prime-teachers-selection',
  imports: [
    CommonModule,
    FormsModule,
    SelectModule,
    ButtonModule,
    TooltipModule,
    MultiSelectModule,
    PrimeProfilePhotoSingleComponent,
    InputTextModule,
    IconFieldModule,
    InputIconModule,
  ],
  templateUrl: './prime-teachers-selection.component.html',
  styleUrl: './prime-teachers-selection.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PrimeTeachersSelectionComponent
  extends BaseDropdownComponent<ISearchTeacherDto, IGetTeachersRequest, IGetTeachersResponse>
  implements AfterViewInit {

  // ===========================
  // VIEW REFERENCES
  // ===========================

  @ViewChild('selectTeachersList') select: Select | undefined;
  @ViewChild('selectTeachersListMultiple') selectMultiple: MultiSelect | undefined;

  // ===========================
  // INJECTED SERVICES
  // ===========================

  readonly generalService = inject(GeneralService);
  private readonly dataStateService = inject(DataApiStateService);
  private readonly authService = inject(AuthStateService);
  private readonly untilDestroyed = untilDestroyed();
  protected override readonly injector = inject(Injector);

  // ===========================
  // BACKWARD COMPATIBILITY INPUTS
  // ===========================

  /** Legacy properties maintained for backward compatibility */
  override selectedItem = signal<ISearchTeacherDto | null>(null);
  @Input() override emptyMessageText = 'No teachers found.';

  /** Legacy items input - enhanced with pagination support */
  items = input<ISearchTeacherDto[]>([]);

  // ===========================
  // INPUT SIGNALS
  // ===========================

  /** Style class for the component */
  styleClass = input('w-full full-width mb-2');
  selectedItemProperty = input('');
  baseProperty = input('');
  textForNameProperty = input('');
  resetSelectionSignal = input(false);

  // ===========================
  // OUTPUTS
  // ===========================

  /** Emitted when a teacher or teachers are selected */
  @Output() itemClicked = new EventEmitter<ISearchTeacherDto | ISearchTeacherDto[]>();

  /** Emitted when the "create new" option is clicked */
  @Output() newItemClicked = new EventEmitter<void>();

  // ===========================
  // SELECTION STATE (BACKWARD COMPATIBILITY)
  // ===========================

  /** Currently selected teacher (single selection mode) */
  selectedTeacher = model<ISearchTeacherDto>({} as ISearchTeacherDto);

  /** Currently selected teachers (multiple selection mode) */
  selectedTeachers = model<ISearchTeacherDto[]>([]);

  // ===========================
  // COMPUTED PROPERTIES (ENHANCED)
  // ===========================

  /** Items to display in dropdown with legacy support */
  override readonly displayItems = computed(() => {
    if (this.enablePagination) {
      // Use the same logic as the parent's displayItems computed property
      if (!this.enablePagination) {
        return this.allItems();
      }

      const items = this.isSearchMode() ? this.searchResults() : this.allItems();
      return Array.isArray(items) ? items.filter(item => item != null) : [];
    } else {
      // Legacy mode: use provided items and map them to ISearchTeacherDto
      return this.filterValidTeacherItems(this.items()).map(item => this.mapToISearchTeacherDto(item));
    }
  });

  /** Computed property for displaying selected teacher names */
  selectedTeacherNames = computed(() => {
    if (this.selectionMode === 'single') {
      const teacher = this.selectedTeacher();
      return teacher?.firstName && teacher?.lastName ? `${teacher.firstName} ${teacher.lastName}` : '';
    } else {
      const teachers = this.selectedTeachers();
      return teachers.map(t => `${t.firstName} ${t.lastName}`).join(', ');
    }
  });

  /** Teachers state from the data service */
  teachers$ = computed(() => this.dataStateService.teachers.state() as State<IGetTeachersResponse>);

  /** Maximum number of languages to display before truncation */
  readonly MAX_VISIBLE_LANGUAGES = 2;

  /** Custom search input value for immediate UI updates */
  searchInputValue = '';

  // ===========================
  // ABSTRACT METHOD IMPLEMENTATIONS
  // ===========================

  protected getApiEvent(): Events {
    return Events.StateLoadTeachers;
  }

  protected buildApiRequest(baseRequest: IGetTeachersRequest): IGetTeachersRequest {
    const request: IGetTeachersRequest = {
      pageNumber: baseRequest.pageNumber,
      pageSize: baseRequest.pageSize,
      sortColumn: 'firstName',
      sortDirection: 'asc',
      searchTerm: baseRequest.searchTerm || null,
      gender: IGenderEnum.None,
      approvedDateFrom: null,
      approvedDateTo: null,
      teachingLanguage: null,
      teachingLanguageLevel: null,
      speakingLanguage: null,
      speakingLanguageLevel: null,
      isNativeSpeakingLanguage: null,
      includeBlocked: null,
      accountStatus: null,
      teachingAgesExperience: null,
      teacherStudentAgesPreference: null,
      availabilityStatus: null,
      studentAgesMin: 2,
      studentAgesMax: 17,
    };

    return new DefaultGetTeachersRequest(request) as IGetTeachersRequest;
  }

  protected createStateSelector(): Observable<State<IGetTeachersResponse>> {
    return toObservable(
      computed(() => this.dataStateService.teachers.state()),
      { injector: this.injector }
    );
  }

  // ===========================
  // LIFECYCLE METHODS
  // ===========================

  override ngOnInit(): void {
    // Set up configuration from inputs
    this.idProperty = 'id' as keyof ISearchTeacherDto;

    // Enable pagination and auto-loading for API data
    this.enablePagination = true;

    super.ngOnInit();
    this.setupResetSubscriptions();
  }

  ngAfterViewInit(): void {
    // Setup any additional view-specific logic if needed
  }


  private setupResetSubscriptions(): void {
    // Watch for reset signal changes
    toObservable(this.resetSelectionSignal, { injector: this.injector })
      .pipe(this.untilDestroyed())
      .subscribe((shouldReset) => {
        console.log('🔄 Teachers resetSelectionSignal received:', shouldReset);
        if (shouldReset) {
          console.log('🔄 Teachers triggering clearSelectionUIOnly');
          this.clearSelectionUIOnly();
        }
      });

    // Subscribe to base component selection changes for backward compatibility
    toObservable(this.selectedItem, { injector: this.injector })
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(item => {
        if (item && this.selectionMode === 'single') {
          this.selectedTeacher.set(item as ISearchTeacherDto);
          this.itemClicked.emit(item as ISearchTeacherDto);
        }
      });

    toObservable(this.selectedItems, { injector: this.injector })
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(items => {
        if (this.selectionMode === 'multiple') {
          this.selectedTeachers.set(items as ISearchTeacherDto[]);
          this.itemClicked.emit(items as ISearchTeacherDto[]);
        }
      });
  }

  /**
   * Override dropdown show logic to preserve initial selection state or load fresh data
   * This prevents the base class from resetting the search term when dropdown opens with initial selection
   * But ensures fresh data loading when no initial selection is present
   */
  protected override handleDropdownShowLogic(): void {
    // Check if we have an initial selection that should be preserved
    if (this.initialSelectedId && this.enableInitialItemSelection) {
      console.log('🎯 Preserving initial teacher selection on dropdown open:', this.initialSelectedId);
      // Don't reset - keep the search term and search mode for initial selection
      this.focusSearchInputIfEnabled();
      return;
    }

    // No initial selection - load fresh data from page 1
    console.log('🔄 No initial selection - loading fresh data for teachers dropdown');

    // Reset to ensure clean state
    this.resetPagination();

    // Load fresh data from page 1
    if (this.enablePagination && this.isInitialized()) {
      console.log('🔄 Loading initial data for fresh teacher search');
      this.loadInitialData();
    }

    // Focus search input if enabled
    this.focusSearchInputIfEnabled();
  }

  override clearSelection(): void {
    // Clear the appropriate dropdown UI based on selection mode
    if (this.selectionMode === 'single') {
      this.select?.clear();
      this.selectedTeacher.set(null as any); // Set to null instead of empty object
    } else {
      // For multiselect, reset the model and trigger change detection
      this.selectedTeachers.set([]);
      if (this.selectMultiple) {
        // Force the multiselect to update its display
        this.selectMultiple.updateModel([]);
      }
    }
    this.selectedItem.set(null);
  }

  /**
   * Clear selection UI only without emitting events (for reset functionality)
   */
  override clearSelectionUIOnly(): void {
    console.log('🔄 clearSelectionUIOnly called for teachers - performing complete reset');

    // Step 1: Clear the dropdown UI first
    if (this.selectionMode === 'single') {
      console.log('🔄 Clearing single select dropdown UI');
      // Clear the PrimeNG dropdown first
      this.select?.clear();
      // Reset the model to undefined to ensure proper clearing
      this.selectedTeacher.set(undefined as any);
      // Also clear the base component selection
      this.selectedItem.set(null);
    } else {
      console.log('🔄 Clearing multi select dropdown UI');
      this.selectedTeachers.set([]);
      if (this.selectMultiple) {
        this.selectMultiple.updateModel([]);
      }
      // Also clear the base component selection
      this.selectedItems.set([]);
    }

    // Step 2: Clear initial selection properties to ensure fresh behavior
    console.log('🔄 Clearing initial selection properties');
    this.initialSelectedId = null;
    this.enableInitialItemSelection = false;

    // Step 3: Reset all base component state (search, pagination, data, etc.)
    console.log('🔄 Resetting pagination and search state');
    this.resetPagination();

    // Step 4: Clear any cached data and reset to fresh state
    console.log('🔄 Clearing cached data for fresh search');
    this.allItems.set([]);
    this.searchResults.set([]);
    this.isInitialized.set(true); // Keep initialized but reset data

    console.log('🔄 Teachers dropdown reset complete - ready for fresh search');
    console.log('🔄 Final state check:', {
      selectedTeacher: this.selectedTeacher(),
      selectedItem: this.selectedItem(),
      allItems: this.allItems().length,
      searchResults: this.searchResults().length,
      initialSelectedId: this.initialSelectedId,
      enableInitialItemSelection: this.enableInitialItemSelection
    });

    // Do NOT emit itemClicked when resetting - this should only reset UI state
    // The actual filter changes will be applied when user clicks "Apply filters"
  }

  // ===========================
  // EVENT HANDLERS (ENHANCED WITH BASE COMPONENT)
  // ===========================


  /**
   * Load more data (next page)
   */
  onLoadMoreClicked(): void {
    super.loadMoreData();
  }
  /**
   * Handle selection change event using base component method
   */
  onSelectionChange(event: { value: ISearchTeacherDto | ISearchTeacherDto[] }): void {
    this.handlePrimeSelectionChange(
      event,
      this.selectedTeacher,
      this.selectedTeachers,
      this.itemClicked
    );
  }

  /**
   * Handle multiple selection change
   */
  onSelectMultipleChange(event: any): void {
    const teachers = event.value as ISearchTeacherDto[];
    this.selectedTeachers.set(teachers);
    this.itemClicked.emit(teachers);
  }

  /**
   * Handle clear selection event from PrimeNG dropdown
   * Simply clears the UI - the parent will handle temp filter clearing via the empty initialSelectedId
   */
  onClearSelection(): void {
    console.log('🗑️ Clear selection clicked for teachers - clearing UI only');

    // Clear the UI state only
    this.handlePrimeClearSelectionUIOnly(
      this.selectedTeacher,
      this.selectedTeachers
    );

    // ✅ Don't emit any events - let the parent handle temp filter clearing
    // The parent will see that teacherId() returns empty and update accordingly

    console.log('🗑️ Teacher UI cleared - parent will handle temp filter via binding');
  }

  /**
   * Handle custom search input changes with debouncing
   */
  override onSearchInputChange(event: Event | string): void {
    // Handle both Event objects and string values
    const value = typeof event === 'string' ? event : (event.target as HTMLInputElement).value;

    // Prevent event bubbling to avoid dropdown closing (only for Event objects)
    if (typeof event !== 'string') {
      event.stopPropagation();
    }

    this.searchInputValue = value;

    console.log('🔍 Search input changed:', value);

    // Delegate to base component
    super.onSearchInputChange(value);
  }

  /**
   * Clear all selected teachers using base component method
   */
  clearAllTeachers(): void {
    this.clearAllSelections(this.selectedTeachers, this.itemClicked);
  }

  /**
   * Select all available teachers (for multiple selection)
   */
  selectAllTeachers(): void {
    const allTeachers = this.displayItems() as ISearchTeacherDto[];
    this.selectedTeachers.set(allTeachers);
    this.itemClicked.emit(allTeachers);
  }

  // ===========================
  // UTILITY METHODS
  // ===========================

  /**
   * Get display text for a teacher's teaching languages
   */
  getTeacherLanguagesText(teacher: ISearchTeacherDto): string {
    if (!teacher.teacherTeachingLanguages || teacher.teacherTeachingLanguages.length === 0) {
      return 'No languages';
    }

    const languages = teacher.teacherTeachingLanguages.map(tl => tl.teachingLanguageName || 'Unknown');

    if (languages.length <= this.MAX_VISIBLE_LANGUAGES) {
      return languages.join(', ');
    }

    const visible = languages.slice(0, this.MAX_VISIBLE_LANGUAGES);
    const remaining = languages.length - this.MAX_VISIBLE_LANGUAGES;
    return `${visible.join(', ')} +${remaining} more`;
  }

  /**
   * Get teacher's full name
   */
  getTeacherFullName(teacher: ISearchTeacherDto): string {
    return `${teacher.firstName || ''} ${teacher.lastName || ''}`.trim();
  }

  /**
   * Get teacher's location text
   */
  getTeacherLocationText(teacher: ISearchTeacherDto): string {
    const parts = [teacher.city, teacher.country].filter(Boolean);
    return parts.join(', ') || 'Location not specified';
  }

  /**
   * Filters out invalid items from the array (teacher-specific implementation)
   * @param items Array of teacher items to filter
   * @returns Filtered array of valid teacher items
   */
  private filterValidTeacherItems(items: ISearchTeacherDto[]): ISearchTeacherDto[] {
    return this.filterValidItems(items);
  }

  /**
   * Maps ISearchTeacherDto to ISearchTeacherDto
   * @param teacher Teacher DTO to map
   * @returns ISearchTeacherDto with required id property
   */
  private mapToISearchTeacherDto(teacher: ISearchTeacherDto): ISearchTeacherDto {
    return {
      ...teacher,
      id: teacher.id || '', // Ensure id property exists for DropdownItem compatibility
    } as ISearchTeacherDto;
  }

  // ===========================
  // TEMPLATE HELPER METHODS
  // ===========================

  /**
   * Get visible teaching languages for display (with truncation)
   * @param teachingLanguages Array of teacher teaching languages
   * @returns Array of visible teaching languages
   */
  getVisibleTeachingLanguages(teachingLanguages: ITeacherTeachingLanguageDto[]): ITeacherTeachingLanguageDto[] {
    if (!teachingLanguages || !Array.isArray(teachingLanguages)) {
      return [];
    }

    return teachingLanguages.slice(0, this.MAX_VISIBLE_LANGUAGES);
  }

  /**
   * Check if teaching languages should be truncated
   * @param teachingLanguages Array of teaching languages
   * @returns True if should truncate
   */
  shouldTruncateTeachingLanguages(teachingLanguages: ITeacherTeachingLanguageDto[]): boolean {
    if (!teachingLanguages || !Array.isArray(teachingLanguages)) {
      return false;
    }

    return teachingLanguages.length > this.MAX_VISIBLE_LANGUAGES;
  }

  /**
   * Get remaining teaching languages count for display
   * @param teachingLanguages Array of teaching languages
   * @returns Number of remaining teaching languages
   */
  getRemainingTeachingLanguagesCount(teachingLanguages: ITeacherTeachingLanguageDto[]): number {
    if (!teachingLanguages || !Array.isArray(teachingLanguages)) {
      return 0;
    }

    return Math.max(0, teachingLanguages.length - this.MAX_VISIBLE_LANGUAGES);
  }

  /**
   * Get tooltip text for remaining teaching languages
   * @param teachingLanguages Array of teaching languages
   * @returns Tooltip text
   */
  getRemainingTeachingLanguagesTooltip(teachingLanguages: ITeacherTeachingLanguageDto[]): string {
    if (!teachingLanguages || !Array.isArray(teachingLanguages)) {
      return '';
    }

    const remainingLanguages = teachingLanguages.slice(this.MAX_VISIBLE_LANGUAGES);
    return remainingLanguages.map(lang => lang.teachingLanguageName).join(', ');
  }
}
