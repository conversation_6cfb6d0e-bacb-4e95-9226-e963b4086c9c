import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, inject, Input, input, Output, ViewChild, OnInit, AfterViewInit, computed, Injector } from '@angular/core';
import { toObservable, takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { Observable } from 'rxjs';
import { ButtonModule } from 'primeng/button';
import { Select, SelectModule } from 'primeng/select';
import { InputTextModule } from 'primeng/inputtext';
import { TooltipModule } from 'primeng/tooltip';
import { AvatarModule } from 'primeng/avatar';
import { AvatarGroupModule } from 'primeng/avatargroup';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { StudentGroupRowDisplayComponent } from '../../student-group-row-display/student-group-row-display.component';
import { BaseDropdownComponent } from '../../base/base-dropdown.component';
import { AuthStateService } from '../../../services/auth-state.service';
import { GeneralService } from '../../../services/general.service';
import { DataApiStateService, State } from '../../../services/data-api-state.service';
import { EventBusService, Events, EmitEvent, DefaultGetStudentGroupsRequest } from '../../../services/event-bus.service';
import { ISearchStudentGroupsDto, IGetStudentGroupsRequest, IGetStudentGroupsResponse, IStudentGroupDto, IBasicProfileInfoDto, ILanguageLevelsEnum } from '../../../GeneratedTsFiles';
import { IUserRole, nameOf } from '../../../models/general.model';
const ISearchStudentGroupsDtoParamMap = nameOf<ISearchStudentGroupsDto>();

@Component({
  selector: 'app-prime-student-group-selection',
  imports: [
    CommonModule,
    FormsModule,
    SelectModule,
    ButtonModule,
    InputTextModule,
    TooltipModule,
    AvatarModule,
    AvatarGroupModule,
    IconFieldModule,
    InputIconModule,
    StudentGroupRowDisplayComponent
  ],
  templateUrl: './prime-student-group-selection.component.html',
  styleUrl: './prime-student-group-selection.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PrimeStudentGroupSelectionComponent
  extends BaseDropdownComponent<ISearchStudentGroupsDto, IGetStudentGroupsRequest, IGetStudentGroupsResponse>
  implements OnInit, AfterViewInit {

  @ViewChild('selectStudentsList') select: Select | undefined;

  // Injected services
  generalService = inject(GeneralService);
  private dataStateService = inject(DataApiStateService);
  private authService = inject(AuthStateService);
  protected override eventBusService = inject(EventBusService);
  protected override injector = inject(Injector);

  // Existing inputs (maintained for backward compatibility)
  @Input() override selectionMode: 'single' | 'multiple' = 'single';
  @Input() nameProperty: string = 'name';
  @Input() imageProperty: string = 'image';
  @Input() groupImageProperty: string = 'image';
  @Input() imageProperties: string[] = [];
  @Input() itemImage: string = '';
  @Input() languagesMode = false;
  @Input() studentGroupMode = false;
  @Input() studentGroup = [] as unknown[];
  @Input() items: (ISearchStudentGroupsDto | IStudentGroupDto)[] = []; // Legacy items input - will be enhanced with pagination
  @Input() createNewText!: string;
  @Input() createNewImage!: string;
  @Input() selectable = true;
  @Input() numVisible = 4;
  @Input() selectedStudent: ISearchStudentGroupsDto | undefined;

  // Override base class inputs with component-specific defaults
  @Input() override enablePagination = false; // Enable pagination functionality
  @Input() override pageSize = 5; // Items per page (default from StudentGroupsListHelperService)
  @Input() override autoLoadInitialData = false; // Auto-load data on init when pagination is enabled
  @Input() override enableSearch = true; // Enable search functionality (enabled by default)
  @Input() override searchDebounceTime = 400; // Debounce time for search in milliseconds
  @Input() override searchPlaceholder = 'Search student groups...'; // Search placeholder text

  // Initial selection inputs
  @Input() override initialSelectedId: string | number | null = null; // Initial group ID to select
  @Input() override enableInitialItemSelection = false; // Enable initial item selection by ID
  @Input() override idProperty: keyof ISearchStudentGroupsDto = ISearchStudentGroupsDtoParamMap.id as keyof ISearchStudentGroupsDto; // Property to use for ID matching

  // Input signals
  styleClass = input('w-full full-width mb-2');
  selectedItemProperty = input('');
  baseProperty = input('');
  textForNameProperty = input('');
  resetSelectionSignal = input(false);

  // Component-specific output events
  @Output() groupItemClicked: EventEmitter<ISearchStudentGroupsDto> = new EventEmitter<ISearchStudentGroupsDto>();
  @Output() newItemClicked: EventEmitter<ISearchStudentGroupsDto> = new EventEmitter<ISearchStudentGroupsDto>();

  // Custom search input management (component-specific)
  searchInputValue = '';

  // Component-specific computed properties for legacy items support
  componentDisplayItems = computed(() => {
    if (this.enablePagination) {
      // Use base class displayItems for paginated data
      return this.displayItems();
    }
    // Legacy support for non-paginated items
    return Array.isArray(this.items) ? this.items.filter(item => item != null) : [];
  });

  studentGroups$ = computed(() => this.dataStateService.parentStudentsGroups.state() as State<IGetStudentGroupsResponse>);

  // ===========================
  // ABSTRACT METHOD IMPLEMENTATIONS
  // ===========================

  protected getApiEvent(): Events {
    return Events.StateLoadParentStudentsGroups;
  }

  protected buildApiRequest(baseRequest: IGetStudentGroupsRequest): IGetStudentGroupsRequest {
    const userId = this.authService.getUserClaims()?.id;
    if (!userId) {
      console.warn('Parent ID not found. Cannot build API request.');
      return baseRequest;
    }

    const request = {
      ...baseRequest,
      parentId: this.authService.getUserRole() === IUserRole.PARENT ? userId : null,
      includeDeleted: false
    };

    return new DefaultGetStudentGroupsRequest(request);
  }

  protected createStateSelector(): Observable<State<IGetStudentGroupsResponse>> {
    return toObservable(this.studentGroups$, { injector: this.injector });
  }

  // ===========================
  // LIFECYCLE METHODS
  // ===========================

  override ngOnInit(): void {
    super.ngOnInit();
    this.setupComponentSpecificSubscriptions();
    this.setupAutoSelectionSubscription();
  }

  ngAfterViewInit(): void {
    // Setup cleaner search handling
    this.setupCleanSearchHandling();
  }

  // ===========================
  // COMPONENT-SPECIFIC METHODS
  // ===========================

  private setupComponentSpecificSubscriptions(): void {
    // Subscribe to reset selection signal
    toObservable(this.resetSelectionSignal, {
      injector: this.injector
    }).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (reset) => {
        if (reset) {
          console.log('resetSelectionSignal', reset);
          this.select?.clear();
          this.selectedStudent = {} as ISearchStudentGroupsDto;
          this.groupItemClicked.emit(this.selectedStudent);
        }
      }
    });
  }

  private setupCleanSearchHandling(): void {
    // Minimal setup - let the existing search logic handle everything
    console.log('Search handling setup complete');
  }

  // Component-specific selection handling
  onSelectionChange(event: { value: ISearchStudentGroupsDto}) {
    console.log(event);
    this.selectedStudent = event.value;
    this.groupItemClicked.emit(this.selectedStudent);
  }

  /**
   * Setup subscription to handle auto-selection when data is loaded
   */
  private setupAutoSelectionSubscription(): void {
    // Subscribe to the student groups state to handle auto-selection
    toObservable(this.studentGroups$, { injector: this.injector }).pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe({
      next: (state) => {
        if (state.data?.pageData && state.data.pageData.length > 0) {
          console.log('🔍 Groups loaded, calling base class auto-selection logic');

          // Call the base class auto-selection method directly
          (this as any).handleAutoSelectItemById(state.data.pageData);

          // Also update our component-specific selectedStudent property
          if (this.selectedItem()) {
            this.selectedStudent = this.selectedItem() as ISearchStudentGroupsDto;
            this.groupItemClicked.emit(this.selectedStudent);
          }
        }
      },
      error: (error) => {
        console.error('Error in auto-selection subscription:', error);
      }
    });
  }

  // Override dropdown show/hide to add component-specific behavior
  override onDropdownShow(): void {
    super.onDropdownShow();
    console.log('🔽 Student Group dropdown opened');
  }

  override onDropdownHide(): void {
    super.onDropdownHide();
    console.log('🔼 Student Group dropdown closed');
  }

  // Override search input handling to manage custom searchInputValue
  override onSearchInputChange(event: Event | string): void {
    const value = typeof event === 'string' ? event : (event.target as HTMLInputElement).value;
    this.searchInputValue = value;
    super.onSearchInputChange(value);
  }

  // Override clear search input to manage custom searchInputValue
  override clearSearchInput(): void {
    this.searchInputValue = '';
    super.clearSearchInput();
  }



  // Component-specific API loading method
  protected loadData(): void {
    this.loadStudentGroups();
  }

  private loadStudentGroups(): void {
    const parentId = this.authService.getUserClaims()?.id;
    if (!parentId) {
      console.warn('Parent ID not found. Cannot load student groups.');
      return;
    }

    // Create a proper request object using DefaultGetStudentGroupsRequest
    const baseRequest = {
      pageNumber: this.currentPage(),
      pageSize: this.pageSize,
      parentId: parentId,
      includeDeleted: false
    };

    // Add search term if in search mode
    if (this.isSearchMode() && this.searchTerm().trim()) {
      (baseRequest as DefaultGetStudentGroupsRequest).searchTerm = this.searchTerm().trim();
    }

    const request = new DefaultGetStudentGroupsRequest(baseRequest);

    console.log('🚀 loadStudentGroups called:', {
      page: this.currentPage(),
      searchMode: this.isSearchMode(),
      searchTerm: this.searchTerm(),
      request: baseRequest
    });

    // Emit event to trigger API call via state management
    this.eventBusService.emit(new EmitEvent(Events.StateLoadParentStudentsGroups, request));
  }

  /**
   * Safely get group details with null checking
   */
  getSafeGroupDetails(data: ISearchStudentGroupsDto): string {
    if (!data) {
      return 'Unknown Group';
    }

    try {
  
      return this.generalService.getGroupDetails(data as ISearchStudentGroupsDto);
    } catch (error) {
      console.warn('Error getting group details:', error, data);

      // Fallback to manual group name construction
      const groupName = data.groupName || data.teachingLanguageName || 'Unknown Group';
      const levelText = data.groupLevel ? this.generalService.getILanguageLevelsEnumText(data.groupLevel, false) : '';

      return levelText ? `${groupName} ${levelText}` : groupName;
    }
  }

  /**
   * Ensure group data has all required properties for child components
   */
  ensureSafeGroupData(data: ISearchStudentGroupsDto | IStudentGroupDto | null | undefined): IStudentGroupDto | ISearchStudentGroupsDto {
    if (!data) {
      return {} as IStudentGroupDto;
    }

    // Ensure required properties exist with safe defaults
    const safeData = {
      ...data,
      groupName: data.groupName || data.teachingLanguageName || '',
      teachingLanguageName: data.teachingLanguageName || '',
      groupLevel: data.groupLevel || ILanguageLevelsEnum.A1,
      id: data.id || '',
      groupStatus: data.groupStatus || 0
    };

    // Ensure students/studentInfo array exists
    if ('students' in data) {
      // ISearchStudentGroupsDto
      return {
        ...safeData,
        students: Array.isArray(data.students) ? data.students : []
      } as ISearchStudentGroupsDto;
    } else {
      // IStudentGroupDto
      return {
        ...safeData,
        studentInfo: Array.isArray((data as IStudentGroupDto).studentInfo) ? (data as IStudentGroupDto).studentInfo : []
      } as IStudentGroupDto;
    }
  }

  /**
   * Get display name for the group (compact version)
   */
  getGroupDisplayName(groupData: ISearchStudentGroupsDto): string {
    if (!groupData) return 'Unknown Group';

    // Use existing getSafeGroupDetails method but make it more compact
    const fullName = this.getSafeGroupDetails(groupData);

    // If the name is too long, truncate it for dropdown display
    return fullName.length > 25 ? fullName.substring(0, 25) + '...' : fullName;
  }

  /**
   * Get language level text in a compact format
   */
  getLanguageLevelText(level: ILanguageLevelsEnum | number | null | undefined): string {
    if (!level) return '';

    try {
      return this.generalService.getILanguageLevelsEnumText(level, false) || '';
    } catch (error) {
      console.warn('Error getting language level text:', error);
      return '';
    }
  }

  /**
   * Get students array for display in avatar group
   */
  getStudentsForDisplay(groupData: ISearchStudentGroupsDto): IBasicProfileInfoDto[] {
    if (!groupData || !groupData.students) {
      return [];
    }

    return Array.isArray(groupData.students) ? groupData.students : [];
  }

  /**
   * Get student initials for avatar display
   */
  getStudentInitials(student: IBasicProfileInfoDto): string {
    if (!student) return '?';

    const firstName = (student.firstName || '').trim();
    const lastName = (student.lastName || '').trim();

    if (!firstName && !lastName) return '?';

    const firstInitial = firstName.charAt(0).toUpperCase();
    const lastInitial = lastName.charAt(0).toUpperCase();

    return firstInitial + lastInitial;
  }

  /**
   * Get search placeholder text based on current state
   */
  override getSearchPlaceholder(): string {
    if (this.isSearching()) {
      return 'Searching...';
    } else if (this.isTyping()) {
      return 'Type to search...';
    }
    return this.searchPlaceholder;
  }

  /**
   * Handle load more clicked event
   */
  onLoadMoreClicked(): void {
    if (this.isLoading() || !this.hasMoreData()) {
      return;
    }

    this.currentPage.update(page => page + 1);
    this.loadStudentGroups();
  }

  /**
   * Get avatar style for student with consistent colors
   */
  getStudentAvatarStyle(student: IBasicProfileInfoDto): { [key: string]: string } {
    if (!student) {
      return { 'background-color': 'var(--surface-300)', 'color': 'var(--text-color)' };
    }

    // Use a simple hash function to generate consistent colors based on student name
    const name = (student.firstName || '') + (student.lastName || '');
    const hash = this.simpleHash(name);

    // Define a set of pleasant colors for avatars
    const colors = [
      '#6366f1', '#8b5cf6', '#a855f7', '#d946ef', '#ec4899',
      '#f43f5e', '#ef4444', '#f97316', '#f59e0b', '#eab308',
      '#84cc16', '#22c55e', '#10b981', '#14b8a6', '#06b6d4',
      '#0ea5e9', '#3b82f6', '#6366f1', '#8b5cf6', '#a855f7'
    ];

    const colorIndex = hash % colors.length;

    return {
      'background-color': colors[colorIndex],
      'color': '#ffffff',
      'font-weight': '600'
    };
  }

  /**
   * Simple hash function for consistent color generation
   */
  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

}
