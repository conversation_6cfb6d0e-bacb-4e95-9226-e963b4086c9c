import { Component, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet } from '@angular/router';

/**
 * Students Parent Component
 * 
 * Serves as a parent container for all student-related pages including:
 * - Students List
 * - Student Profile
 * - Add Student
 * - Student Management features
 * 
 * This component provides the routing outlet for student-related child routes
 */
@Component({
  selector: 'app-students',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet
  ],
  template: `
    <div class="students-container">
      <router-outlet></router-outlet>
    </div>
  `,
  styles: [`
    .students-container {
      height: 100%;
      width: 100%;
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class StudentsComponent {
  // This component serves as a routing container
  // All functionality is handled by child components
}
