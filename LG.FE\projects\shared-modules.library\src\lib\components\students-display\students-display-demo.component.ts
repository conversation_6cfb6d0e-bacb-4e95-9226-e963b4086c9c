import { CommonModule } from '@angular/common';
import { Component, ChangeDetectionStrategy } from '@angular/core';
import { StudentsDisplayComponent } from './students-display.component';
import { IBasicProfileInfoDto, ISearchStudentDto } from '../../GeneratedTsFiles';

@Component({
  selector: 'lib-students-display-demo',
  standalone: true,
  imports: [
    CommonModule,
    StudentsDisplayComponent
  ],
  template: `
    <div class="p-4 space-y-6">
      <h2 class="text-2xl font-bold mb-4">Students Display Component Demo</h2>
      
      <!-- Horizontal Layout -->
      <div class="demo-section">
        <h3 class="text-lg font-semibold mb-2">Horizontal Layout</h3>
        <div class="surface-card p-3 border-round">
          <lib-students-display 
            [students]="mockStudents"
            [config]="{
              layout: 'horizontal',
              size: 'medium',
              showImages: true,
              showNames: true,
              showAge: true,
              showLanguages: false,
              maxVisible: 4,
              showMoreButton: true
            }">
          </lib-students-display>
        </div>
      </div>
      
      <!-- Avatar Group Layout -->
      <div class="demo-section">
        <h3 class="text-lg font-semibold mb-2">Avatar Group Layout</h3>
        <div class="surface-card p-3 border-round">
          <lib-students-display 
            [students]="mockStudents"
            [config]="{
              layout: 'avatar-group',
              size: 'medium',
              showImages: true,
              maxVisible: 5,
              showMoreButton: true
            }">
          </lib-students-display>
        </div>
      </div>
      
      <!-- Compact Layout -->
      <div class="demo-section">
        <h3 class="text-lg font-semibold mb-2">Compact Layout (Tags)</h3>
        <div class="surface-card p-3 border-round">
          <lib-students-display 
            [students]="mockStudents"
            [config]="{
              layout: 'compact',
              size: 'normal',
              showImages: false,
              showNames: true,
              showAge: false,
              showLanguages: false,
              maxVisible: 6,
              showMoreButton: true
            }">
          </lib-students-display>
        </div>
      </div>
      
      <!-- Vertical Layout with Languages -->
      <div class="demo-section">
        <h3 class="text-lg font-semibold mb-2">Vertical Layout with Languages</h3>
        <div class="surface-card p-3 border-round">
          <lib-students-display 
            [students]="mockStudents"
            [config]="{
              layout: 'vertical',
              size: 'medium',
              showImages: true,
              showNames: true,
              showAge: true,
              showLanguages: true,
              maxVisible: 3,
              showMoreButton: true
            }">
          </lib-students-display>
        </div>
      </div>
      
      <!-- Grid Layout -->
      <div class="demo-section">
        <h3 class="text-lg font-semibold mb-2">Grid Layout</h3>
        <div class="surface-card p-3 border-round">
          <lib-students-display 
            [students]="mockStudents"
            [config]="{
              layout: 'grid',
              size: 'medium',
              showImages: true,
              showNames: true,
              showAge: true,
              showLanguages: true,
              maxVisible: 6,
              showMoreButton: false
            }">
          </lib-students-display>
        </div>
      </div>
      
      <!-- Basic Profile Info (without language data) -->
      <div class="demo-section">
        <h3 class="text-lg font-semibold mb-2">Basic Profile Info (IBasicProfileInfoDto)</h3>
        <div class="surface-card p-3 border-round">
          <lib-students-display
            [students]="mockBasicUsers"
            [config]="{
              layout: 'horizontal',
              size: 'medium',
              showImages: true,
              showNames: true,
              showAge: true,
              showLanguages: false,
              maxVisible: 3,
              showMoreButton: false
            }">
          </lib-students-display>
        </div>
      </div>

      <!-- Empty State -->
      <div class="demo-section">
        <h3 class="text-lg font-semibold mb-2">Empty State</h3>
        <div class="surface-card p-3 border-round">
          <lib-students-display
            [students]="[]"
            [config]="{
              layout: 'horizontal',
              size: 'medium'
            }">
          </lib-students-display>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .demo-section {
      margin-bottom: 2rem;
    }
    
    .space-y-6 > * + * {
      margin-top: 1.5rem;
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class StudentsDisplayDemoComponent {

  // Basic profile info (without language data)
  mockBasicUsers: IBasicProfileInfoDto[] = [
    {
      userId: '1',
      firstName: 'Emma',
      lastName: 'Johnson',
      discriminator: 'student',
      dateOfBirth: new Date('2010-05-15'),
      profilePhotoUrl: 'https://randomuser.me/api/portraits/women/1.jpg',
      hasCompletedOnBoardingTutorial: true,
      hasSetupPassword: true
    },
    {
      userId: '2',
      firstName: 'Liam',
      lastName: 'Smith',
      discriminator: 'student',
      dateOfBirth: new Date('2011-08-22'),
      profilePhotoUrl: 'https://randomuser.me/api/portraits/men/2.jpg',
      hasCompletedOnBoardingTutorial: true,
      hasSetupPassword: true
    },
    {
      userId: '3',
      firstName: 'Sophia',
      lastName: 'Davis',
      discriminator: 'teacher',
      dateOfBirth: new Date('1985-12-03'),
      profilePhotoUrl: 'https://randomuser.me/api/portraits/women/3.jpg',
      hasCompletedOnBoardingTutorial: true,
      hasSetupPassword: true
    }
  ];

  // Extended student data (with language information)
  mockStudents: ISearchStudentDto[] = [
  ];
}
