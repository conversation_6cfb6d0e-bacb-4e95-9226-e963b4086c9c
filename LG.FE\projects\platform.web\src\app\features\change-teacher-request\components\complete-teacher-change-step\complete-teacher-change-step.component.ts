import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, signal, computed, OnInit, Input, Output, EventEmitter, DestroyRef } from '@angular/core';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';

// PrimeNG Imports
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { DropdownModule } from 'primeng/dropdown';
import { DividerModule } from 'primeng/divider';
import { SkeletonModule } from 'primeng/skeleton';
import { RadioButtonModule } from 'primeng/radiobutton';
import { TextareaModule } from 'primeng/textarea';
import { TagModule } from 'primeng/tag';

// Shared Library
import {
  AuthStateService,
  HandleApiResponseService,
  IGetTeachersToChangeResponse,
  ITeacherToChangeWithAffectedResourcesDto,
  IBasicProfileInfoDto,
  IAffectedStudentPackageDto,
  PrimeProfilePhotoSingleComponent
} from 'SharedModules.Library';

// Local Interfaces
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { TeacherChangeType } from '../../interfaces/change-teacher-request.interfaces';
import { IStudentGroupSelectionStepData } from '../student-group-selection-step/student-group-selection-step.component';

// Services
import { TeacherChangeRequestService } from '../../../../shared/services/teacher-change-request.service';



export interface ICompleteTeacherChangeStepData {
  teacherId?: string;
  teacher?: IBasicProfileInfoDto;
  reason?: string;
}

@Component({
  selector: 'app-complete-teacher-change-step',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ButtonModule,
    CardModule,
    DropdownModule,
    DividerModule,
    SkeletonModule,
    RadioButtonModule,
    TextareaModule,
    TagModule,
    PrimeProfilePhotoSingleComponent
  ],
  templateUrl: './complete-teacher-change-step.component.html',
  styleUrl: './complete-teacher-change-step.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CompleteTeacherChangeStepComponent implements OnInit {
  // Injected services
  private readonly fb = inject(FormBuilder);
  // private readonly generalService = inject(GeneralService);
  private readonly apiService = inject(HandleApiResponseService);
  private readonly authStateService = inject(AuthStateService);
  private readonly teacherChangeRequestService = inject(TeacherChangeRequestService);
  private readonly destroy: DestroyRef = inject(DestroyRef);

  // Inputs
  @Input() initialData?: ICompleteTeacherChangeStepData;
  @Input() changeType: TeacherChangeType = TeacherChangeType.COMPLETE_CHANGE;
  @Input() studentGroupData?: IStudentGroupSelectionStepData; // For STUDENT_GROUP_CHANGE

  // Outputs
  @Output() dataChanged = new EventEmitter<ICompleteTeacherChangeStepData>();
  @Output() validationChanged = new EventEmitter<boolean>();



  // Component state
  form!: FormGroup;
  isLoading = signal(false);
  teachersToChangeResponse = signal<IGetTeachersToChangeResponse | null>(null);
  teacherOptions = signal<IBasicProfileInfoDto[]>([]);
  selectedTeacherId = signal<string | null>(null);
  selectedTeacher = signal<IBasicProfileInfoDto | null>(null);
  reason = signal<string>('');



  currentData = computed<ICompleteTeacherChangeStepData>(() => {
    const teacher = this.selectedTeacher();
    const teacherId = this.selectedTeacherId();
    const reasonValue = this.reason();
    return {
      teacherId: teacherId || undefined,
      teacher: teacher || undefined,
      reason: reasonValue || undefined
    };
  });

  ngOnInit(): void {
    this.initializeForm();
    this.setupFormSubscriptions();
    this.loadInitialData();

    // Only load teachers for COMPLETE_CHANGE type
    // For STUDENT_GROUP_CHANGE, teachers are loaded with specific parameters
    if (this.changeType === TeacherChangeType.COMPLETE_CHANGE) {
      console.log('🔍 Loading teachers for COMPLETE_CHANGE (parentId only)');
      this.initGetTeachersToChangeRequest();
    } else if (this.changeType === TeacherChangeType.STUDENT_GROUP_CHANGE) {
      console.log('🔍 Loading teachers for STUDENT_GROUP_CHANGE with specific parameters');
      this.loadTeachersForStudentGroupChange();
    }
  }


  initGetTeachersToChangeRequest(): void {
    this.isLoading.set(true);
    this.apiService.getApiData<IGetTeachersToChangeResponse>(
      { url: 'TeacherChangeRequest/GetTeachersToChangeRequest', method: 'GET' },
      { parentId: this.authStateService.getUserClaims()?.id }
    ).pipe(takeUntilDestroyed(this.destroy))
      .subscribe({
        next: (response: IGetTeachersToChangeResponse) => {
          console.log('Teachers to change response:', response);
          this.teachersToChangeResponse.set(response);
          this.processTeachersData(response);
          this.isLoading.set(false);
        },
        error: (error) => {
          console.error('Error loading teachers to change:', error);
          this.isLoading.set(false);
        }
      });
  }

  /**
   * Load teachers for STUDENT_GROUP_CHANGE with specific parameters
   */
  private loadTeachersForStudentGroupChange(): void {
    if (!this.studentGroupData) {
      console.warn('🔍 No student/group data available for loading teachers');
      return;
    }

    this.isLoading.set(true);

    const params = {
      teachingLanguageId: this.studentGroupData.teachingLanguageId,
      studentId: this.studentGroupData.studentId,
      groupId: this.studentGroupData.groupId,
      parentId: this.authStateService.getUserClaims()?.id
    };

    console.log('🔍 Loading teachers for STUDENT_GROUP_CHANGE with params:', params);

    this.teacherChangeRequestService.getAvailableTeachers(params).subscribe({
      next: (availableTeachersData) => {
        console.log('🔍 Teachers loaded for STUDENT_GROUP_CHANGE:', availableTeachersData.teachers.length, 'teachers');
        this.teacherOptions.set(availableTeachersData.teachers);
        this.isLoading.set(false);
      },
      error: (error) => {
        console.error('Error loading teachers for STUDENT_GROUP_CHANGE:', error);
        this.isLoading.set(false);
      }
    });
  }

  private processTeachersData(response: IGetTeachersToChangeResponse): void {
    const teachers: IBasicProfileInfoDto[] = [];

    response.teachersToChangeWithAffectedResources.forEach(teacherData => {
      // Get teacher info from the first affected student package
      const firstPackage = teacherData.affectedStudentPackages[0];
      // The API response includes additional properties not in the interface definition
      const packageWithTeacher = firstPackage as IAffectedStudentPackageDto & { teacher: IBasicProfileInfoDto };

      if (packageWithTeacher?.teacher && teacherData.teacherToChangeId) {
        // Check if we already have this teacher (avoid duplicates)
        const existingTeacher = teachers.find(t => t.userId === teacherData.teacherToChangeId);
        if (!existingTeacher) {
          teachers.push(packageWithTeacher.teacher);
        }
      }
    });

    console.log('Teacher options:', teachers);
    this.teacherOptions.set(teachers);
  }

  private initializeForm(): void {
    this.form = this.fb.group({
      reason: ['', [Validators.required, Validators.minLength(10), Validators.maxLength(1000)]]
    });
  }

  private setupFormSubscriptions(): void {
    // Subscribe to reason field changes
    this.form.get('reason')?.valueChanges.pipe(
      takeUntilDestroyed(this.destroy)
    ).subscribe(value => {
      this.reason.set(value || '');
      this.emitDataChange();
    });
  }

  private loadInitialData(): void {
    if (this.initialData) {
      // Set selected teacher
      if (this.initialData.teacherId) {
        this.selectedTeacherId.set(this.initialData.teacherId);
        if (this.initialData.teacher) {
          this.selectedTeacher.set(this.initialData.teacher);
        }
      }

      // Set reason
      if (this.initialData.reason) {
        this.reason.set(this.initialData.reason);
        this.form.get('reason')?.setValue(this.initialData.reason);
      }
    }
  }

  // Teacher selection handlers
  onTeacherRadioChange(teacherToChangeId: string): void {
    console.log('Teacher radio selected:', teacherToChangeId);
    this.selectedTeacherId.set(teacherToChangeId);

    // Find the selected teacher from options using teacher userId
    const selectedTeacher = this.teacherOptions().find(teacher => teacher.userId === teacherToChangeId);
    if (selectedTeacher) {
      this.selectedTeacher.set(selectedTeacher);
    }

    this.emitDataChange();
  }

  onTeacherCleared(): void {
    console.log('Teacher selection cleared');
    this.selectedTeacherId.set(null);
    this.selectedTeacher.set(null);
    this.emitDataChange();
  }



  // Data emission methods
  private emitDataChange(): void {
    this.dataChanged.emit(this.currentData());
    // Emit validation state - valid if teacher is selected and reason is provided
    const isValid = this.selectedTeacherId() !== null &&
                   this.selectedTeacher() !== null &&
                   this.form.get('reason')?.valid === true;
    this.validationChanged.emit(isValid);
  }

  getData(): ICompleteTeacherChangeStepData {
    return this.currentData();
  }

  reset(): void {
    this.form.reset();
    this.selectedTeacherId.set(null);
    this.selectedTeacher.set(null);
    this.emitDataChange();
  }
}
