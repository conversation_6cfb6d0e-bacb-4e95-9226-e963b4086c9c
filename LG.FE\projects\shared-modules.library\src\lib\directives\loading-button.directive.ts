import { Directive, Input, ElementRef, Renderer2, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { Subscription } from 'rxjs';

@Directive({
  selector: '[disableWhileRequest]'
})
export class DisableWhileRequestDirective implements OnIni<PERSON>, OnD<PERSON>roy {
  @Input('disableWhileRequest') subscription: Subscription | undefined;

  constructor(private el: ElementRef, private renderer: Renderer2) {}

  ngOnInit() {
    if (this.subscription) {
      // Disable the button when the subscription starts
      this.renderer.setProperty(this.el.nativeElement, 'disabled', true);

      // Listen to the subscription completion (success or error)
      this.subscription.add(() => {
        // Re-enable the button when the request completes
        this.renderer.setProperty(this.el.nativeElement, 'disabled', false);
        console.log("🚀 ~ DisableWhileRequestDirective ~ this.subscription.add ~ nativeElement:", this.el.nativeElement)

      });
    }
  }

  ngOnDestroy() {
    // Clean up the subscription if the directive is destroyed
    if (this.subscription && !this.subscription.closed) {
      this.subscription.unsubscribe();
    }
  }
}