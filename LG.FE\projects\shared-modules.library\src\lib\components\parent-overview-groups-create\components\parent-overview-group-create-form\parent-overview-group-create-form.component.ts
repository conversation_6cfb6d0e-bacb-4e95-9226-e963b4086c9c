// ... existing code ...
import { ChangeDetectionStrategy, Component, computed, DestroyRef, EventEmitter, inject, Injector, input, linkedSignal, model, OnDestroy, OnInit, Output, signal, Signal } from '@angular/core';

import { ButtonModule } from 'primeng/button';
import { AbstractControl, FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  IApiResponseBase,
  IAvailability,
  IAvailabilityDto,
  IBasicProfileInfoDto,
  ICreateAvailabilityDto,
  ICreateStudentGroupRequest,
  ICreateStudentGroupResponse,
  IEditStudentGroupRequest,
  IFindCommonTimeSlotsResponse,
  IGetAllTeachingLanguagesResponse,
  IGetAvailabilityResponse,
  IGetStudentGroupResponse,
  IGetStudentsResponse,
  ILanguageLevelsEnum,
  ISearchStudentDto,
  IStudentGroupDto,
  IStudentLevelEnum,
  ITeachingLanguageDto,
  IWeekDayTimeSlotDto,
  StudentGroupRoutes
} from '../../../../GeneratedTsFiles';
import { Params, Router } from '@angular/router';
import { InputTextModule } from 'primeng/inputtext';
import { SelectChangeEvent, SelectModule } from 'primeng/select';
import { toObservable } from '@angular/core/rxjs-interop';
import { TextareaModule } from 'primeng/textarea';
import { ActivatedRoute } from '@angular/router';
import { SmartStudentSelectorComponent } from '../smart-student-selector/smart-student-selector.component';
import { StudentGroupSelectionSuggestionTextStepComponent } from '../../../dialogs/student-group-selection-dialog/student-group-selection-suggestion-text-step/student-group-selection-suggestion-text-step.component';
import { StudentGroupService } from '../../../../services/student-group.service';
import { PrimeStudentsSelectionComponent } from '../../../prime/prime-students-selection/prime-students-selection.component';
import { StudentGroupFormService } from '../../../../services/student-group-form.service';
import { CardSplitLayoutComponent } from '../../../card-split-layout/card-split-layout.component';
import { GeneralService } from '../../../../services/general.service';
import { AuthStateService } from '../../../../services/auth-state.service';
import { HandleApiResponseService } from '../../../../services/handle-api-response.service';
import { DataApiStateService } from '../../../../services/data-api-state.service';
import { ToastService } from '../../../../services/toast.service';
import { EventBusService, Events, EmitEvent, DefaultGetStudentsRequest } from '../../../../services/event-bus.service';
import { FormErrorScrollerService } from '../../../../services/form-error-scroller.service';
import { ApiLoadingStateService } from '../../../../services/api-loading-state.service';
import { CustomValidators } from '../../../../helpers/custom-validators';
import { GroupDialogState, nameOf, IUserRole } from '../../../../models/general.model';
import { GenericPrimeDropdownOption, GenericPrimeEnumToDropdownOptionsConfig } from '../../../../services/general.service';
import { ITimeZoneIdData } from '../../../availability-picker-days/availability-timezone-selector/availability-timezone-selector.component';
import { AvailabilityPickerDaysComponent } from '../../../availability-picker-days/availability-picker-days.component';
import { AvailabilityTimezoneSelectorComponent } from '../../../availability-picker-days/availability-timezone-selector/availability-timezone-selector.component';
import { PrimeReactiveFormInputComponent } from '../../../prime/prime-reactive-form-input/prime-reactive-form-input.component';
import { PrimeDropdownComponent } from '../../../prime/prime-dropdown/prime-dropdown.component';
import { FormFieldValidationMessageComponent } from '../../../prime/form-field-validation-message/form-field-validation-message.component';

// Type helper to create FormControl types from interface
type FormControlsOf<T> = {
  [K in keyof T]: K extends 'availability'
  ? FormControl<T[K] | null>
  : T[K] extends object
  ? T[K] extends any[]
  ? FormControl<T[K] | null>
  : FormGroup<FormControlsOf<T[K]>>
  : FormControl<T[K] | null>;
};

@Component({
  selector: 'app-parent-overview-group-create-form',
  imports: [
    CommonModule,
    CardSplitLayoutComponent,
    ButtonModule,
    StudentGroupSelectionSuggestionTextStepComponent,
    FormsModule,
    ReactiveFormsModule,
    InputTextModule,
    TextareaModule,
    SelectModule,
    AvailabilityPickerDaysComponent,
    AvailabilityTimezoneSelectorComponent,
    PrimeStudentsSelectionComponent,
    PrimeReactiveFormInputComponent,
    PrimeDropdownComponent,
    FormFieldValidationMessageComponent,
    SmartStudentSelectorComponent,
  ],
  templateUrl: './parent-overview-group-create-form.component.html',
  styleUrl: './parent-overview-group-create-form.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ParentOverviewGroupCreateFormComponent implements OnInit, OnDestroy {
  private readonly destroyRef = inject(DestroyRef);
  groupForm!: FormGroup<FormControlsOf<ICreateStudentGroupRequest>>;
  editGroupForm: FormGroup<FormControlsOf<IEditStudentGroupRequest>> = new FormGroup<FormControlsOf<IEditStudentGroupRequest>>({} as any);

  // Injected services
  readonly services = {
    general: inject(GeneralService),
    auth: inject(AuthStateService),
    api: inject(HandleApiResponseService),
    studentGroup: inject(StudentGroupService),
    studentGroupForm: inject(StudentGroupFormService),
    dataState: inject(DataApiStateService),
    toast: inject(ToastService),
    eventBus: inject(EventBusService),
    formErrorScroller: inject(FormErrorScrollerService),
    apiLoadingStateService: inject(ApiLoadingStateService),
    router: inject(Router)
  };

  private readonly injector = inject(Injector);
  readonly EditGroupStateEnum = GroupDialogState;
  protected readonly GroupDialogState = GroupDialogState;
  isDialogPopup = model<boolean>(false);
  isEditMode = model<boolean>(false);
  showTopTitle = model<boolean>(true);
  parentId = model<string>(this.services.auth.getUserClaims().id);
  groupId = model<string | null>(null);
  editGroupState = model<GroupDialogState>(GroupDialogState.CreateGroupSuggestionStep);

  // Query parameter signals
  languageIdFromQuery = signal<string | null>(null);
  hasProcessedLanguagePreselection = signal<boolean>(false);
  isWaitingForLanguagePreselection = computed(() => {
    const languageIdFromQuery = this.languageIdFromQuery();
    const hasProcessed = this.hasProcessedLanguagePreselection();
    const isEditMode = this.isEditMode();

    // Show loader if we have a languageId query param, haven't processed it yet, and not in edit mode
    return !isEditMode && languageIdFromQuery && !hasProcessed;
  });

  @Output() onAvailabilityStepValidityChanged = new EventEmitter<boolean>();
  @Output() onGroupStateChanged = new EventEmitter<GroupDialogState>(undefined);
  @Output() onGroupCreated = new EventEmitter<ICreateStudentGroupResponse>();
  @Output() onGroupFormValidityChanged = new EventEmitter<boolean>();

  availabilityForm: FormGroup = new FormGroup({});
  availabilityData = signal<IWeekDayTimeSlotDto[]>([]);
  availabilityTimezone = signal<ITimeZoneIdData>({} as ITimeZoneIdData);
  availabilityDataLoaded = signal(false);
  initialAvailabilityLoaded = signal(false); // Track if initial availability has been loaded in edit mode
  areAllCommonTimeSlotsEmptyBool = signal(true);
  isPanelClosed = false;
  selectedLanguageOfInterest = signal<string>('');
  selectedStudents = signal<ISearchStudentDto[]>([]);
  allStudents$ = signal<ISearchStudentDto[]>([]);
  // Raw students data from API
  rawStudents$: Signal<ISearchStudentDto[]> = computed(() => {
    const studentState = this.services.dataState.parentStudents.state();
    const studentsResponse: IGetStudentsResponse = studentState.data as IGetStudentsResponse;
    return studentsResponse?.pageData ?? [];
  });

  // Filtered students based on selected language and availability
  students$: Signal<ISearchStudentDto[]> = computed(() => {
    const allStudents = this.rawStudents$();
    const selectedLanguageId = this.selectedLanguageOfInterest();

    // If no language is selected, return all students
    if (!selectedLanguageId) {
      return allStudents;
    }

    // Filter students by selected language ID and where groupId is null (not in any group)
    const filteredStudents = this.services.studentGroupForm.getAvailableStudentsForLanguage(allStudents, selectedLanguageId);

    // Log filtering results for debugging
    console.log('🎯 Language filter applied:', {
      selectedLanguageId,
      totalStudents: allStudents.length,
      filteredStudents: filteredStudents.length,
      filterCriteria: 'teachingLanguageId matches AND groupId is null'
    });

    return filteredStudents;
  });

  teachingLanguages$ = computed(() => this.services.dataState.teachingLanguages.state().data as IGetAllTeachingLanguagesResponse);

  // Available students for group creation (not already in a group for the selected language)
  availableStudentsForLanguage$: Signal<ISearchStudentDto[]> = computed(() => {
    const allStudents = this.rawStudents$();
    const selectedLanguageId = this.selectedLanguageOfInterest();

    if (!selectedLanguageId) {
      return [];
    }

    if (this.isEditMode()) {
      // In edit mode, return all students with the selected language (including those in groups)
      return this.services.studentGroupForm.getStudentsByLanguageAndGroupStatus(allStudents, selectedLanguageId, false)
        .concat(this.services.studentGroupForm.getStudentsByLanguageAndGroupStatus(allStudents, selectedLanguageId, true));
    } else {
      // In create mode, only return students not in any group for this language
      return this.services.studentGroupForm.getAvailableStudentsForLanguage(allStudents, selectedLanguageId);
    }
  });

  // Students already in groups for the selected language (for reference/debugging)
  studentsInGroupsForLanguage$: Signal<ISearchStudentDto[]> = computed(() => {
    const allStudents = this.rawStudents$();
    const selectedLanguageId = this.selectedLanguageOfInterest();

    if (!selectedLanguageId) {
      return [];
    }

    return this.services.studentGroupForm.getStudentsInGroupsForLanguage(allStudents, selectedLanguageId);
  });

  // Validation state
  hasMinimumStudents = computed(() => this.selectedStudents().length >= 2);
  availabilityFormValid = signal(false);

  // Computed note text for availability picker
  availabilityNoteText = computed(() => {
    const timezone = this.availabilityTimezone();
    if (this.isEditMode() && timezone?.timeZoneDisplayName) {
      return `Timezone: ${timezone.timeZoneDisplayName}`;
    }
    return 'Please note that all times are in your account\'s timezone.';
  });

  // Debug method to check form validation status
  getFormValidationErrors(): string[] {
    if (!this.groupForm) return ['Form not initialized'];
    return this.services.studentGroupForm.extractFormValidationErrors(this.groupForm);
  }

  // Student level dropdown options
  studentLevels: GenericPrimeDropdownOption[] = [];

  // Computed group level from selected students' ILanguageLevelsEnum
  computedGroupLanguageLevels = computed(() => {
    const students = this.selectedStudents();
    const selectedLanguageId = this.selectedLanguageOfInterest();
    return this.services.studentGroupForm.computeGroupLanguageLevels(students, selectedLanguageId);
  });

  // Display text for computed group levels
  computedGroupLevelsText = computed(() => {
    const levels = this.computedGroupLanguageLevels();
    return this.services.studentGroupForm.createGroupLevelsText(levels);
  });

  // Suggested student level based on language levels
  suggestedStudentLevel = computed((): IStudentLevelEnum => {
    const levels = this.computedGroupLanguageLevels();
    return this.services.studentGroupForm.suggestStudentLevel(levels);
  });

  // Computed level from selected students - maps to IStudentLevelEnum
  computedLevel = computed((): IStudentLevelEnum => {
    const students = this.selectedStudents();
    return this.services.studentGroupForm.computeStudentLevel(students);
  });

  // Form Options
  readonly levels = [
    { label: 'Select a Level *', value: null },
    { label: 'No Experience', value: IStudentLevelEnum.NoExperience },
    { label: 'Beginner', value: IStudentLevelEnum.Beginner },
    { label: 'Intermediate', value: IStudentLevelEnum.Intermediate },
    { label: 'Advanced', value: IStudentLevelEnum.Advanced }
  ];

  studentGroupItem = signal<any>({});
  originalStudents = signal<ISearchStudentDto[]>([]); // Track original students in the group for edit mode
  title = computed(() => this.isEditMode() ? 'Edit Student Group' : 'Create New Student Group');
  paragraphText = computed(() => this.isEditMode()
    ? 'Update your student group details below'
    : 'Create a new student group by selecting students and setting availability');

  constructor(private route: ActivatedRoute) { }

  ngOnInit(): void {
    this.initializeStudentLevels();
    this.initEvents();

    // Initialize timezone with user's timezone
    const userBasicInfo = this.services.auth.getUserBasicInfo();
    if (userBasicInfo?.timeZoneId) {
      this.availabilityTimezone.set({
        timeZoneId: userBasicInfo.timeZoneId,
        timeZoneDisplayName: userBasicInfo.timeZoneDisplayName || 'Unknown',
        timeZoneIana: userBasicInfo.timeZoneIana || ''
      });
    }

    if (this.isEditMode()) {
      this.editGroupForm = this.editGroupClassTypedFormGroup();
    } else {
      this.groupForm = this.createGroupClassTypedFormGroup();
      // Reset availability flags when switching to create mode
      this.initialAvailabilityLoaded.set(false);
      this.availabilityDataLoaded.set(false);
    }

    if (this.isEditMode() && this.groupId()) {
      this.editGroupState.set(GroupDialogState.EditGroup);
      this.loadGroupDetails(this.groupId()!);
    } else {
      this.handleQueryParams();
    }

    // Setup language preselection watcher
    this.setupLanguagePreselectionWatcher();


    // Watch for students data changes using toObservable
    toObservable(this.students$, { injector: this.injector })
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((students: ISearchStudentDto[]) => {
        this.allStudents$.set(students);
      });

    // Watch for student selection changes to reset availability data
    toObservable(this.hasMinimumStudents, { injector: this.injector })
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((hasMinimum: boolean) => {
        if (!hasMinimum) {
          this.availabilityDataLoaded.set(false);
          this.areAllCommonTimeSlotsEmptyBool.set(true);
        }
      });

    if (!this.isEditMode()) {
      this.groupForm.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
        this.onGroupFormValidityChanged.emit(this.activeForm.valid);
      });
    }

    // Watch for form validity changes

  }

  ngOnDestroy(): void {
    // HTTP calls, router subscriptions, and form subscriptions are automatically cleaned up by takeUntilDestroyed
  }

  private createGroupClassTypedFormGroup(): FormGroup<FormControlsOf<ICreateStudentGroupRequest>> {
    return new FormGroup<FormControlsOf<ICreateStudentGroupRequest>>({
      parentId: new FormControl<string | null>(this.parentId(), Validators.required),
      teachingLanguageId: new FormControl<string | null>(null, Validators.required),
      studentsToAdd: new FormControl<string[] | null>(null, Validators.required),
      studentLevel: new FormControl<IStudentLevelEnum | null>(null, Validators.required),
      moreDetails: new FormControl<string | null>(null, CustomValidators.moreDetails),
      groupName: new FormControl<string | null>(null, CustomValidators.groupName(150)),
      availabilityDto: new FormGroup<FormControlsOf<ICreateAvailabilityDto>>({
        weekDayTimeSlots: new FormControl<IWeekDayTimeSlotDto[] | null>(null, Validators.required)
      })
    });
  }

  private editGroupClassTypedFormGroup(): FormGroup<FormControlsOf<IEditStudentGroupRequest>> {

    return new FormGroup<FormControlsOf<IEditStudentGroupRequest>>({
      parentId: new FormControl<string | null>(this.parentId(), Validators.required),
      groupId: new FormControl<string | null>(this.groupId(), Validators.required),
      moreDetails: new FormControl<string | null>(null, CustomValidators.moreDetails),
      groupName: new FormControl<string | null>(null, CustomValidators.groupName(150)),
      studentsToAdd: new FormControl<string[] | null>(null),
      studentsToRemove: new FormControl<string[] | null>(null),
      studentLevel: new FormControl<IStudentLevelEnum | null>(null, Validators.required),
      availability: new FormControl<IAvailabilityDto | null>(null, Validators.required)
    });
  }

  // Get the active form based on mode
  get activeForm(): FormGroup {
    return this.isEditMode() ? this.editGroupForm : this.groupForm;
  }

  canSaveGroup(): boolean {
    const hasMinStudents = this.hasMinimumStudents();
    const formValid = this.activeForm.valid;
    const availabilityValid = this.isAvailabilityValid();

    console.log('🔍 canSaveGroup validation:', {
      hasMinStudents,
      formValid,
      availabilityValid,
      availabilityDataLoaded: this.availabilityDataLoaded(),
      availabilityFormValid: this.availabilityFormValid()
    });

    return hasMinStudents && formValid && availabilityValid;
  }

  // Helper methods to access form controls
  getFormControl(name: string): AbstractControl | null {
    return this.activeForm.get(name);
  }

  onSubmit(): void {
    if (this.isEditMode()) {
      if (this.editGroupForm.valid) {
        this.updateStudentGroup();
      } else {
        this.editGroupForm.markAllAsTouched();
        this.services.formErrorScroller.validateAndScrollToFirstError(this.editGroupForm);
      }
    } else {
      if (this.groupForm.valid) {
        this.createStudentGroupRequest();
      } else {
        this.groupForm.markAllAsTouched();
        this.services.formErrorScroller.validateAndScrollToFirstError(this.groupForm);
      }
    }
  }

  private updateStudentGroup(): void {
    const formValue = this.editGroupForm.getRawValue();

    console.log('🚀 ~ ParentOverviewGroupsCreateComponent ~ updateStudentGroup ~ formValue:', formValue);
    // return;
    const request: IEditStudentGroupRequest = {
      parentId: this.parentId(),
      groupId: this.groupId()!,
      groupName: formValue.groupName!,
      studentLevel: formValue.studentLevel!,
      moreDetails: formValue.moreDetails!,
      studentsToAdd: formValue.studentsToAdd!,
      studentsToRemove: formValue.studentsToRemove!,
      availability: formValue.availability!,
    }

    this.services.api.getApiData<IEditStudentGroupRequest>(
      { url: StudentGroupRoutes.patchEditStudentGroup, method: 'PATCH' },
      request
    ).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: this.handleEditGroupSuccess,
    });
  }

  private handleEditGroupSuccess = (): void => {
    this.services.eventBus.emit(new EmitEvent(Events.StudentGroupEdited, undefined));
    this.services.toast.show({ severity: 'success', summary: 'Success', detail: 'Student group updated successfully' });
    
    if (this.services.auth.getUserClaims().role !== IUserRole.ADMIN) {
    this.services.router.navigate(['/dashboard/parent/groups/list']);
    }
    this.onGroupStateChanged.emit(GroupDialogState.AfterEditSuccess);
  }

  onLanguageChange(event: SelectChangeEvent): void {
    console.log('Language changed:', event.value);
    this.selectedLanguageOfInterest.set(event.value as string);
    this.availabilityDataLoaded.set(false);
    this.selectedStudents.set([]);
    this.loadStudents();

    // Debug: Log filtering results after language change
    this.logStudentFilteringResults();
  }

  /**
   * Get students filtered by language ID and group status
   * @param languageId - The teaching language ID to filter by
   * @param inGroup - true for students in groups, false for students not in groups
   * @returns Array of filtered students
   */
  getStudentsByLanguageAndGroupStatus(languageId: string, inGroup: boolean): ISearchStudentDto[] {
    const allStudents = this.rawStudents$();
    return this.services.studentGroupForm.getStudentsByLanguageAndGroupStatus(allStudents, languageId, inGroup);
  }

  /**
   * Get available students for a specific language (not in any group)
   * @param languageId - The teaching language ID
   * @returns Array of available students
   */
  getAvailableStudentsForLanguage(languageId: string): ISearchStudentDto[] {
    const allStudents = this.rawStudents$();
    return this.services.studentGroupForm.getAvailableStudentsForLanguage(allStudents, languageId);
  }

  /**
   * Validates if a student can be selected in edit mode
   * Prevents selection of students already assigned to another group with the same teaching language
   * @param student - The student to validate
   * @returns true if student can be selected, false otherwise
   */
  validateStudentSelection = (student: ISearchStudentDto): boolean => {
    const context = {
      isEditMode: this.isEditMode(),
      currentGroupLanguageId: this.selectedLanguageOfInterest(),
      currentGroupId: this.groupId(),
      originalStudents: this.originalStudents(),
      selectedLanguageName: this.getSelectedLanguageName()
    };

    return this.services.studentGroupForm.validateStudentSelection(student, context);
  }

  /**
   * Get students already in groups for a specific language
   * @param languageId - The teaching language ID
   * @returns Array of students in groups
   */
  getStudentsInGroupsForLanguage(languageId: string): ISearchStudentDto[] {
    const allStudents = this.rawStudents$();
    return this.services.studentGroupForm.getStudentsInGroupsForLanguage(allStudents, languageId);
  }

  /**
   * Debug method to log student filtering results
   */
  private logStudentFilteringResults(): void {
    const selectedLanguageId = this.selectedLanguageOfInterest();
    if (!selectedLanguageId) return;

    const allStudents = this.rawStudents$();
    const filteringResult = this.services.studentGroupForm.createFilteringResult(allStudents, selectedLanguageId);

    this.services.studentGroupForm.logStudentFilteringResults(allStudents, selectedLanguageId, filteringResult);
  }

  onStudentSelected(students: ISearchStudentDto[]): void {
    console.log('Students selected:', students);
    this.selectedStudents.set(students);

    // Update form with selected student IDs
    if (this.isEditMode()) {
      // In edit mode, calculate studentsToAdd and studentsToRemove based on original students
      const changes = this.services.studentGroupForm.calculateStudentSelectionChanges(
        this.originalStudents(),
        students
      );

      console.log('Edit mode student changes:', {
        originalStudentIds: this.originalStudents().map(s => s.userId),
        currentStudentIds: students.map(s => s.userId),
        studentsToAdd: changes.studentsToAdd,
        studentsToRemove: changes.studentsToRemove
      });

      this.editGroupForm.patchValue({
        studentsToAdd: changes.studentsToAdd,
        studentsToRemove: changes.studentsToRemove
      });
      console.log('Edit group form value:', this.editGroupForm.value);
    } else {
      // Create mode: simple assignment
      this.groupForm.patchValue({
        studentsToAdd: students.map(s => s.userId)
      });
    }

    // Handle availability based on mode and student selection
    if (this.isEditMode()) {
      // Edit mode: Only use the existing group's availability, never calculate new common time slots
      console.log('🔍 Edit mode - preserving existing group availability');

      // In edit mode, we always keep the loaded availability regardless of student changes
      // The availability belongs to the group, not to the specific student combination
      if (this.initialAvailabilityLoaded()) {
        this.availabilityDataLoaded.set(true);
        console.log('⏭️ Edit mode - using existing group availability');
      } else {
        console.log('⏳ Edit mode - waiting for initial group availability to load');
      }
    } else {
      // Create mode: Calculate common time slots for selected students
      if (students.length >= 2) {
        this.availabilityDataLoaded.set(false);
        this.fetchCommonTimeSlots(students.map(s => s.userId));
        console.log('🔄 Create mode - fetching common time slots for selected students');
      } else {
        // Reset availability data if below minimum in create mode
        this.availabilityDataLoaded.set(false);
        this.areAllCommonTimeSlotsEmptyBool.set(true);
        console.log('⚠️ Create mode - insufficient students for availability calculation');
      }
    }
  }

  // API Methods
  fetchCommonTimeSlots(studentIds: string[]): void {
    this.services.api.getApiData<IFindCommonTimeSlotsResponse>(
      { url: IAvailability.postFindCommonTimeSlots, method: 'POST' },
      { studentIds }
    ).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (response: IFindCommonTimeSlotsResponse) => this.handleFetchSuccess(response),
      error: (error: IApiResponseBase) => this.handleFetchError(error),
    });
  }

  private handleFetchError(error: IApiResponseBase): void {
    console.error('Error fetching common time slots:', error);
    this.availabilityDataLoaded.set(true);
  }

  private getGroupAvailability(availabilityId: string): void {
    this.services.api.getApiData<IGetAvailabilityResponse>(
      { url: IAvailability.getAvailability, method: 'GET' },
      { availabilityId }
    ).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (response: IGetAvailabilityResponse) => this.handleGetAvailabilitySuccess(response),
      error: () => this.availabilityDataLoaded.set(true)
    });
  }

  // Event Handlers
  onTimeSlotsChange(timeSlots: IWeekDayTimeSlotDto[]): void {
    if (this.isEditMode()) {
      const timezone = this.availabilityTimezone();
      this.editGroupForm.patchValue({
        availability: {
          timeZoneDisplayName: timezone.timeZoneDisplayName,
          timeZoneIana: timezone.timeZoneIana,
          timeZoneId: timezone.timeZoneId!,
          weekDayTimeSlots: timeSlots,
          availabilityId: this.editGroupForm.get('availability')?.value?.availabilityId || undefined,
        }
      });
    } else {
      this.groupForm.patchValue({
        availabilityDto: {
          weekDayTimeSlots: timeSlots,
        }
      });
    }

    console.log(this.services.studentGroup.getEditStudentGroupRequest());
  }

  onTimeZoneChanged(timezone: ITimeZoneIdData): void {
    console.log('🌍 Timezone changed:', timezone);

    // Update local timezone property for noteText reactivity
    this.availabilityTimezone.set(timezone);

    this.services.studentGroup.updateEditStudentGroupRequest({
      availability: {
        ...this.services.studentGroup.getEditStudentGroupRequest().availability,
        timeZoneDisplayName: timezone.timeZoneDisplayName,
        timeZoneIana: timezone.timeZoneIana,
      }
    });
  }

  onAvailabilityDaysFormValid(valid: boolean): void {
    this.onAvailabilityStepValidityChanged.emit(valid);
    this.availabilityFormValid.set(valid);
  }

  /**
   * Checks if the availability is valid
   * This considers both the availability form validity and the availability data state
   */
  isAvailabilityValid(): boolean {
    // If availability data is not loaded yet, it's not valid
    if (!this.availabilityDataLoaded()) {
      return false;
    }

    // Check the availability form validity
    return this.availabilityFormValid();
  }

  private createStudentGroupRequest() {
    let request: ICreateStudentGroupRequest = this.groupForm.getRawValue() as ICreateStudentGroupRequest;

    console.log('Create group request:', request);
    // return;
    this.services.api.getApiData<ICreateStudentGroupResponse>(
      { url: StudentGroupRoutes.postCreateStudentGroup, method: 'POST' },
      request, true
    ).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (response: ICreateStudentGroupResponse) => {
        console.log('Group created', response);
        this.services.toast.show({ severity: 'success', summary: 'Success', detail: 'Student group created successfully' });

        if (!this.isDialogPopup()) {
          this.services.router.navigate(['/dashboard/parent/groups/list']);
        }
        this.services.eventBus.emit(new EmitEvent(Events.StateLoadParentDashboard, {
          role: this.services.auth.getUserClaims().role as IUserRole,
        }));
        this.onGroupCreated.emit(response);
        // this.services.router.navigate(['/dashboard/parent/groups']);
      },
      // error: (error: IApiResponseBase) => {
      //   console.error('Failed to create group', error);
      //   this.services.toast.show({ severity: 'error', summary: 'Error', detail: this.services.general.errorDataSignal });
      // }
    });
  }



  // Helper Methods

  getSelectedLanguageName(): string {
    const selectedLanguageId = this.selectedLanguageOfInterest();
    const teachingLanguages = this.teachingLanguages$();
    return this.services.studentGroupForm.getLanguageName(selectedLanguageId, teachingLanguages);
  }

  getStudentLevelName(): string {
    const selectedLevel = this.groupForm.controls['studentLevel']?.value || null;
    return this.services.studentGroupForm.getStudentLevelName(selectedLevel, this.studentLevels);
  }

  getLanguageLevelDisplayName(level: ILanguageLevelsEnum): string {
    return this.services.studentGroupForm.getLanguageLevelDisplayName(level);
  }

  /** Initializes student levels from enum */
  private initializeStudentLevels(): void {
    const config: GenericPrimeEnumToDropdownOptionsConfig = {
      labelProperty: 'name',
      valueProperty: 'code',
      excludeKeys: ['All'],
      additionalProperties: {
        description: (key: string) => `Level ${key}`
      }
    };
    this.studentLevels = this.services.general.getDropdownOptionsFromEnum(IStudentLevelEnum, config);
  }

  private initEvents() {
    this.services.eventBus.emit(new EmitEvent(Events.StateLoadTeachingLanguages, undefined));
    this.loadStudents();
  }

  loadStudents() {
    // Load all students for the selected language without level filtering
    // Level filtering will be handled by the smart student selector
    // DefaultGetStudentsRequest automatically cleans null values in constructor
    const request = new DefaultGetStudentsRequest({
      teachingLanguage: this.selectedLanguageOfInterest(),
      pageSize: 100,
      parentId: this.parentId(),
      // Remove level filtering to get all students
    });

    this.services.eventBus.emit(new EmitEvent(Events.StateLoadParentStudents, request));
  }

  changeGroupState(state: GroupDialogState) {
    this.editGroupState.set(state);
    this.isPanelClosed = false;
    this.onGroupStateChanged.emit(state);
  }

  private handleGetAvailabilitySuccess(response: IGetAvailabilityResponse): void {
    const processedData = this.services.studentGroupForm.processAvailabilityResponse(response);

    this.availabilityTimezone.set(processedData.timezone);
    this.availabilityDataLoaded.set(true);
    this.initialAvailabilityLoaded.set(true); // Mark that initial availability has been loaded
    this.availabilityData.set(processedData.weekDayTimeSlots);

    this.editGroupForm.patchValue({
      availability: processedData.availabilityData
    });

    console.log("🚀 ~ handleGetAvailabilitySuccess ~ Initial availability loaded for edit mode");
  }

  // Handler Methods
  private handleFetchSuccess(response: IFindCommonTimeSlotsResponse): void {
    console.log(response);
    this.availabilityDataLoaded.set(true);

    const processedData = this.services.studentGroupForm.processCommonTimeSlotsResponse(response);
    this.availabilityData.set(processedData.weekDayTimeSlots);
    this.areAllCommonTimeSlotsEmptyBool.set(processedData.areAllEmpty);
  }

  private handleQueryParams(): void {
    this.route.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((params: Params) => {
      const groupIdFromParams = params['groupId'] || null;
      const languageIdFromParams = params['languageId'] || null;

      this.groupId.set(groupIdFromParams);
      this.languageIdFromQuery.set(languageIdFromParams);

      if (groupIdFromParams) {
        this.isEditMode.set(true);
        this.loadGroupDetails(groupIdFromParams);
        this.editGroupState.set(GroupDialogState.EditGroup);
      } else {
        this.isEditMode.set(false);
        this.editGroupState.set(GroupDialogState.CreateGroupSuggestionStep);
      }

      // Log query parameter extraction for debugging
      if (languageIdFromParams) {
        console.log('🔗 Language ID from query params:', languageIdFromParams);
      }
    });
  }

  private loadGroupDetails(groupId: string): void {
    this.services.api.getApiData<IGetStudentGroupResponse>(
      { url: StudentGroupRoutes.getStudentGroup, method: 'GET' },
      { GroupId: groupId }
    ).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: this.handleGetGroupSuccess,
      error: this.handleGetGroupError
    });
  }

  private handleGetGroupSuccess = (response: { studentGroup: IStudentGroupDto }): void => {
    const { studentGroup } = response;
    this.studentGroupItem.set(studentGroup);
    console.log('🚀 ~ ParentOverviewGroupsCreateComponent ~ handleGetGroupSuccess ~ response:', response);

    // Set language and process group data
    if (studentGroup.teachingLanguageId) this.selectedLanguageOfInterest.set(studentGroup.teachingLanguageId);
    const processedData = this.services.studentGroupForm.processGroupDataForForm(studentGroup);

    // Populate form
    this.editGroupForm.patchValue({ parentId: this.parentId(), ...processedData.formData });

    // Update student level control
    const studentLevelControl = this.editGroupForm.get('studentLevel');
    if (studentLevelControl && studentGroup.studentLevelEnum) {
      studentLevelControl.setValue(studentGroup.studentLevelEnum);
      studentLevelControl.markAsDirty();
      studentLevelControl.updateValueAndValidity();
    }

    // Set state and students
    this.availabilityDataLoaded.set(false);
    this.selectedStudents.set(processedData.originalStudents);
    this.originalStudents.set(processedData.originalStudents);

    // Load availability if available
    if (studentGroup.availabilityId) this.getGroupAvailability(studentGroup.availabilityId);
  }

  private handleGetGroupError = (error: any): void => {
    console.error('Failed to load group details', error);
    this.services.toast.show({ severity: 'error', summary: 'Error', detail: 'Failed to load group details' });
    this.services.router.navigate(['/dashboard/parent/groups']);
  }

  /** Sets up language preselection watcher for create mode */
  private setupLanguagePreselectionWatcher(): void {
    if (this.isEditMode()) return;

    toObservable(this.teachingLanguages$, { injector: this.injector })
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((teachingLanguagesResponse) => {
        const languageIdFromQuery = this.languageIdFromQuery();
        if (!languageIdFromQuery || this.hasProcessedLanguagePreselection()) return;
        if (!teachingLanguagesResponse?.teachingLanguages?.length) return;

        const matchingLanguage = this.services.studentGroupForm.findLanguageById(
          languageIdFromQuery, teachingLanguagesResponse.teachingLanguages
        );

        if (matchingLanguage) {
          this.groupForm.patchValue({ teachingLanguageId: matchingLanguage.id });
          this.selectedLanguageOfInterest.set(matchingLanguage.id!);
          this.loadStudents();
          this.editGroupState.set(GroupDialogState.CreateGroup);
          console.log('🎯 Language preselected:', matchingLanguage.name);
        } else {
          console.warn('⚠️ Language not found:', languageIdFromQuery);
        }
        this.hasProcessedLanguagePreselection.set(true);
      });
  }

}
