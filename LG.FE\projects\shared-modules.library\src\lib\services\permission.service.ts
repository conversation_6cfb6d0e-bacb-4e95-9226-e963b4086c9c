import { Injectable } from '@angular/core';
import { IUserRole, nameOf } from '../models/general.model';
import { IBasicProfileInfoDto, IPatchProfileInfoRequest, IUserClaims } from '../GeneratedTsFiles';

// Define valid user roles as a union type for better type safety
type ValidUserRole = IUserRole.PARENT | IUserRole.STUDENT | IUserRole.TEACHER | IUserRole.ADMIN;

// Define valid actions as a union type
type PermissionAction = 'view' | 'create' | 'update' | 'delete';

// Define valid resources as a union type
type PermissionResource = 'lessons' | 'groups' | 'students' | 'packages' | 'settings' | 'settingsProfile';

// Define valid settings sub-resources
type SettingsSubResource = 'city' | 'country' | 'zoom' | 'msTeamsEmailAddress' | 'primaryEmail';

// Define strongly typed permission paths
type PermissionPath =
  | [`${PermissionResource}`, PermissionAction]
  | ['settings', SettingsSubResource, PermissionAction]
  | ['settingsProfile', PermissionAction];

// Type for permission check result
type PermissionCheckResult = boolean;

// Type alias for better readability
type User = IUserClaims;

// Type for a single permission check value: boolean or a function
type PermissionCheckValue<T> = T extends void ? boolean | ((user: User) => boolean) : boolean | ((user: User, data: T) => boolean);

// Recursive type to map the structure of Permissions to permission check values
type NestedPermissionMap<T> = {
  [K in keyof T]?: T[K] extends object
    ? (T[K] extends { view?: any; create?: any; update?: any; delete?: any } // Heuristic to identify action nodes
      ? { [Action in keyof T[K]]?: PermissionCheckValue<T[K][Action]> }
      : NestedPermissionMap<T[K]>) // Recurse for nested objects
    : never; // Should not happen with our current Permissions structure
};

// Defines the structure of roles and their associated permissions
type RolesWithPermissions = {
  [R in ValidUserRole]: NestedPermissionMap<Permissions>;
};

const IBasicProfileInfoDtoParamsMap = nameOf<IBasicProfileInfoDto>();
type ProfileField =
  | typeof IBasicProfileInfoDtoParamsMap.firstName
  | typeof IBasicProfileInfoDtoParamsMap.lastName
  | typeof IBasicProfileInfoDtoParamsMap.gender
  | typeof IBasicProfileInfoDtoParamsMap.dateOfBirth
  | typeof IBasicProfileInfoDtoParamsMap.msTeamsEmailAddress
  | typeof IBasicProfileInfoDtoParamsMap.zoom

type ProfilePermissions = {
  [Field in ProfileField as string]: {
    action: 'view' | 'update';
    dataType: IPatchProfileInfoRequest;
  };
};

// Define specific data types for different resources
type LessonData = {
  id: string;
  title: string;
  studentId: string;
  teacherId: string;
  scheduledAt: Date;
};

type GroupData = {
  id: string;
  name: string;
  teacherId: string;
  studentIds: string[];
  isActive: boolean;
};

// Define action-specific permission types
type ResourcePermissions<T = void> = {
  view?: T;
  create?: T;
  update?: T;
  delete?: T;
};

type SettingsPermissions = {
  [key: string]: ResourcePermissions<void>;
  city: ResourcePermissions<void>;
  country: ResourcePermissions<void>;
};

// Defines the overall permission structure, including nested resources.
// The leaf nodes of this type define the `dataType` expected for that specific action.
// Use `void` if no specific data is expected for the permission check.
type Permissions = {
  lessons: ResourcePermissions<LessonData>;
  groups: ResourcePermissions<GroupData>;
  students: ResourcePermissions<void>;
  packages: ResourcePermissions<void>;
  settings: SettingsPermissions;
  settingsProfile: ProfilePermissions;
};

const ROLES: RolesWithPermissions = {
  [IUserRole.PARENT]: {
    settings: {
      city: { view: true }, // Parent can view city setting
      country: { view: true },
      zoom: { view: false },
      msTeamsEmailAddress: { view: false },
      primaryEmail: { view: true },
    },
    settingsProfile: {
      view: true,
      update: true,
    },
    lessons: { view: true, create: true, update: true },
    groups: { view: true, create: true, update: true, delete: true },
    students: { view: true, create: true, update: true, delete: false },
    packages: { view: true, create: true, update: true, delete: false },
  },
  [IUserRole.STUDENT]: {
    settings: {
      city: { view: true, update: false },
      country: { view: true, update: false },
      zoom: { view: false },
      msTeamsEmailAddress: { view: false },
      primaryEmail: { view: false },
    },
    settingsProfile: {
      view: true,
      update: false,
    },
    lessons: { view: true, create: false, update: false },
    groups: { view: true, create: false, update: false, delete: false },
    students: { view: true, create: false, update: false, delete: false },
    packages: { view: true, create: false, update: true, delete: false },
  },
  [IUserRole.TEACHER]: {
    settings: {
      city: { view: true, update: true },
      country: { view: true, update: true },
      zoom: { view: true },
      msTeamsEmailAddress: { view: true },
      primaryEmail: { view: true },
    },
    settingsProfile: {
      view: true,
      update: true,
    },
    lessons: { view: true, create: true, update: true },
    groups: { view: true, create: false, update: false, delete: false },
    students: { view: true, create: false, update: false, delete: false },
    packages: { view: true, create: false, update: true, delete: false },
  },
  [IUserRole.ADMIN]: {
    settings: {
      city: { view: true, update: true, create: true },
      country: { view: true, update: true, create: true }
    },
    lessons: { view: true, create: true, update: true },
    groups: { view: true, create: true, update: true, delete: true },
  },
};

// Constants for better maintainability
const PERMISSION_ACTIONS = ['view', 'create', 'update', 'delete'] as const;
const PERMISSION_RESOURCES = ['lessons', 'groups', 'students', 'packages', 'settings', 'settingsProfile'] as const;
const SETTINGS_SUB_RESOURCES = ['city', 'country', 'zoom', 'msTeamsEmailAddress', 'primaryEmail'] as const;

/**
 * Service for managing user permissions in a type-safe manner.
 *
 * This service provides methods to check user permissions based on their role
 * and the requested action/resource combination. It supports nested permissions
 * for complex resource hierarchies like settings.
 *
 * @example
 * ```typescript
 * // Basic permission check
 * const canViewLessons = permissionService.hasPermission(user, ['lessons', 'view']);
 *
 * // Nested permission check
 * const canUpdateCity = permissionService.hasPermission(user, ['settings', 'city', 'update']);
 *
 * // Using helper methods
 * const canCreateGroups = permissionService.canCreate(user, 'groups');
 *
 * // Batch permission checks
 * const hasAnyAdmin = permissionService.hasAnyPermission(user, [
 *   ['users', 'create'],
 *   ['users', 'delete']
 * ]);
 * ```
 */
@Injectable({
  providedIn: 'root',
})
export class PermissionService {
  constructor() { }

  /**
   * Checks if a user has permission to perform a specific action on a resource or sub-resource.
   *
   * @param user The user for whom to check permissions.
   * @param path An array of strings representing the path to the specific action.
   * The last element of the array should be the action (e.g., 'view', 'create').
   * Examples: ['lessons', 'view'], ['settings', 'city', 'update'].
   * @param data Optional data relevant to the permission check. The type of this data
   * depends on the specific action being checked (as defined in the `Permissions` type).
   * @returns True if the user has permission, false otherwise.
   */
  hasPermission<T = void>(
    user: IUserClaims | null | undefined,
    path: readonly string[],
    data?: T
  ): boolean {
    // Validate user input
    if (!user || !user.role) {
      console.warn('PermissionService: Invalid user or missing role');
      return false;
    }

    // Validate path input
    if (!path || path.length === 0) {
      console.warn('PermissionService: Invalid or empty permission path');
      return false;
    }

    const userRole = user.role as ValidUserRole;

    // Ensure the user's role is defined in our ROLES configuration.
    if (!this.isValidRole(userRole) || !ROLES[userRole]) {
      console.warn(`PermissionService: Role '${userRole}' not found in ROLES configuration.`);
      return false;
    }

    // Traverse the ROLES object based on the provided path.
    let currentPermissions: unknown = ROLES[userRole];
    const action = path[path.length - 1]; // The last element is the action
    const resourcePath = path.slice(0, -1); // The rest of the path is the resource/sub-resource

    // Validate action
    if (!this.isValidAction(action)) {
      console.warn(`PermissionService: Invalid action '${action}'`);
      return false;
    }

    // Navigate through the permission tree
    for (const segment of resourcePath) {
      if (!this.isObject(currentPermissions) || !(segment in currentPermissions)) {
        return false; // Path segment not found in permissions
      }
      currentPermissions = (currentPermissions as Record<string, unknown>)[segment];
    }

    // Get the specific permission for the action at the end of the path.
    const permission = this.isObject(currentPermissions)
      ? (currentPermissions as Record<string, unknown>)[action]
      : undefined;

    // If no specific permission is defined (null or undefined), default to false.
    if (permission == null) {
      return false;
    }

    // If the permission is a boolean, return it directly.
    if (typeof permission === 'boolean') {
      return permission;
    }

    // If it's a function, execute the function with the user and provided data.
    if (typeof permission === 'function') {
      try {
        return permission(user, data);
      } catch (error) {
        console.error('PermissionService: Error executing permission function', error);
        return false;
      }
    }

    // If permission is neither boolean nor function, default to false
    console.warn(`PermissionService: Invalid permission type for path ${path.join('.')}`);
    return false;
  }

  /**
   * Type guard to check if a role is valid
   */
  private isValidRole(role: string): role is ValidUserRole {
    return Object.values(IUserRole).includes(role as IUserRole);
  }

  /**
   * Type guard to check if an action is valid
   */
  private isValidAction(action: string): action is PermissionAction {
    return PERMISSION_ACTIONS.includes(action as PermissionAction);
  }

  /**
   * Type guard to check if a resource is valid
   */
  private isValidResource(resource: string): resource is PermissionResource {
    return PERMISSION_RESOURCES.includes(resource as PermissionResource);
  }

  /**
   * Type guard to check if a settings sub-resource is valid
   */
  private isValidSettingsSubResource(subResource: string): subResource is SettingsSubResource {
    return SETTINGS_SUB_RESOURCES.includes(subResource as SettingsSubResource);
  }

  /**
   * Type guard to check if a value is an object
   */
  private isObject(value: unknown): value is Record<string, unknown> {
    return value !== null && typeof value === 'object' && !Array.isArray(value);
  }

  /**
   * Helper method to check permissions with better type safety for common use cases
   */
  canView(user: IUserClaims | null | undefined, resource: PermissionResource, subResource?: string): boolean {
    const path = subResource ? [resource, subResource, 'view'] : [resource, 'view'];
    return this.hasPermission(user, path);
  }

  /**
   * Helper method to check create permissions
   */
  canCreate(user: IUserClaims | null | undefined, resource: PermissionResource, subResource?: string): boolean {
    const path = subResource ? [resource, subResource, 'create'] : [resource, 'create'];
    return this.hasPermission(user, path);
  }

  /**
   * Helper method to check update permissions
   */
  canUpdate(user: IUserClaims | null | undefined, resource: PermissionResource, subResource?: string): boolean {
    const path = subResource ? [resource, subResource, 'update'] : [resource, 'update'];
    return this.hasPermission(user, path);
  }

  /**
   * Helper method to check delete permissions
   */
  canDelete(user: IUserClaims | null | undefined, resource: PermissionResource, subResource?: string): boolean {
    const path = subResource ? [resource, subResource, 'delete'] : [resource, 'delete'];
    return this.hasPermission(user, path);
  }

  /**
   * Strongly typed version of hasPermission for compile-time path validation
   * This method provides better IntelliSense and compile-time checking
   */
  hasPermissionTyped(
    user: IUserClaims | null | undefined,
    path: PermissionPath,
    data?: unknown
  ): PermissionCheckResult {
    return this.hasPermission(user, path, data);
  }

  /**
   * Batch permission check - useful for checking multiple permissions at once
   */
  hasPermissions(
    user: IUserClaims | null | undefined,
    paths: readonly (readonly string[])[]
  ): boolean[] {
    return paths.map(path => this.hasPermission(user, path));
  }

  /**
   * Check if user has ANY of the specified permissions (OR operation)
   */
  hasAnyPermission(
    user: IUserClaims | null | undefined,
    paths: readonly (readonly string[])[]
  ): boolean {
    return paths.some(path => this.hasPermission(user, path));
  }

  /**
   * Check if user has ALL of the specified permissions (AND operation)
   */
  hasAllPermissions(
    user: IUserClaims | null | undefined,
    paths: readonly (readonly string[])[]
  ): boolean {
    return paths.every(path => this.hasPermission(user, path));
  }

  /**
   * Get all permissions for a user role (useful for debugging or admin interfaces)
   */
  getUserPermissions(userRole: ValidUserRole): Partial<RolesWithPermissions[ValidUserRole]> | null {
    if (!this.isValidRole(userRole) || !ROLES[userRole]) {
      return null;
    }
    return ROLES[userRole];
  }

  /**
   * Check if a user has a specific role
   */
  hasRole(user: IUserClaims | null | undefined, role: ValidUserRole): boolean {
    return user?.role === role;
  }

  /**
   * Check if a user has any of the specified roles
   */
  hasAnyRole(user: IUserClaims | null | undefined, roles: readonly ValidUserRole[]): boolean {
    return roles.some(role => this.hasRole(user, role));
  }

  /**
   * Validates a permission path for correctness
   */
  private validatePermissionPath(path: readonly string[]): { isValid: boolean; error?: string } {
    if (!path || path.length === 0) {
      return { isValid: false, error: 'Permission path cannot be empty' };
    }

    if (path.length < 2) {
      return { isValid: false, error: 'Permission path must have at least resource and action' };
    }

    const action = path[path.length - 1];
    if (!this.isValidAction(action)) {
      return { isValid: false, error: `Invalid action: ${action}` };
    }

    const resource = path[0];
    if (!this.isValidResource(resource)) {
      return { isValid: false, error: `Invalid resource: ${resource}` };
    }

    // Special validation for settings paths
    if (resource === 'settings' && path.length === 3) {
      const subResource = path[1];
      if (!this.isValidSettingsSubResource(subResource)) {
        return { isValid: false, error: `Invalid settings sub-resource: ${subResource}` };
      }
    }

    return { isValid: true };
  }

  /**
   * Enhanced hasPermission with path validation
   */
  hasPermissionSafe<T = void>(
    user: IUserClaims | null | undefined,
    path: readonly string[],
    data?: T
  ): { hasPermission: boolean; error?: string } {
    const validation = this.validatePermissionPath(path);
    if (!validation.isValid) {
      return { hasPermission: false, error: validation.error };
    }

    try {
      const hasPermission = this.hasPermission(user, path, data);
      return { hasPermission };
    } catch (error) {
      return {
        hasPermission: false,
        error: `Permission check failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
}
