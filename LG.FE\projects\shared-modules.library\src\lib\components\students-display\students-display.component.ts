import { CommonModule } from '@angular/common';
import { Component, Input, input, computed, ChangeDetectionStrategy } from '@angular/core';
import { TagModule } from 'primeng/tag';
import { AvatarModule } from 'primeng/avatar';
import { AvatarGroupModule } from 'primeng/avatargroup';
import { TooltipModule } from 'primeng/tooltip';
import { ButtonModule } from 'primeng/button';
import { IBasicProfileInfoDto } from '../../GeneratedTsFiles';
import { PrimeProfilePhotoSingleComponent } from '../prime/prime-profile-photo-single/prime-profile-photo-single.component';
import { GeneralService } from '../../services/general.service';

export type UsersDisplayLayout = 'horizontal' | 'vertical' | 'grid' | 'compact' | 'avatar-group';
export type UsersDisplaySize = 'normal' | 'medium' | 'large';

export interface IUsersDisplayConfig {
  layout?: UsersDisplayLayout;
  size?: UsersDisplaySize;
  showImages?: boolean;
  showNames?: boolean;
  showAge?: boolean;
  showLanguages?: boolean;
  maxVisible?: number;
  showMoreButton?: boolean;
  imageSize?: number;
  styleClass?: string;
  itemStyleClass?: string;
}

// Legacy type aliases for backward compatibility
export type StudentsDisplayLayout = UsersDisplayLayout;
export type StudentsDisplaySize = UsersDisplaySize;
export interface IStudentsDisplayConfig extends IUsersDisplayConfig {}

@Component({
  selector: 'lib-students-display',
  standalone: true,
  imports: [
    CommonModule,
    TagModule,
    AvatarModule,
    AvatarGroupModule,
    TooltipModule,
    ButtonModule,
    PrimeProfilePhotoSingleComponent
  ],
  templateUrl: './students-display.component.html',
  styleUrl: './students-display.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class StudentsDisplayComponent {
  
  // ===========================
  // INPUTS
  // ===========================
  
  /** Array of users to display */
  students = input<IBasicProfileInfoDto[]>([]);

  /** Configuration for the component */
  config = input<IStudentsDisplayConfig>({
    layout: 'horizontal',
    size: 'medium',
    showImages: true,
    showNames: true,
    showAge: false,
    showLanguages: false,
    maxVisible: 5,
    showMoreButton: true,
    imageSize: 24,
    styleClass: '',
    itemStyleClass: ''
  });

  /** Legacy input for backward compatibility */
  @Input() items: IBasicProfileInfoDto[] = [];
  
  // ===========================
  // COMPUTED PROPERTIES
  // ===========================
  
  /** Merged configuration with defaults */
  readonly mergedConfig = computed(() => {
    const defaultConfig: IStudentsDisplayConfig = {
      layout: 'horizontal',
      size: 'medium',
      showImages: true,
      showNames: true,
      showAge: false,
      showLanguages: false,
      maxVisible: 5,
      showMoreButton: true,
      imageSize: 24,
      styleClass: '',
      itemStyleClass: ''
    };
    return { ...defaultConfig, ...this.config() };
  });
  
  /** Students data (supports both new input signal and legacy input) */
  readonly studentsData = computed(() => {
    const inputStudents = this.students();
    return inputStudents.length > 0 ? inputStudents : this.items;
  });
  
  /** Visible students based on maxVisible config */
  readonly visibleStudents = computed(() => {
    const students = this.studentsData();
    const maxVisible = this.mergedConfig().maxVisible || students.length;
    return students.slice(0, maxVisible);
  });
  
  /** Remaining students count */
  readonly remainingCount = computed(() => {
    const students = this.studentsData();
    const maxVisible = this.mergedConfig().maxVisible || students.length;
    return Math.max(0, students.length - maxVisible);
  });
  
  /** Whether to show the "more" indicator */
  readonly showMoreIndicator = computed(() => {
    const config = this.mergedConfig();
    return config.showMoreButton && this.remainingCount() > 0;
  });
  
  /** Container CSS classes based on layout and size */
  readonly containerClasses = computed(() => {
    const config = this.mergedConfig();
    const baseClasses = ['students-display-container'];
    
    // Layout classes
    switch (config.layout) {
      case 'horizontal':
        baseClasses.push('flex', 'align-items-center', 'gap-2', 'flex-wrap');
        break;
      case 'vertical':
        baseClasses.push('flex', 'flex-column', 'gap-2');
        break;
      case 'grid':
        baseClasses.push('grid', 'gap-2');
        break;
      case 'compact':
        baseClasses.push('flex', 'align-items-center', 'gap-1');
        break;
      case 'avatar-group':
        baseClasses.push('flex', 'align-items-center');
        break;
    }
    
    // Size classes
    switch (config.size) {
      case 'normal':
        baseClasses.push('text-sm');
        break;
      case 'large':
        baseClasses.push('text-lg');
        break;
    }
    
    // Custom style class
    if (config.styleClass) {
      baseClasses.push(config.styleClass);
    }
    
    return baseClasses.join(' ');
  });
  
  /** Item CSS classes based on layout and size */
  readonly itemClasses = computed(() => {
    const config = this.mergedConfig();
    const baseClasses = ['student-item'];
    
    // Layout-specific item classes
    switch (config.layout) {
      case 'horizontal':
      case 'compact':
        baseClasses.push('flex', 'align-items-center', 'gap-2');
        break;
      case 'vertical':
        baseClasses.push('flex', 'align-items-center', 'gap-2', 'w-full');
        break;
      case 'grid':
        baseClasses.push('col-12', 'md:col-6', 'lg:col-4');
        break;
      case 'avatar-group':
        baseClasses.push('flex', 'align-items-center');
        break;
    }
    
    // Custom item style class
    if (config.itemStyleClass) {
      baseClasses.push(config.itemStyleClass);
    }
    
    return baseClasses.join(' ');
  });
  
  /** Image size based on configuration */
  readonly imageSize = computed(() => {
    const config = this.mergedConfig();
    return config.imageSize || 32;
  });
  
  // ===========================
  // CONSTRUCTOR
  // ===========================
  
  constructor(private generalService: GeneralService) {}
  
  // ===========================
  // PUBLIC METHODS
  // ===========================
  
  /**
   * Get user's full name
   */
  getStudentFullName(user: IBasicProfileInfoDto): string {
    if (!user) return 'Unknown User';
    const firstName = user.firstName || '';
    const lastName = user.lastName || '';
    return `${firstName} ${lastName}`.trim() || 'Unknown User';
  }

  /**
   * Get user's age
   */
  getStudentAge(user: IBasicProfileInfoDto): number | null {
    if (!user?.dateOfBirth) return null;
    return this.generalService.calculateAge(user.dateOfBirth);
  }

  /**
   * Get tooltip text for remaining users
   */
  getRemainingStudentsTooltip(): string {
    const users = this.studentsData();
    const maxVisible = this.mergedConfig().maxVisible || users.length;
    const remainingUsers = users.slice(maxVisible);
    return remainingUsers.map(user => this.getStudentFullName(user)).join(', ');
  }

  /**
   * Get user initials for avatar fallback
   */
  getStudentInitials(user: IBasicProfileInfoDto): string {
    const firstName = user.firstName || '';
    const lastName = user.lastName || '';
    const firstInitial = firstName.charAt(0).toUpperCase();
    const lastInitial = lastName.charAt(0).toUpperCase();
    return `${firstInitial}${lastInitial}` || 'U';
  }

  /**
   * Track by function for users
   */
  trackByStudentId(_index: number, user: IBasicProfileInfoDto): string {
    return user.userId || _index.toString();
  }

  /**
   * Check if user has language information (for extended types like ISearchStudentDto)
   */
  hasLanguageInfo(user: IBasicProfileInfoDto): boolean {
    return 'studentTeachingLanguageDto' in user &&
           Array.isArray((user as any).studentTeachingLanguageDto) &&
           (user as any).studentTeachingLanguageDto.length > 0;
  }

  /**
   * Get visible languages for display (with truncation)
   */
  getVisibleLanguages(user: IBasicProfileInfoDto): any[] {
    if (!this.hasLanguageInfo(user)) return [];
    const languages = (user as any).studentTeachingLanguageDto;
    return languages.slice(0, 2);
  }

  /**
   * Check if languages should be truncated
   */
  shouldTruncateLanguages(user: IBasicProfileInfoDto): boolean {
    if (!this.hasLanguageInfo(user)) return false;
    const languages = (user as any).studentTeachingLanguageDto;
    return languages.length > 2;
  }

  /**
   * Get remaining languages count for display
   */
  getRemainingLanguagesCount(user: IBasicProfileInfoDto): number {
    if (!this.hasLanguageInfo(user)) return 0;
    const languages = (user as any).studentTeachingLanguageDto;
    return Math.max(0, languages.length - 2);
  }

  /**
   * Get tooltip text for remaining languages
   */
  getRemainingLanguagesTooltip(user: IBasicProfileInfoDto): string {
    if (!this.hasLanguageInfo(user)) return '';
    const languages = (user as any).studentTeachingLanguageDto;
    const remainingLanguages = languages.slice(2);
    return remainingLanguages.map((lang: any) => lang.teachingLanguageName || 'Unknown').join(', ');
  }
}
