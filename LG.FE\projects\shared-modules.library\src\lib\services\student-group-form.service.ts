import { Injectable, inject } from '@angular/core';
import {
  ISearchStudentDto,
  ILanguageLevelsEnum,
  ITeachingLanguageDto,
  IGetAllTeachingLanguagesResponse,
  IStudentLevelEnum,
  IStudentGroupDto,
  IFindCommonTimeSlotsResponse,
  IGetAvailabilityResponse,
} from '../GeneratedTsFiles';
import { ToastService } from './toast.service';
import { GenericPrimeDropdownOption } from './general.service';

export interface StudentSelectionValidationContext {
  isEditMode: boolean;
  currentGroupLanguageId: string | null;
  currentGroupId: string | null;
  originalStudents: ISearchStudentDto[];
  selectedLanguageName: string;
}

export interface StudentFilteringResult {
  totalStudents: number;
  availableStudents: ISearchStudentDto[];
  studentsInGroups: ISearchStudentDto[];
}

export interface StudentSelectionChanges {
  studentsToAdd: string[];
  studentsToRemove: string[];
}

/**
 * Service responsible for student group form business logic
 * Handles student filtering, validation, and selection operations
 */
@Injectable({
  providedIn: 'root'
})
export class StudentGroupFormService {
  private readonly toastService = inject(ToastService);

  /** Filters students by language ID and group status */
  getStudentsByLanguageAndGroupStatus(students: ISearchStudentDto[], languageId: string, inGroup: boolean): ISearchStudentDto[] {
    return students.filter(student => {
      const languageEntry = student.studentTeachingLanguageDto?.find((lang: any) => lang.teachingLanguageId === languageId);
      if (!languageEntry) return false;
      const hasGroupId = languageEntry.groupId != null;
      return inGroup ? hasGroupId : !hasGroupId;
    });
  }

  /** Gets available students for a specific language (not in any group) */
  getAvailableStudentsForLanguage(students: ISearchStudentDto[], languageId: string): ISearchStudentDto[] {
    return this.getStudentsByLanguageAndGroupStatus(students, languageId, false);
  }

  /** Gets students already in groups for a specific language */
  getStudentsInGroupsForLanguage(students: ISearchStudentDto[], languageId: string): ISearchStudentDto[] {
    return this.getStudentsByLanguageAndGroupStatus(students, languageId, true);
  }

  /**
   * Validates if a student can be selected in edit mode
   * Prevents selection of students already assigned to another group with the same teaching language
   */
  validateStudentSelection(student: ISearchStudentDto, context: StudentSelectionValidationContext): boolean {
    if (!context.isEditMode || !context.currentGroupLanguageId) return true;
    if (context.originalStudents.some(s => s.userId === student.userId)) return true;

    const conflictingEntry = student.studentTeachingLanguageDto?.find((lang: any) =>
      lang.teachingLanguageId === context.currentGroupLanguageId &&
      lang.groupId && lang.groupId !== context.currentGroupId
    );

    if (conflictingEntry) {
      this.toastService.show({
        severity: 'warn',
        summary: 'Student Already Assigned',
        detail: `${student.firstName} ${student.lastName} is already assigned to another ${context.selectedLanguageName} language group and cannot be selected.`,
        life: 5000
      });
      return false;
    }
    return true;
  }

  /**
   * Calculates student selection changes for edit mode
   * @param originalStudents - Students originally in the group
   * @param currentStudents - Currently selected students
   * @returns Object containing students to add and remove
   */
  calculateStudentSelectionChanges(
    originalStudents: ISearchStudentDto[], 
    currentStudents: ISearchStudentDto[]
  ): StudentSelectionChanges {
    const originalStudentIds = originalStudents.map(s => s.userId);
    const currentStudentIds = currentStudents.map(s => s.userId);

    // Students to add: currently selected but not in original group
    const studentsToAdd = currentStudentIds.filter(id => !originalStudentIds.includes(id));

    // Students to remove: were in original group but not currently selected
    const studentsToRemove = originalStudentIds.filter(id => !currentStudentIds.includes(id));

    return { studentsToAdd, studentsToRemove };
  }

  /**
   * Gets the display name for a teaching language
   * @param languageId - The language ID to find
   * @param teachingLanguages - Available teaching languages
   * @returns The language name or empty string if not found
   */
  getLanguageName(languageId: string, teachingLanguages: IGetAllTeachingLanguagesResponse | null): string {
    if (!languageId || !teachingLanguages?.teachingLanguages) return '';

    const selectedLanguage = teachingLanguages.teachingLanguages.find(
      lang => lang.id === languageId
    );

    return selectedLanguage?.name || '';
  }

  /**
   * Gets the display name for a language level
   * @param level - The language level enum
   * @returns The display name for the level
   */
  getLanguageLevelDisplayName(level: ILanguageLevelsEnum): string {
    switch (level) {
      case ILanguageLevelsEnum.A1: return 'A1';
      case ILanguageLevelsEnum.A2: return 'A2';
      case ILanguageLevelsEnum.B1: return 'B1';
      case ILanguageLevelsEnum.B2: return 'B2';
      case ILanguageLevelsEnum.C1: return 'C1';
      case ILanguageLevelsEnum.C2: return 'C2';
      case ILanguageLevelsEnum.None: return 'N/A';
      default: return 'N/A';
    }
  }

  /**
   * Creates a filtering result summary for debugging purposes
   * @param students - All students
   * @param languageId - Selected language ID
   * @returns Filtering result summary
   */
  createFilteringResult(students: ISearchStudentDto[], languageId: string): StudentFilteringResult {
    const availableStudents = this.getAvailableStudentsForLanguage(students, languageId);
    const studentsInGroups = this.getStudentsInGroupsForLanguage(students, languageId);

    return {
      totalStudents: students.length,
      availableStudents,
      studentsInGroups
    };
  }

  /**
   * Logs detailed student filtering results for debugging
   * @param students - All students
   * @param languageId - Selected language ID
   * @param result - Filtering result
   */
  logStudentFilteringResults(
    students: ISearchStudentDto[], 
    languageId: string, 
    result: StudentFilteringResult
  ): void {
    console.log('📊 Student Filtering Results:', {
      selectedLanguageId: languageId,
      totalStudents: result.totalStudents,
      availableStudents: result.availableStudents.length,
      studentsInGroups: result.studentsInGroups.length,
      availableStudentNames: result.availableStudents.map(s => `${s.firstName} ${s.lastName}`),
      studentsInGroupNames: result.studentsInGroups.map(s => `${s.firstName} ${s.lastName}`)
    });

    // Log detailed language data for first few students for debugging
    students.slice(0, 3).forEach(student => {
      const languageData = student.studentTeachingLanguageDto?.filter((lang: any) =>
        lang.teachingLanguageId === languageId
      );

      console.log(`👤 ${student.firstName} ${student.lastName}:`, {
        languageData,
        hasSelectedLanguage: !!languageData?.length,
        groupIds: languageData?.map((lang: any) => lang.groupId)
      });
    });
  }

  /**
   * Checks if all common time slots are empty
   * @param timeSlots - Array of time slots to check
   * @returns true if all time slots are empty
   */
  areAllCommonTimeSlotsEmpty(timeSlots: any[]): boolean {
    return timeSlots.every(item => item.timeSlots.length === 0);
  }

  /** Computes group language levels from selected students */
  computeGroupLanguageLevels(students: ISearchStudentDto[], selectedLanguageId: string): ILanguageLevelsEnum[] {
    if (!students.length || !selectedLanguageId) return [];
    const levels = new Set<ILanguageLevelsEnum>();
    students.forEach(student =>
      student.studentTeachingLanguageDto?.forEach(lang => {
        if (lang.teachingLanguageId === selectedLanguageId) levels.add(lang.languageLevel);
      })
    );
    return Array.from(levels).sort();
  }

  /** Creates display text for computed group levels */
  createGroupLevelsText(levels: ILanguageLevelsEnum[]): string {
    return levels.length ? levels.map(level => this.getLanguageLevelDisplayName(level)).join(', ') : 'N/A';
  }

  /** Suggests student level based on language levels */
  suggestStudentLevel(levels: ILanguageLevelsEnum[]): IStudentLevelEnum {
    if (!levels.length) return IStudentLevelEnum.Beginner;
    if (levels.some(l => [ILanguageLevelsEnum.A1, ILanguageLevelsEnum.A2].includes(l))) return IStudentLevelEnum.Beginner;
    if (levels.some(l => [ILanguageLevelsEnum.B1, ILanguageLevelsEnum.B2].includes(l))) return IStudentLevelEnum.Intermediate;
    if (levels.some(l => [ILanguageLevelsEnum.C1, ILanguageLevelsEnum.C2].includes(l))) return IStudentLevelEnum.Advanced;
    return IStudentLevelEnum.Beginner;
  }

  /** Computes student level from selected students */
  computeStudentLevel(students: ISearchStudentDto[]): IStudentLevelEnum {
    if (!students.length) return IStudentLevelEnum.Beginner;
    const allLevels = new Set<ILanguageLevelsEnum>();
    students.forEach(student =>
      student.studentTeachingLanguageDto?.forEach(lang => allLevels.add(lang.languageLevel))
    );
    return this.suggestStudentLevel(Array.from(allLevels));
  }

  /** Gets student level name from dropdown options */
  getStudentLevelName(selectedLevel: IStudentLevelEnum | null, studentLevels: GenericPrimeDropdownOption[]): string {
    return selectedLevel ? studentLevels.find(level => level.code === selectedLevel)?.name || '' : '';
  }

  /** Processes availability response for edit mode */
  processAvailabilityResponse(response: IGetAvailabilityResponse) {
    const { availability } = response;
    return {
      timezone: { timeZoneDisplayName: availability.timeZoneDisplayName, timeZoneIana: availability.timeZoneIana },
      weekDayTimeSlots: availability.weekDayTimeSlots,
      availabilityData: {
        availabilityId: availability.availabilityId,
        weekDayTimeSlots: availability.weekDayTimeSlots,
        timeZoneDisplayName: availability.timeZoneDisplayName,
        timeZoneId: availability.timeZoneId,
        timeZoneIana: availability.timeZoneIana,
      }
    };
  }

  /** Processes common time slots response */
  processCommonTimeSlotsResponse(response: IFindCommonTimeSlotsResponse | null) {
    if (!response) return { weekDayTimeSlots: [], areAllEmpty: true };
    const weekDayTimeSlots = response.availability.weekDayTimeSlots;
    return { weekDayTimeSlots, areAllEmpty: this.areAllCommonTimeSlotsEmpty(weekDayTimeSlots) };
  }

  /** Finds matching language from teaching languages by ID */
  findLanguageById(languageId: string, teachingLanguages: ITeachingLanguageDto[]): ITeachingLanguageDto | undefined {
    return teachingLanguages.find(lang => lang.id === languageId);
  }

  /** Processes group data for edit mode form population */
  processGroupDataForForm(studentGroup: IStudentGroupDto) {
    return {
      formData: {
        groupId: studentGroup.id,
        groupName: studentGroup.groupName,
        studentLevel: studentGroup.studentLevelEnum,
        moreDetails: studentGroup.moreDetails,
        studentsToAdd: [],
        studentsToRemove: []
      },
      originalStudents: (studentGroup.studentInfo || []) as ISearchStudentDto[]
    };
  }

  /** Extracts form validation errors for debugging */
  extractFormValidationErrors(form: any): string[] {
    const errors: string[] = [];

    Object.keys(form.controls).forEach(key => {
      const control = form.get(key);
      if (control?.invalid) {
        if (control.errors?.['required']) errors.push(`${key} is required`);
        if (control.errors) errors.push(`${key}: ${JSON.stringify(control.errors)}`);
      }
    });

    const availabilityDto = form.get('availabilityDto');
    if (availabilityDto?.invalid) {
      Object.keys(availabilityDto.controls).forEach((nestedKey: string) => {
        const nestedControl = availabilityDto.get(nestedKey);
        if (nestedControl?.invalid) {
          if (nestedControl.errors?.['required']) errors.push(`availabilityDto.${nestedKey} is required`);
          if (nestedControl.errors) errors.push(`availabilityDto.${nestedKey}: ${JSON.stringify(nestedControl.errors)}`);
        }
      });
    }

    return errors;
  }
}
