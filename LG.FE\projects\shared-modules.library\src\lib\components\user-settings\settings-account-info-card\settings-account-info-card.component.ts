import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, EventEmitter, inject, input, Input, model, Output, Signal, signal, type OnInit } from '@angular/core';
import { SettingsUserInfoFormComponent } from '../settings-user-info-form/settings-user-info-form.component';

import { ButtonModule } from 'primeng/button';
import { LoaderDirective } from '../../../directives/loader.directive';
import { ActionableAlertComponent } from '../../actionable-alert/actionable-alert.component';
import {
  IGetProfileInfoResponse,
  IGenderEnum
} from '../../../GeneratedTsFiles';
import { IUserRole } from '../../../models/general.model';
import { AuthStateService } from '../../../services/auth-state.service';
import { GeneralService } from '../../../services/general.service';
import { ParentService } from '../../../services/parent.service';
import { DataApiStateService, State } from '../../../services/data-api-state.service';
import { EventBusService } from '../../../services/event-bus.service';
import { PermissionService } from '../../../services/permission.service';
import { ROUTER_OUTLET_DATA } from '@angular/router';

@Component({
  selector: 'app-settings-account-info-card',
  imports: [
    CommonModule,
    ButtonModule,
    SettingsUserInfoFormComponent,
    ActionableAlertComponent,
    LoaderDirective,
  ],
  templateUrl: './settings-account-info-card.component.html',
  styleUrl: './settings-account-info-card.component.scss',
})
export class SettingsAccountInfoCardComponent implements OnInit {
  @Input() mainTitle: string = 'Settings';
  @Input() showTitleButton: boolean = true;
  @Input() isLoading: boolean = false;
  IUserRole = IUserRole;
  IGenderEnum = IGenderEnum;
  authService = inject(AuthStateService);
  generalService = inject(GeneralService);
  parentService = inject(ParentService);
  dataStateService = inject(DataApiStateService);
  eventBusService = inject(EventBusService);
  permissionService = inject(PermissionService);
  showEditButton = model(true);
  isEditing = model(true);
  profileInfo = model({} as IGetProfileInfoResponse);
  isParentEditingStudent = input(false);
  finalRole = model(this.authService.getUserRole());
  showSettings = model(false);
  cardClass = model('surface-card p-3 shadow-2 border-round relative');
  @Output() editProfileToggle = new EventEmitter<void>();

  data = inject(ROUTER_OUTLET_DATA) as Signal<State<IGetProfileInfoResponse>>;

  userInfo$ = computed(() => this.dataStateService.getUserProfileInfo.state().data || ({} as IGetProfileInfoResponse));

  ngOnInit(): void {
    console.log('ROUTER_OUTLET_DATA data: ', this.data());
    if (this.data() && this.data().data) {
      this.profileInfo.set(this.data().data);
      this.finalRole.set(this.data().data.basicProfileInfoDto.discriminator);
    }
  }

  onEditProfileClick(): void {
    this.showSettings.set(!this.showSettings());
    this.isEditing.set(!this.isEditing());
    this.editProfileToggle.emit();
  }
}