// ============================================================================
// TEACHER CHANGE REQUESTS LIST FILTERS COMPONENT STYLES
// ============================================================================

@use 'mixins';

.teacher-change-requests-filters-container {
  min-height: 100%;
  
  .filter-header {
    border-bottom: 1px solid var(--surface-border);
    padding-bottom: 1rem;
    margin-bottom: 0.5rem;
  }

  .filter-group {
    .filter-label {
      color: var(--text-color);
      font-weight: 500;
    }

    :host ::ng-deep {
      .p-dropdown {
        width: 100%;
        
        .p-dropdown-label {
          padding: 0.75rem;
        }
        
        .p-dropdown-trigger {
          padding: 0.75rem;
        }
      }
      
      .p-dropdown-panel {
        .p-dropdown-items {
          .p-dropdown-item {
            padding: 0.75rem;
            
            &:hover {
              background-color: var(--surface-hover);
            }
          }
        }
      }
    }
  }

  .filter-summary {
    .bg-primary-50 {
      background-color: var(--primary-50);
    }
    
    .border-primary-200 {
      border-color: var(--primary-200);
    }
    
    .text-primary {
      color: var(--primary-color);
    }
  }

  .no-filters-message {
    .bg-surface-50 {
      background-color: var(--surface-50);
    }
    
    .border-surface-200 {
      border-color: var(--surface-200);
    }
  }

  .filter-actions-info {
    .bg-blue-50 {
      background-color: var(--blue-50);
    }
    
    .border-blue-200 {
      border-color: var(--blue-200);
    }
    
    .text-blue-500 {
      color: var(--blue-500);
    }
    
    .text-blue-700 {
      color: var(--blue-700);
    }
  }
}

// Responsive design using mixins


// Status severity styling
:host ::ng-deep {
  .p-tag {
    &.p-tag-success {
      background-color: var(--green-100);
      color: var(--green-800);
    }
    
    &.p-tag-info {
      background-color: var(--blue-100);
      color: var(--blue-800);
    }
    
    &.p-tag-warning {
      background-color: var(--orange-100);
      color: var(--orange-800);
    }
    
    &.p-tag-danger {
      background-color: var(--red-100);
      color: var(--red-800);
    }
  }
}
