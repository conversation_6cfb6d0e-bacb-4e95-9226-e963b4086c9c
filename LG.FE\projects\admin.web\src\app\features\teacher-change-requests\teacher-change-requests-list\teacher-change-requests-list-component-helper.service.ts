// ============================================================================
// TEACHER CHANGE REQUESTS LIST COMPONENT HELPER SERVICE
// ============================================================================

import { Injectable, inject } from '@angular/core';
import { Table } from 'primeng/table';
import { Params } from '@angular/router';
import {
  IGetTeacherChangeRequestRequest,
  IGetTeacherChangeRequestResponse,
  ISearchTeacherChangeRequestDto,
  IDataGridFields,
  nameOf,
  EnumDropdownOptionsService,
  ITeacherChangeRequestStatusEnum,
  IEnumDropdownOptions
} from 'SharedModules.Library';
import moment from 'moment-timezone';
import { BaseListHelperService } from '../../../shared/services/base-list-helper.service';

const ISearchTeacherChangeRequestDtoParamsMap = nameOf<ISearchTeacherChangeRequestDto>();

/**
 * Helper service for teacher change requests list component
 * Provides utilities for request mapping, defaults, column configuration, and export functionality
 * Following the same pattern as GroupsListComponentHelperService
 */
@Injectable({
  providedIn: 'root'
})
export class TeacherChangeRequestsListComponentHelperService extends BaseListHelperService<
  IGetTeacherChangeRequestRequest,
  IGetTeacherChangeRequestResponse,
  ISearchTeacherChangeRequestDto
> {
  enumDropdownOptionsService = inject(EnumDropdownOptionsService);

  // Default sort configuration - centralized in one place
  public static readonly DEFAULT_SORT_COLUMN = ISearchTeacherChangeRequestDtoParamsMap.dateCreated;
  public static readonly DEFAULT_SORT_DIRECTION = 'desc';
  public static readonly DEFAULT_PAGE_SIZE = 10;

  constructor() {
    super();
  }

  // ============================================================================
  // ABSTRACT METHOD IMPLEMENTATIONS - Required by BaseListHelperService
  // ============================================================================

  getDefaultSortColumn(): string {
    return TeacherChangeRequestsListComponentHelperService.DEFAULT_SORT_COLUMN;
  }

  getDefaultSortDirection(): 'asc' | 'desc' {
    return TeacherChangeRequestsListComponentHelperService.DEFAULT_SORT_DIRECTION as 'asc' | 'desc';
  }

  getFieldNames(): { [K in keyof IGetTeacherChangeRequestRequest]: K } {
    return nameOf<IGetTeacherChangeRequestRequest>();
  }

  getDefaultPageSize(): number {
    return TeacherChangeRequestsListComponentHelperService.DEFAULT_PAGE_SIZE;
  }

  getEntityName(): string {
    return 'Teacher Change Requests';
  }

  initializeTableColumns(): IDataGridFields[] {
    const requestFields = nameOf<ISearchTeacherChangeRequestDto>();
    
    return [
      { field: requestFields.requestId, header: 'Request ID', sortable: true, maxWidth: '150px' },
      { field: requestFields.parentFirstName, header: 'Parent Name', sortable: true, maxWidth: '200px' },
      { field: requestFields.teacherToChangeFirstName!, header: 'Current Teacher', sortable: true, maxWidth: '200px' },
      { field: requestFields.newTeacherFirstName!, header: 'New Teacher', sortable: true, maxWidth: '200px' },
      { field: requestFields.studentFirstName!, header: 'Student', sortable: true, maxWidth: '180px' },
      { field: requestFields.groupName!, header: 'Group', sortable: true, maxWidth: '150px' },
      { field: requestFields.teachingLanguageName!, header: 'Language', sortable: true, maxWidth: '120px' },
      { field: requestFields.reason!, header: 'Reason', sortable: true, maxWidth: '250px' },
      { field: requestFields.dateCreated, header: 'Date Created', sortable: true, maxWidth: '140px' },
      { field: requestFields.lastModifiedDate!, header: 'Last Modified', sortable: true, maxWidth: '140px' }
    ];
  }

  /**
   * Export teacher change requests table using base class functionality
   */
  exportTeacherChangeRequestsTable(table: Table, cols: IDataGridFields[], response: IGetTeacherChangeRequestResponse) {
    // Use the base class export functionality
    this.exportTable(table, cols, response);
  }

  /**
   * Formats a single teacher change request record for CSV export
   * This contains all the request-specific formatting logic
   * Implementation of abstract method from BaseListHelperService
   */
  formatItemForExport(request: ISearchTeacherChangeRequestDto, cols: IDataGridFields[]): Record<string, string> {
    const exportObject: Record<string, string> = {};
    const requestFields = nameOf<ISearchTeacherChangeRequestDto>();

    // Initialize all column fields to empty strings to ensure consistent structure
    for (const col of cols) {
      exportObject[col.field] = '';
    }

    // Format each field with proper display values
    if (cols.some(col => col.field === requestFields.requestId)) {
      exportObject[requestFields.requestId] = request.requestId || '';
    }

    if (cols.some(col => col.field === requestFields.parentFirstName)) {
      const parentName = this.getFullName(request.parentFirstName, request.parentLastName);
      exportObject[requestFields.parentFirstName] = parentName;
    }

    if (cols.some(col => col.field === requestFields.teacherToChangeFirstName)) {
      const currentTeacher = this.getFullName(request.teacherToChangeFirstName, request.teacherToChangeLastName);
      exportObject[requestFields.teacherToChangeFirstName!] = currentTeacher;
    }

    if (cols.some(col => col.field === requestFields.newTeacherFirstName)) {
      const newTeacher = this.getFullName(request.newTeacherFirstName, request.newTeacherLastName);
      exportObject[requestFields.newTeacherFirstName!] = newTeacher;
    }

    if (cols.some(col => col.field === requestFields.studentFirstName)) {
      const studentName = this.getFullName(request.studentFirstName, request.studentLastName);
      exportObject[requestFields.studentFirstName!] = studentName;
    }

    if (cols.some(col => col.field === requestFields.groupName)) {
      exportObject[requestFields.groupName!] = request.groupName || '';
    }

    if (cols.some(col => col.field === requestFields.teachingLanguageName)) {
      exportObject[requestFields.teachingLanguageName!] = request.teachingLanguageName || '';
    }

    if (cols.some(col => col.field === requestFields.reason)) {
      exportObject[requestFields.reason!] = request.reason || '';
    }

    if (cols.some(col => col.field === requestFields.dateCreated)) {
      exportObject[requestFields.dateCreated] = this.formatDateForExport(request.dateCreated);
    }

    if (cols.some(col => col.field === requestFields.lastModifiedDate)) {
      exportObject[requestFields.lastModifiedDate!] = this.formatDateForExport(request.lastModifiedDate);
    }

    return exportObject;
  }

  // ============================================================================
  // TEACHER CHANGE REQUESTS SPECIFIC METHODS
  // ============================================================================

  createDefaultRequest(): IGetTeacherChangeRequestRequest {
    const config = this.getDefaultRequestConfig();
    return {
      pageNumber: config.pageNumber!,
      pageSize: config.pageSize!,
      sortColumn: config.sortColumn,
      sortDirection: config.sortDirection,
      searchTerm: null,
      status: null,
    };
  }

  /**
   * Creates a default IGetTeacherChangeRequestRequest object with standard values
   * @returns IGetTeacherChangeRequestRequest with default values
   */
  createDefaultTeacherChangeRequestsRequest(): IGetTeacherChangeRequestRequest {
    return this.createDefaultRequest();
  }

  /**
   * Maps URL query parameters to an IGetTeacherChangeRequestRequest object
   * @param params Route parameters from ActivatedRoute
   * @param defaultRequest Optional default request to use as base (will create one if not provided)
   * @returns IGetTeacherChangeRequestRequest populated with values from URL parameters
   */
  mapQueryParamsToTeacherChangeRequestsRequest(params: Params, defaultRequest?: IGetTeacherChangeRequestRequest): IGetTeacherChangeRequestRequest {
    // Use the base class method for common parameters, then add request-specific ones
    return this.mapQueryParamsToRequest(params, defaultRequest);
  }

  // ============================================================================
  // ENTITY-SPECIFIC PARAMETER MAPPING
  // ============================================================================

  protected override mapEntitySpecificParams(params: Params, request: IGetTeacherChangeRequestRequest): void {
    const fieldNames = nameOf<IGetTeacherChangeRequestRequest>();

    // Map status parameter
    if (params[fieldNames.status!] !== undefined && params[fieldNames.status!] !== null) {
      const statusValue = parseInt(params[fieldNames.status!], 10);
      if (!isNaN(statusValue) && Object.values(ITeacherChangeRequestStatusEnum).includes(statusValue)) {
        request.status = statusValue as ITeacherChangeRequestStatusEnum;
      }
    }
  }

  // ============================================================================
  // HELPER METHODS
  // ============================================================================

  /**
   * Get status options for filtering
   */
  getStatusOptions(): IEnumDropdownOptions[] {
    return this.enumDropdownOptionsService.teacherChangeRequestStatusEnumOptions;
  }

  // ============================================================================
  // FORMATTING HELPERS
  // ============================================================================

  /**
   * Format teacher change request status enum for display
   */
  formatTeacherChangeRequestStatus(status: ITeacherChangeRequestStatusEnum): string {
    const statusOptions = this.enumDropdownOptionsService.teacherChangeRequestStatusEnumOptions;
    const option = statusOptions.find((opt: IEnumDropdownOptions) => opt.value === status);
    return option?.label || status?.toString() || '';
  }

  /**
   * Format full name from first and last name
   */
  getFullName(firstName?: string, lastName?: string): string {
    if (!firstName && !lastName) return '';
    return `${firstName || ''} ${lastName || ''}`.trim();
  }

  /**
   * Format date for display in admin localized format
   */
  formatDateForDisplay(date?: Date | string): string {
    if (!date) return '';
    return moment.utc(date).tz('Europe/Athens').format('DD/MM/YYYY HH:mm');
  }
}
