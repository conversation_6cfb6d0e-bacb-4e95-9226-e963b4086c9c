import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  Output,
  EventEmitter,
  computed,
  ChangeDetectionStrategy,
  OnInit,
  OnChanges,
  inject
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { DropdownModule } from 'primeng/dropdown';
import { SelectModule } from 'primeng/select';
import { MultiSelectModule } from 'primeng/multiselect';
import { CheckboxModule } from 'primeng/checkbox';
import { ButtonModule } from 'primeng/button';
import { SliderModule } from 'primeng/slider';
import { DatePickerModule } from 'primeng/datepicker';
import {
  IGetStudentsRequest,
  ITeachingLanguageDto,
  IUserAccountStatus,
  nameOf,
  EnumDropdownOptionsService,
  GeneralService,
  BaseDataGridFiltersComponent,
  IFilterChangeEvent,
  IFilterActionEvent,
  IBaseFilterState,
  ILanguageLevelsEnum
} from 'SharedModules.Library';

export type IStudentsFilterChangeEvent = IFilterChangeEvent<IGetStudentsRequest>;

export type IStudentsFilterActionEvent = IFilterActionEvent<IGetStudentsRequest>;

/**
 * Interface for filter state data passed from parent
 */
export interface IStudentsFilterState extends IBaseFilterState<IGetStudentsRequest> {
  queryParams: IGetStudentsRequest;
  studentAgesRange: number[];
  teachingLanguages: ITeachingLanguageDto[];
  isFilterOpen: boolean;
}

/**
 * Interface for filter configuration
 */
export interface IStudentsFilterConfig {
  showToggleButton?: boolean;
  defaultOpen?: boolean;
  enableAutoSearch?: boolean;
  searchDebounceMs?: number;
}

/**
 * Students List Filters Component
 *
 * Provides filtering capabilities for the students list including:
 * - Teaching language selection
 * - Account status filtering
 * - Age range filtering
 * - Registration date range
 * - Gender selection
 * - Blocked status filtering
 *
 * Extends BaseDataGridFiltersComponent for common filter functionality
 */
@Component({
  selector: 'app-students-list-filters',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    DropdownModule,
    SelectModule,
    MultiSelectModule,
    CheckboxModule,
    ButtonModule,
    SliderModule,
    DatePickerModule
  ],
  templateUrl: './students-list-filters.component.html',
  styleUrls: ['./students-list-filters.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class StudentsListFiltersComponent
  extends BaseDataGridFiltersComponent<IGetStudentsRequest, IStudentsFilterState, IStudentsFilterConfig>
  implements OnInit, OnChanges {

  // ============================================================================
  // DEPENDENCY INJECTION
  // ============================================================================

  enumDropdownOptionsService = inject(EnumDropdownOptionsService);
  private generalService = inject(GeneralService);

  // ============================================================================
  // FIELD NAMES
  // ============================================================================

  /** Field names for the request object */
  readonly fieldNames = nameOf<IGetStudentsRequest>();

  // ============================================================================
  // COMPUTED PROPERTIES
  // ============================================================================

  /** Current teaching language */
  teachingLanguage = computed(() => this.currentFilters().teachingLanguage || '');

  /** Current teaching language level */
  teachingLanguageLevel = computed(() => this.currentFilters().teachingLanguageLevel || null);

  /** Current account status */
  accountStatus = computed(() => this.currentFilters().accountStatus || null);

  /** Current gender */
  gender = computed(() => this.currentFilters().gender || 0);

  /** Current include blocked status */
  includeBlocked = computed(() => this.currentFilters().includeBlocked || false);

  /** Current student ages range */
  studentAgesRange = computed(() => [
    this.currentFilters().studentAgesMin || 2,
    this.currentFilters().studentAgesMax || 17
  ]);

  /** Current registration date from */
  registeredFrom = computed(() => this.currentFilters().registeredFrom || null);

  /** Current registration date to */
  registeredTo = computed(() => this.currentFilters().registeredTo || null);

  // ============================================================================
  // DROPDOWN OPTIONS
  // ============================================================================

  /** Account status options */
  accountStatusOptions = computed(() => this.enumDropdownOptionsService.userAccountStatusOptions);

  /** Gender options */
  genderOptions = computed(() => this.enumDropdownOptionsService.genderOptions);

  /** Teaching language level options */
  teachingLanguageLevelOptions = computed(() => this.enumDropdownOptionsService.languageLevelsOptions);

  /** Teaching languages from parent state */
  teachingLanguages = computed(() => this.filterState?.teachingLanguages || []);

  // ============================================================================
  // LIFECYCLE METHODS
  // ============================================================================

  override ngOnInit(): void {
    super.ngOnInit();
  }

  override ngOnChanges(): void {
    super.ngOnChanges();
  }

  // ============================================================================
  // FILTER METHODS
  // ============================================================================

  /**
   * Handle teaching language changes
   */
  onTeachingLanguageChange(value: string): void {
    this._tempFilters.update(current => ({
      ...current,
      [this.fieldNames.teachingLanguage!]: value || null,
      // Clear teaching language level when language changes
      [this.fieldNames.teachingLanguageLevel!]: null
    }));
  }

  /**
   * Handle teaching language level changes
   */
  onTeachingLanguageLevelChange(value: ILanguageLevelsEnum): void {
    this._tempFilters.update(current => ({
      ...current,
      [this.fieldNames.teachingLanguageLevel!]: value
    }));
  }

  /**
   * Handle account status changes
   */
  onAccountStatusChange(value: IUserAccountStatus): void {
    this._tempFilters.update(current => ({
      ...current,
      [this.fieldNames.accountStatus!]: value
    }));
  }

  /**
   * Handle gender changes
   */
  onGenderChange(value: number): void {
    this._tempFilters.update(current => ({
      ...current,
      [this.fieldNames.gender!]: value
    }));
  }

  /**
   * Handle include blocked changes
   */
  onIncludeBlockedChange(value: boolean): void {
    this._tempFilters.update(current => ({
      ...current,
      [this.fieldNames.includeBlocked!]: value
    }));
  }

  /**
   * Handle student ages range changes
   */
  onStudentAgesRangeChange(value: number[]): void {
    this._tempFilters.update(current => ({
      ...current,
      [this.fieldNames.studentAgesMin!]: value[0],
      [this.fieldNames.studentAgesMax!]: value[1]
    }));
  }

  /**
   * Handle registration date from changes
   */
  onRegisteredFromChange(value: Date): void {
    this._tempFilters.update(current => ({
      ...current,
      [this.fieldNames.registeredFrom!]: value
    }));
  }

  /**
   * Handle registration date to changes
   */
  onRegisteredToChange(value: Date): void {
    this._tempFilters.update(current => ({
      ...current,
      [this.fieldNames.registeredTo!]: value
    }));
  }

  // ============================================================================
  // RESET METHODS
  // ============================================================================

  /**
   * Public method to reset filters to default state (for drawer reset button)
   * This only resets the temporary filter state, not the applied filters
   */
  resetFiltersToDefault(): void {
    this.resetAllFilters();
  }

  /**
   * Reset all filters to default values
   */
  protected override resetAllFilters(): void {
    this._tempFilters.set({
      [this.fieldNames.teachingLanguage!]: null,
      [this.fieldNames.teachingLanguageLevel!]: null,
      [this.fieldNames.accountStatus!]: null,
      [this.fieldNames.gender!]: 0,
      [this.fieldNames.includeBlocked!]: false,
      [this.fieldNames.studentAgesMin!]: 2,
      [this.fieldNames.studentAgesMax!]: 17,
      [this.fieldNames.registeredFrom!]: null,
      [this.fieldNames.registeredTo!]: null
    });
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  /**
   * Check if teaching language level should be disabled
   */
  isTeachingLanguageLevelDisabled(): boolean {
    return !this.teachingLanguage();
  }
}
