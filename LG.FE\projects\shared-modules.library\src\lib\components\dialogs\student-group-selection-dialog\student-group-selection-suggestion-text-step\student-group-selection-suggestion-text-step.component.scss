@use "mixins";

// Minimal, professional color palette
$primary: #4f46e5;
$primary-50: #eef2ff;
$primary-100: #e0e7ff;
$primary-500: #6366f1;
$primary-600: #4f46e5;
$primary-700: #4338ca;

$success: #059669;
$success-50: #ecfdf5;
$success-100: #d1fae5;

$orange: #ea580c;
$orange-50: #fff7ed;
$orange-100: #ffedd5;

$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;

// Subtle shadows for minimal design
$shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
$shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.06), 0 1px 2px -1px rgba(0, 0, 0, 0.03);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.06), 0 2px 4px -2px rgba(0, 0, 0, 0.03);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.06), 0 4px 6px -4px rgba(0, 0, 0, 0.03);

// Smooth, subtle transitions
$transition-fast: all 0.15s ease-out;
$transition-base: all 0.2s ease-out;
$transition-slow: all 0.3s ease-out;

:host {
  display: block;
}

.benefits-container {
  max-width: 540px;
  margin: 0 auto;
  padding: 1rem;

  @include mixins.breakpoint(mobile) {
    padding: 0.875rem;
  }

  // Hero Section with Planet Graphic
  .hero-section {
    text-align: center;
    margin-bottom: 1.5rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, $gray-50 0%, $primary-50 100%);
    border-radius: 12px;
    border: 1px solid $gray-200;
    position: relative;
    overflow: hidden;

    @include mixins.breakpoint(mobile) {
      margin-bottom: 1.25rem;
      padding: 1.25rem;
    }

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -20%;
      width: 200px;
      height: 200px;
      background: radial-gradient(circle, rgba(79, 70, 229, 0.05) 0%, transparent 70%);
      border-radius: 50%;
    }

    .hero-content {
      position: relative;
      z-index: 1;
      display: flex;
      align-items: center;
      gap: 1.25rem;
      max-width: 460px;
      margin: 0 auto;

      @include mixins.breakpoint(mobile) {
        flex-direction: column;
        gap: 1rem;
      }
    }

    .hero-visual {
      flex-shrink: 0;

      .solar-container {
        width: 52px;
        height: 52px;
        position: relative;

        @include mixins.breakpoint(mobile) {
          width: 44px;
          height: 44px;
        }

        .solar-system {
          width: 100%;
          height: 100%;
          position: relative;

          .central-planet {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 3;

            .planet-core {
              width: 16px;
              height: 16px;
              background: linear-gradient(135deg, $primary 0%, $primary-600 100%);
              border-radius: 50%;
              position: relative;
              z-index: 2;

              @include mixins.breakpoint(mobile) {
                width: 14px;
                height: 14px;
              }
            }

            .planet-glow {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              width: 24px;
              height: 24px;
              background: radial-gradient(circle, rgba(79, 70, 229, 0.15) 0%, transparent 70%);
              border-radius: 50%;
              animation: planet-glow 4s ease-in-out infinite;
              z-index: 1;

              @include mixins.breakpoint(mobile) {
                width: 20px;
                height: 20px;
              }
            }
          }

          .orbit-ring {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border-radius: 50%;
            border: 1px solid rgba(79, 70, 229, 0.08);

            .orbiting-planet {
              position: absolute;
              border-radius: 50%;
              top: 0;
              left: 50%;
              transform: translateX(-50%);
            }

            &.orbit-1 {
              width: 32px;
              height: 32px;
              animation: orbit-rotation 8s linear infinite;

              @include mixins.breakpoint(mobile) {
                width: 28px;
                height: 28px;
              }

              .planet-1 {
                width: 4px;
                height: 4px;
                background: $success;
                top: -2px;
                box-shadow: 0 0 4px rgba(5, 150, 105, 0.4);

                @include mixins.breakpoint(mobile) {
                  width: 3px;
                  height: 3px;
                  top: -1.5px;
                }
              }
            }

            &.orbit-2 {
              width: 42px;
              height: 42px;
              animation: orbit-rotation 12s linear infinite reverse;

              @include mixins.breakpoint(mobile) {
                width: 36px;
                height: 36px;
              }

              .planet-2 {
                width: 3px;
                height: 3px;
                background: $orange;
                top: -1.5px;
                box-shadow: 0 0 3px rgba(234, 88, 12, 0.4);

                @include mixins.breakpoint(mobile) {
                  width: 2.5px;
                  height: 2.5px;
                  top: -1.25px;
                }
              }
            }

            &.orbit-3 {
              width: 50px;
              height: 50px;
              animation: orbit-rotation 16s linear infinite;

              @include mixins.breakpoint(mobile) {
                width: 42px;
                height: 42px;
              }

              .planet-3 {
                width: 3px;
                height: 3px;
                background: rgba(79, 70, 229, 0.7);
                top: -1.5px;
                box-shadow: 0 0 3px rgba(79, 70, 229, 0.3);

                @include mixins.breakpoint(mobile) {
                  width: 2.5px;
                  height: 2.5px;
                  top: -1.25px;
                }
              }
            }
          }
        }
      }
    }

    .hero-text {
      flex: 1;
      text-align: left;

      @include mixins.breakpoint(mobile) {
        text-align: center;
      }

      .hero-title {
        font-size: 1.375rem;
        font-weight: 700;
        color: $gray-900;
        margin: 0 0 0.5rem;
        letter-spacing: -0.025em;
        line-height: 1.2;

        @include mixins.breakpoint(mobile) {
          font-size: 1.25rem;
          margin-bottom: 0.375rem;
        }
      }

      .hero-subtitle {
        font-size: 0.875rem;
        color: $gray-600;
        margin: 0;
        line-height: 1.5;

        @include mixins.breakpoint(mobile) {
          font-size: 0.8125rem;
        }
      }
    }
  }

  // Benefits Section
  .benefits-section {
    margin-bottom: 1.5rem;

    @include mixins.breakpoint(mobile) {
      margin-bottom: 1.25rem;
    }

    .benefits-title {
      font-size: 1.125rem;
      font-weight: 600;
      color: $gray-900;
      text-align: center;
      margin: 0 0 1.25rem;

      @include mixins.breakpoint(mobile) {
        font-size: 1rem;
        margin-bottom: 1rem;
      }
    }

    .benefits-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 1rem;

      @include mixins.breakpoint(mobile) {
        grid-template-columns: 1fr;
        gap: 0.875rem;
      }

      .benefit-card {
        background: white;
        border-radius: 10px;
        padding: 1rem;
        border: 1px solid $gray-200;
        transition: $transition-base;
        display: flex;
        align-items: flex-start;
        gap: 0.875rem;

        @include mixins.breakpoint(mobile) {
          padding: 0.875rem;
          gap: 0.75rem;
        }

        &:hover {
          border-color: $primary-100;
          box-shadow: $shadow-sm;
          transform: translateY(-1px);
        }

        .benefit-icon {
          width: 36px;
          height: 36px;
          background: $primary-50;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: $primary;
          font-size: 1rem;
          flex-shrink: 0;

          @include mixins.breakpoint(mobile) {
            width: 32px;
            height: 32px;
            font-size: 0.9375rem;
          }
        }

        .benefit-content {
          flex: 1;

          .benefit-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: $gray-900;
            margin: 0 0 0.25rem;
            line-height: 1.3;

            @include mixins.breakpoint(mobile) {
              font-size: 0.8125rem;
            }
          }

          .benefit-desc {
            font-size: 0.75rem;
            color: $gray-600;
            margin: 0;
            line-height: 1.4;

            @include mixins.breakpoint(mobile) {
              font-size: 0.6875rem;
            }
          }
        }
      }
    }
  }

  // Tips Section
  .tips-section {
    margin-bottom: 1.5rem;

    @include mixins.breakpoint(mobile) {
      margin-bottom: 1.25rem;
    }

    .tips-title {
      font-size: 1.125rem;
      font-weight: 600;
      color: $gray-900;
      text-align: center;
      margin: 0 0 1rem;

      @include mixins.breakpoint(mobile) {
        font-size: 1rem;
        margin-bottom: 0.875rem;
      }
    }

    .tips-list {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;

      @include mixins.breakpoint(mobile) {
        gap: 0.625rem;
      }

      .tip-item {
        background: $gray-50;
        border-radius: 8px;
        padding: 0.875rem;
        border: 1px solid $gray-200;
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        transition: $transition-base;

        @include mixins.breakpoint(mobile) {
          padding: 0.75rem;
          gap: 0.625rem;
        }

        &:hover {
          background: white;
          border-color: $primary-100;
        }

        .tip-icon {
          width: 28px;
          height: 28px;
          background: white;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: $primary;
          font-size: 0.875rem;
          flex-shrink: 0;
          border: 1px solid $gray-200;

          @include mixins.breakpoint(mobile) {
            width: 24px;
            height: 24px;
            font-size: 0.8125rem;
          }
        }

        .tip-content {
          flex: 1;

          .tip-title {
            font-size: 0.8125rem;
            font-weight: 600;
            color: $gray-900;
            margin: 0 0 0.1875rem;

            @include mixins.breakpoint(mobile) {
              font-size: 0.75rem;
            }
          }

          .tip-desc {
            font-size: 0.6875rem;
            color: $gray-600;
            margin: 0;
            line-height: 1.4;

            @include mixins.breakpoint(mobile) {
              font-size: 0.625rem;
            }
          }
        }
      }
    }
  }

  // Selection Section
  .selection-section {
    background: linear-gradient(135deg, $primary 0%, $primary-600 100%);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: $shadow-md;

    @include mixins.breakpoint(mobile) {
      padding: 1.25rem;
    }

    .selection-content {
      max-width: 380px;
      margin: 0 auto;

      .selection-header {
        margin-bottom: 1.25rem;

        @include mixins.breakpoint(mobile) {
          margin-bottom: 1rem;
        }

        .selection-title {
          font-size: 1.25rem;
          font-weight: 700;
          color: white;
          margin: 0 0 0.5rem;
          letter-spacing: -0.025em;

          @include mixins.breakpoint(mobile) {
            font-size: 1.125rem;
            margin-bottom: 0.375rem;
          }
        }

        .selection-subtitle {
          font-size: 0.875rem;
          color: rgba(255, 255, 255, 0.9);
          margin: 0;
          line-height: 1.5;

          @include mixins.breakpoint(mobile) {
            font-size: 0.8125rem;
          }
        }
      }

      .selection-action {
        .selection-note {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.375rem;
          margin-top: 0.875rem;
          font-size: 0.75rem;
          color: rgba(255, 255, 255, 0.8);
          font-weight: 500;

          @include mixins.breakpoint(mobile) {
            font-size: 0.6875rem;
            margin-top: 0.75rem;
          }

          i {
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.8125rem;

            @include mixins.breakpoint(mobile) {
              font-size: 0.75rem;
            }
          }
        }
      }
    }
  }
}

// Solar System Animations
@keyframes planet-glow {
  0%, 100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

@keyframes orbit-rotation {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .solar-system {
    .planet-glow {
      animation: none;
      opacity: 0.8;
      transform: translate(-50%, -50%) scale(1);
    }

    .orbit-ring {
      animation: none;
    }
  }
}

// Modern Button Styles
::ng-deep {
  .selection-button {
    background: white !important;
    border: 2px solid white !important;
    color: $primary !important;
    font-weight: 600 !important;
    padding: 0.875rem 1.75rem !important;
    border-radius: 10px !important;
    transition: $transition-base !important;
    font-size: 0.875rem !important;
    box-shadow: $shadow-md !important;

    @include mixins.breakpoint(mobile) {
      padding: 0.75rem 1.5rem !important;
      font-size: 0.8125rem !important;
    }

    &:hover {
      background: rgba(255, 255, 255, 0.95) !important;
      border-color: rgba(255, 255, 255, 0.95) !important;
      color: $primary-600 !important;
      transform: translateY(-2px) !important;
      box-shadow: $shadow-lg !important;
    }

    &:active {
      transform: translateY(-1px) !important;
    }

    &:focus {
      box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3) !important;
    }

    .p-button-icon {
      color: $primary !important;
      margin-right: 0.5rem !important;
      transition: $transition-base !important;
      font-size: 0.875rem !important;

      @include mixins.breakpoint(mobile) {
        font-size: 0.8125rem !important;
      }
    }

    &:hover .p-button-icon {
      color: $primary-600 !important;
    }

    .p-button-label {
      color: $primary !important;
      font-weight: 600 !important;
    }

    &:hover .p-button-label {
      color: $primary-600 !important;
    }
  }

  .cta-button {
    background: white !important;
    border: 2px solid white !important;
    color: $primary !important;
    font-weight: 600 !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: 8px !important;
    transition: $transition-base !important;
    font-size: 0.8125rem !important;
    box-shadow: $shadow-sm !important;

    @include mixins.breakpoint(mobile) {
      padding: 0.625rem 1.25rem !important;
      font-size: 0.75rem !important;
    }

    &:hover {
      background: rgba(255, 255, 255, 0.95) !important;
      border-color: rgba(255, 255, 255, 0.95) !important;
      color: $primary-600 !important;
      transform: translateY(-1px) !important;
      box-shadow: $shadow-md !important;
    }

    &:active {
      transform: translateY(0) !important;
    }

    &:focus {
      box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3) !important;
    }

    .p-button-icon {
      color: $primary !important;
      margin-left: 0.375rem !important;
      transition: $transition-base !important;
      font-size: 0.75rem !important;

      @include mixins.breakpoint(mobile) {
        font-size: 0.6875rem !important;
      }
    }

    &:hover .p-button-icon {
      transform: translateX(2px) !important;
      color: $primary-600 !important;
    }

    .p-button-label {
      color: $primary !important;
      font-weight: 600 !important;
    }

    &:hover .p-button-label {
      color: $primary-600 !important;
    }
  }
}