// ============================================================================
// TEACHER CHANGE REQUESTS LIST FILTERS COMPONENT
// ============================================================================

import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  inject,
  Input,
  OnChanges,
  OnInit,
  signal,
  computed,
  AfterViewInit,
} from '@angular/core';
import { FormsModule } from '@angular/forms';

// === PRIMENG IMPORTS ===
import { ButtonModule } from 'primeng/button';
import { CheckboxModule } from 'primeng/checkbox';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { MultiSelectModule } from 'primeng/multiselect';
import { TagModule } from 'primeng/tag';

// === SHARED LIBRARY IMPORTS ===
import {
  IGetTeacherChangeRequestRequest,
  ITeacherChangeRequestStatusEnum,
  nameOf,
  EnumDropdownOptionsService,
  IEnumDropdownOptions,
  BaseDataGridFiltersComponent,
  IFilterChangeEvent,
  IBaseFilterState,
} from 'SharedModules.Library';

// ============================================================================
// INTERFACES
// ============================================================================
export type ITeacherChangeRequestsFilterChangeEvent = IFilterChangeEvent<IGetTeacherChangeRequestRequest>;

/**
 * Interface for filter state data passed from parent
 */
export interface ITeacherChangeRequestsFilterState extends IBaseFilterState<IGetTeacherChangeRequestRequest> {
  queryParams: IGetTeacherChangeRequestRequest;
  isFilterOpen: boolean;
}

/**
 * Interface for filter configuration
 */
export interface ITeacherChangeRequestsFilterConfig {
  enableAutoSearch?: boolean;
  searchDebounceMs?: number;
}

// ============================================================================
// COMPONENT DEFINITION
// ============================================================================

/**
 * Teacher Change Requests List Filters Component
 * 
 * Provides filtering capabilities for the teacher change requests list including:
 * - Filter by status (Pending, Approved, Rejected, Cancelled)
 * - Search functionality
 * 
 * Designed to work within the filters drawer sidebar for full height support
 * Uses temporary state management to prevent unsaved changes from being applied
 */
@Component({
  selector: 'app-teacher-change-requests-list-filters',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    CheckboxModule,
    DropdownModule,
    InputTextModule,
    MultiSelectModule,
    TagModule,
  ],
  templateUrl: './teacher-change-requests-list-filters.component.html',
  styleUrls: ['./teacher-change-requests-list-filters.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TeacherChangeRequestsListFiltersComponent extends BaseDataGridFiltersComponent<
  IGetTeacherChangeRequestRequest,
  ITeacherChangeRequestsFilterState,
  ITeacherChangeRequestsFilterConfig
> implements OnInit, OnChanges, AfterViewInit {
  enumDropdownOptionsService = inject(EnumDropdownOptionsService);

  /**
   * Filter state data from parent component
   */
  @Input() override filterState: ITeacherChangeRequestsFilterState = {
    queryParams: {} as IGetTeacherChangeRequestRequest,
    isFilterOpen: true
  };

  // ============================================================================
  // COMPONENT STATE
  // ============================================================================

  // Computed properties that merge temp + current state
  override readonly currentFilters = computed(() => ({ ...this.filterState.queryParams, ...this._tempFilters() }));

  // Filter form signals - prioritize temp filters over applied filters for UI display
  readonly selectedStatus = computed(() => {
    const tempValue = this._tempFilters().status;
    return tempValue !== undefined ? tempValue : (this.filterState.queryParams.status ?? null);
  });

  // Reset signal for components
  resetSelectionSignal = signal(false);

  // Flag to prevent event emissions during reset
  private isResetting = signal(false);

  // Dropdown options
  statusOptions = signal<IEnumDropdownOptions[]>([]);

  // Field name references for type safety
  private readonly fieldNames = nameOf<IGetTeacherChangeRequestRequest>();

  // ============================================================================
  // LIFECYCLE HOOKS
  // ============================================================================

  override ngOnInit(): void {
    this.initializeDropdownOptions();
    super.ngOnInit();
  }

  override ngOnChanges(): void {
    super.ngOnChanges();
  }

  ngAfterViewInit(): void {
    // Any post-view initialization if needed
  }

  // ============================================================================
  // INITIALIZATION
  // ============================================================================

  private initializeDropdownOptions(): void {
    // Initialize status options
    this.statusOptions.set(this.enumDropdownOptionsService.teacherChangeRequestStatusEnumOptions);
  }

  // ============================================================================
  // FILTER CHANGE HANDLERS
  // ============================================================================

  /**
   * Handle status filter changes
   */
  onStatusChange(status: ITeacherChangeRequestStatusEnum | null): void {
    if (this.isResetting()) return;

    console.log('Status filter changed:', status);
    this._tempFilters.update(current => ({
      ...current,
      [this.fieldNames.status!]: status
    }));
  }

  // ============================================================================
  // RESET FUNCTIONALITY
  // ============================================================================

  /**
   * Reset all filters to default values
   * Implementation of abstract method from BaseDataGridFiltersComponent
   */
  protected override resetAllFilters(): void {
    this.isResetting.set(true);

    // Reset all filter values to defaults
    this._tempFilters.set({
      [this.fieldNames.status!]: null,
    });

    // Trigger reset signal for any child components
    this.resetSelectionSignal.set(true);
    
    // Reset the signal after a brief delay
    setTimeout(() => {
      this.resetSelectionSignal.set(false);
      this.isResetting.set(false);
    }, 100);
  }

  /**
   * Public method to reset filters to default state (for drawer reset button)
   * This only resets the temporary filter state, not the applied filters
   */
  resetFiltersToDefault(): void {
    this.resetAllFilters();
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  /**
   * Get status label for display
   */
  getStatusLabel(status: ITeacherChangeRequestStatusEnum): string {
    return this.enumDropdownOptionsService.teacherChangeRequestStatusEnumOptions
      .find(option => option.value === status)?.label || 'Unknown';
  }

  /**
   * Check if any filters are applied
   */
  hasActiveFilters(): boolean {
    const current = this.currentFilters();
    return !!(current.status !== null && current.status !== undefined);
  }

  /**
   * Get count of active filters
   */
  getActiveFiltersCount(): number {
    let count = 0;
    const current = this.currentFilters();

    if (current.status !== null && current.status !== undefined) count++;

    return count;
  }

  /**
   * Get status severity for PrimeNG tags
   */
  getStatusSeverity(status: ITeacherChangeRequestStatusEnum): 'success' | 'info' | 'warn' | 'danger' {
    switch (status) {
      case ITeacherChangeRequestStatusEnum.Approved:
        return 'success';
      case ITeacherChangeRequestStatusEnum.Pending:
        return 'info';
      case ITeacherChangeRequestStatusEnum.Rejected:
        return 'danger';
      case ITeacherChangeRequestStatusEnum.Cancelled:
        return 'warn';
      default:
        return 'info';
    }
  }
}
