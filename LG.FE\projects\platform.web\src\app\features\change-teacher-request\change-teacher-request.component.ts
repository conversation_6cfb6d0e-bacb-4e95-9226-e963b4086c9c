import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, signal, computed, OnInit, ViewChild, AfterViewInit, inject } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

// PrimeNG Imports
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { RadioButtonModule } from 'primeng/radiobutton';

// Shared Components
import { DynamicProgressStepsComponent, StepDefinition, ProcessedStep } from '../../shared/components/dynamic-progress-steps/dynamic-progress-steps.component';

// Shared Library (imports will be added as needed)
// import { GeneralService } from 'SharedModules.Library';

// Local Interfaces
import {
  TeacherChangeType,
  ChangeTeacherStep
} from './interfaces/change-teacher-request.interfaces';
import { CardSplitLayoutComponent, LoaderDirective, ICreateTeacherChangeRequestRequest, IBasicProfileInfoDto, IAffectedStudentPackageDto } from 'SharedModules.Library';
import { GeneralService } from 'SharedModules.Library';

// Child Components
import { CompleteTeacherChangeStepComponent, ICompleteTeacherChangeStepData } from './components/complete-teacher-change-step/complete-teacher-change-step.component';
import { StudentGroupSelectionStepComponent, IStudentGroupSelectionStepData } from './components/student-group-selection-step/student-group-selection-step.component';
import { TeacherChangeReviewStepComponent } from './components/teacher-change-review-step/teacher-change-review-step.component';

// Services
import { TeacherChangeRequestService } from '../../shared/services/teacher-change-request.service';

@Component({
  selector: 'app-change-teacher-request',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ButtonModule,
    CardModule,
    RadioButtonModule,
    DynamicProgressStepsComponent,
    CardSplitLayoutComponent,
    LoaderDirective,
    CompleteTeacherChangeStepComponent,
    StudentGroupSelectionStepComponent,
    TeacherChangeReviewStepComponent
  ],
  templateUrl: './change-teacher-request.component.html',
  styleUrl: './change-teacher-request.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ChangeTeacherRequestComponent implements OnInit, AfterViewInit {
  // ViewChild to access the progress steps component
  @ViewChild('progressSteps') progressStepsComponent!: DynamicProgressStepsComponent;

  // Injected services (keeping for future use)
  // private readonly router = inject(Router);
  // private readonly generalService = inject(GeneralService);

  // Enums for template
  readonly TeacherChangeType = TeacherChangeType;
  readonly ChangeTeacherStep = ChangeTeacherStep;

  readonly generalService = inject(GeneralService);
  private readonly teacherChangeRequestService = inject(TeacherChangeRequestService);

  // ViewChild references
  @ViewChild(TeacherChangeReviewStepComponent) reviewStepComponent?: TeacherChangeReviewStepComponent;

  // Component state signals
  currentStep = signal<ChangeTeacherStep>(ChangeTeacherStep.SELECTION);
  selectedChangeType = signal<TeacherChangeType | null>(TeacherChangeType.COMPLETE_CHANGE);

  // Teacher change request data - using API contract interface
  teacherChangeRequest = signal<Partial<ICreateTeacherChangeRequestRequest> | undefined>(undefined);
  requestValid = signal<boolean>(false);

  // Student/Group selection data (for STUDENT_GROUP_CHANGE type)
  studentGroupSelectionData = signal<IStudentGroupSelectionStepData | undefined>(undefined);
  studentGroupSelectionValid = signal<boolean>(false);

  // Additional data for UI display
  selectedTeacher = signal<IBasicProfileInfoDto | undefined>(undefined);
  affectedResources = signal<IAffectedStudentPackageDto[]>([]);

  // Submission state
  isSubmitting = signal<boolean>(false);
  submissionError = signal<string | null>(null);
  submissionSuccess = signal<boolean>(false);

  // Base step definitions
  private readonly baseStepDefinitions: StepDefinition[] = [
    {
      id: 1,
      route: 'selection',
      title: 'Teacher Change Selection',
      shortTitle: 'Selection',
      description: 'Choose the type of teacher change you need',
      icon: 'pi pi-user-edit'
    },
    {
      id: 2,
      route: 'student-group-selection',
      title: 'Student/Group Selection',
      shortTitle: 'Student/Group',
      description: 'Select the student or group for the teacher change',
      icon: 'pi pi-users'
    },
    {
      id: 3,
      route: 'details',
      title: 'Change Details',
      shortTitle: 'Details',
      description: 'Provide details for your teacher change request',
      icon: 'pi pi-info-circle'
    },
    {
      id: 4,
      route: 'review-confirm',
      title: 'Review & Confirm',
      shortTitle: 'Review & Confirm',
      description: 'Review the impact and confirm your teacher change request',
      icon: 'pi pi-check-circle'
    }
  ];

  // Step definitions for progress component
  readonly stepDefinitions: StepDefinition[] = this.baseStepDefinitions;

  // Current route for the progress component
  currentRouteForProgress = computed(() => {
    const currentStepValue = this.currentStep();
    const step = this.baseStepDefinitions.find(s => s.id === currentStepValue);
    return step ? step.route : 'selection';
  });

  // Computed properties
  canProceedToNextStep = computed(() => {
    switch (this.currentStep()) {
      case ChangeTeacherStep.SELECTION:
        return this.selectedChangeType() !== null;
      case ChangeTeacherStep.STUDENT_GROUP_SELECTION:
        // Only show this step for STUDENT_GROUP_CHANGE type
        if (this.selectedChangeType() === TeacherChangeType.STUDENT_GROUP_CHANGE) {
          return this.studentGroupSelectionValid();
        }
        return true; // Skip this step for COMPLETE_CHANGE
      case ChangeTeacherStep.DETAILS:
        // For complete change, check if request is valid
        if (this.selectedChangeType() === TeacherChangeType.COMPLETE_CHANGE) {
          return this.requestValid();
        }
        // For student/group change, check if both student/group and teacher are selected
        return this.requestValid();
      case ChangeTeacherStep.REVIEW_CONFIRM:
        // Can proceed (submit) from review & confirm if we have valid request data
        return this.teacherChangeRequest() !== undefined && this.requestValid();
      default:
        return false;
    }
  });

  isFirstStep = computed(() => this.currentStep() === ChangeTeacherStep.SELECTION);
  isLastStep = computed(() => this.currentStep() === ChangeTeacherStep.REVIEW_CONFIRM);

  // Helper to determine if student/group selection step should be shown
  shouldShowStudentGroupStep = computed(() => {
    return this.selectedChangeType() === TeacherChangeType.STUDENT_GROUP_CHANGE;
  });

  // Dynamic button labels and actions
  getPreviousButtonLabel(): string {
    switch (this.currentStep()) {
      case ChangeTeacherStep.STUDENT_GROUP_SELECTION:
        return 'Back';
      case ChangeTeacherStep.DETAILS:
        return 'Back';
      case ChangeTeacherStep.REVIEW_CONFIRM:
        return 'Back';
      default:
        return 'Previous';
    }
  }

  getNextButtonLabel(): string {
    switch (this.currentStep()) {
      case ChangeTeacherStep.SELECTION:
        return 'Continue';
      case ChangeTeacherStep.STUDENT_GROUP_SELECTION:
        return 'Continue';
      case ChangeTeacherStep.DETAILS:
        return 'Review & Confirm';
      case ChangeTeacherStep.REVIEW_CONFIRM:
        return 'Submit Request';
      default:
        return 'Next';
    }
  }

  getNextButtonIcon(): string {
    switch (this.currentStep()) {
      case ChangeTeacherStep.REVIEW_CONFIRM:
        return 'pi pi-send';
      default:
        return 'pi pi-chevron-right';
    }
  }

  getNextButtonClass(): string {
    switch (this.currentStep()) {
      case ChangeTeacherStep.REVIEW_CONFIRM:
        return 'w-full md:w-auto';
      default:
        return 'w-full md:w-auto';
    }
  }

  handleNextAction(): void {
    if (this.currentStep() === ChangeTeacherStep.REVIEW_CONFIRM) {
      // Submit the request directly from the review & confirm step
      this.submitTeacherChangeRequest();
    } else {
      this.nextStep();
    }
  }

  ngOnInit(): void {
    // Initialize component
    console.log('Change Teacher Request component initialized');
  }

  ngAfterViewInit(): void {
    // Update the progress component after view initialization
    this.updateProgressComponent();
  }

  // Method to manually update the progress component's internal state
  private updateProgressComponent(): void {
    if (this.progressStepsComponent) {
      const currentStepValue = this.currentStep();
      const currentStepDef = this.stepDefinitions.find(s => s.id === currentStepValue);

      if (currentStepDef) {
        // Manually set the current route and step index in the progress component
        const progressComponent = this.progressStepsComponent as any;

        if (progressComponent.currentRoute?.set) {
          progressComponent.currentRoute.set(currentStepDef.route);
        }

        if (progressComponent.currentStepIndex?.set) {
          // If submission is successful, set the step index to the total number of steps
          // This will mark all steps as completed in the progress component
          if (this.submissionSuccess()) {
            progressComponent.currentStepIndex.set(this.stepDefinitions.length);
          } else {
            progressComponent.currentStepIndex.set(currentStepValue - 1);
          }
        }
      }
    }
  }

  // Navigation methods
  handleStepNavigation = (route: string): void => {
    // Handle step navigation without changing routes
    const step = this.stepDefinitions.find((s: StepDefinition) => s.route === route);
    if (step) {
      const targetStep = step.id as ChangeTeacherStep;

      // Only allow navigation to accessible steps
      if (this.isStepAccessible(targetStep)) {
        this.currentStep.set(targetStep);
        this.updateProgressComponent();
      }
    }
  };

  // Helper method to determine if a step is accessible
  private isStepAccessible(step: ChangeTeacherStep): boolean {
    switch (step) {
      case ChangeTeacherStep.SELECTION:
        return true; // Always accessible
      case ChangeTeacherStep.STUDENT_GROUP_SELECTION:
        // Only accessible for STUDENT_GROUP_CHANGE type and if change type is selected
        return this.selectedChangeType() === TeacherChangeType.STUDENT_GROUP_CHANGE;
      case ChangeTeacherStep.DETAILS:
        // Accessible if change type is selected and (for student/group change) selection is made
        if (this.selectedChangeType() === TeacherChangeType.COMPLETE_CHANGE) {
          return true; // Just need change type selected
        } else if (this.selectedChangeType() === TeacherChangeType.STUDENT_GROUP_CHANGE) {
          return this.studentGroupSelectionValid(); // Need student/group selected
        }
        return false;
      case ChangeTeacherStep.REVIEW_CONFIRM:
        // Accessible if details step is completed
        if (this.selectedChangeType() === TeacherChangeType.COMPLETE_CHANGE) {
          return this.requestValid();
        } else if (this.selectedChangeType() === TeacherChangeType.STUDENT_GROUP_CHANGE) {
          return this.requestValid() && this.studentGroupSelectionValid();
        }
        return false;
      default:
        return false;
    }
  }

  nextStep(): void {
    if (this.canProceedToNextStep() && !this.isLastStep()) {
      const currentStepValue = this.currentStep();
      let nextStepValue = currentStepValue + 1;

      // Skip student/group selection step for COMPLETE_CHANGE
      if (nextStepValue === ChangeTeacherStep.STUDENT_GROUP_SELECTION &&
          this.selectedChangeType() === TeacherChangeType.COMPLETE_CHANGE) {
        nextStepValue = ChangeTeacherStep.DETAILS;
      }

      // Teachers will be loaded by the complete-teacher-change-step component
      // with the correct parameters when it initializes

      this.currentStep.set(nextStepValue as ChangeTeacherStep);
      this.updateProgressComponent();
    }
  }

  previousStep(): void {
    if (!this.isFirstStep()) {
      let previousStepValue = this.currentStep() - 1;

      // Skip student/group selection step for COMPLETE_CHANGE when going back
      if (previousStepValue === ChangeTeacherStep.STUDENT_GROUP_SELECTION &&
          this.selectedChangeType() === TeacherChangeType.COMPLETE_CHANGE) {
        previousStepValue = ChangeTeacherStep.SELECTION;
      }

      this.currentStep.set(previousStepValue as ChangeTeacherStep);
      this.updateProgressComponent();
    }
  }

  // Selection methods
  onChangeTypeSelect(changeType: TeacherChangeType): void {
    this.selectedChangeType.set(changeType);
  }

  // Step event handlers
  onStepChanged(event: { currentStep: ProcessedStep; stepIndex: number }): void {
    console.log('Step changed to:', event.currentStep.title);
    // Only sync if the step is different to avoid infinite loops
    const newStep = event.currentStep.id as ChangeTeacherStep;
    if (this.currentStep() !== newStep) {
      this.currentStep.set(newStep);
    }
  }

  onNavigationAttempted(event: { targetStep: ProcessedStep; allowed: boolean }): void {
    if (!event.allowed) {
      console.log('Navigation blocked to step:', event.targetStep.title);
      // Could show a toast message here in the future
    } else {
      console.log('Navigation allowed to step:', event.targetStep.title);
    }
  }

  // Helper methods for template
  getInitialStepData(): ICompleteTeacherChangeStepData | undefined {
    const request = this.teacherChangeRequest();
    const teacher = this.selectedTeacher();

    if (!request) return undefined;

    return {
      teacherId: request.teacherId,
      teacher: teacher,
      reason: request.reason
    };
  }

  getReviewStepData(): ICompleteTeacherChangeStepData | undefined {
    const request = this.teacherChangeRequest();
    const teacher = this.selectedTeacher();

    if (!request) return undefined;

    return {
      teacherId: request.teacherId,
      teacher: teacher,
      reason: request.reason
    };
  }

  // Step 2/3 event handlers (Details step)
  onCompleteChangeStepDataChanged(data: { teacherId?: string; teacher?: IBasicProfileInfoDto; reason?: string }): void {
    console.log('Complete change step data changed:', data);

    // Create base request using service
    const baseRequest = this.teacherChangeRequestService.createBaseRequest();

    // Update the request with new data
    let updatedRequest: Partial<ICreateTeacherChangeRequestRequest> = {
      ...baseRequest,
      teacherId: data.teacherId,
      reason: data.reason
    };

    // If this is a student/group change, add the selection data
    if (this.selectedChangeType() === TeacherChangeType.STUDENT_GROUP_CHANGE) {
      const selectionData = this.studentGroupSelectionData();
      if (selectionData) {
        updatedRequest = {
          ...updatedRequest,
          studentId: selectionData.studentId,
          groupId: selectionData.groupId,
          // Add teaching language ID from selection data
          teachingLanguageId: selectionData.teachingLanguageId
        };
      }
    }

    this.teacherChangeRequest.set(updatedRequest);
    this.selectedTeacher.set(data.teacher);

    // Validate the request
    const validation = this.teacherChangeRequestService.validateRequest(updatedRequest);
    this.requestValid.set(validation.isValid);
  }

  onCompleteChangeStepValidationChanged(isValid: boolean): void {
    console.log('Complete change step validation changed:', isValid);
    this.requestValid.set(isValid);
  }

  // Student/Group selection step event handlers
  onStudentGroupSelectionDataChanged(data: IStudentGroupSelectionStepData): void {
    console.log('Student/Group selection data changed:', data);
    this.studentGroupSelectionData.set(data);
  }

  onStudentGroupSelectionValidationChanged(isValid: boolean): void {
    console.log('Student/Group selection validation changed:', isValid);
    this.studentGroupSelectionValid.set(isValid);
  }



  // Review step event handlers
  onReviewStepConfirm(reviewData: { request: ICreateTeacherChangeRequestRequest; affectedResources: IAffectedStudentPackageDto[] }): void {
    console.log('Review step confirmed:', reviewData);
    this.teacherChangeRequest.set(reviewData.request);
    this.affectedResources.set(reviewData.affectedResources);
    // Proceed to confirmation step
    this.nextStep();
  }



  // Confirmation step methods
  submitTeacherChangeRequest(): void {
    if (this.isSubmitting()) {
      return; // Prevent double submission
    }

    const changeType = this.selectedChangeType();
    const requestData = this.teacherChangeRequest();

    if (!changeType || !requestData) {
      this.submissionError.set('Missing required data for submission. Please ensure you have selected a teacher and provided a reason.');
      return;
    }

    // Validate the request one more time before submission
    const validation = this.teacherChangeRequestService.validateRequest(requestData);
    if (!validation.isValid) {
      this.submissionError.set(`Invalid request data: ${validation.errors.join(', ')}`);
      return;
    }

    this.isSubmitting.set(true);
    this.submissionError.set(null);

    // Submit using the appropriate service method based on change type
    const submitObservable = changeType === TeacherChangeType.COMPLETE_CHANGE
      ? this.teacherChangeRequestService.createCompleteTeacherChangeRequest(requestData as ICreateTeacherChangeRequestRequest)
      : this.teacherChangeRequestService.createStudentGroupTeacherChangeRequest(requestData as ICreateTeacherChangeRequestRequest);

    submitObservable.subscribe({
      next: (response) => {
        console.log('Teacher change request submitted successfully:', response);
        this.submissionSuccess.set(true);
        this.isSubmitting.set(false);

        // Update progress component to show completion
        this.updateProgressComponent();

        // Could navigate to a success page or show success message
        // this.router.navigate(['/dashboard/teacher-change-success']);
      },
      error: (error) => {
        console.error('Error submitting teacher change request:', error);
        this.submissionError.set(error?.message || 'Failed to submit teacher change request');
        this.isSubmitting.set(false);
      }
    });
  }

  resetSubmissionState(): void {
    this.isSubmitting.set(false);
    this.submissionError.set(null);
    this.submissionSuccess.set(false);
  }

  startAgain(): void {
    // Reset all component state
    this.currentStep.set(ChangeTeacherStep.SELECTION);
    this.selectedChangeType.set(TeacherChangeType.COMPLETE_CHANGE);
    this.teacherChangeRequest.set(undefined);
    this.requestValid.set(false);
    this.studentGroupSelectionData.set(undefined);
    this.studentGroupSelectionValid.set(false);
    this.selectedTeacher.set(undefined);
    this.affectedResources.set([]);
    this.resetSubmissionState();

    // Update progress component to reflect the reset
    // This will reset the currentStepIndex back to the first step
    this.updateProgressComponent();
  }
}
