<!-- Student/Group Selection Step -->
<div class="student-group-selection-step">
  <div class="flex flex-column gap-2">
    
    <!-- Step Header -->
    <div class="text-center mb-2">
      <h3 class="text-2xl font-semibold text-900 mb-2">
        Select Student or Group
      </h3>
      <p class="text-600 text-lg">
        Choose whether you want to request a teacher change for an individual student or an entire group.
      </p>
    </div>

    <!-- Selection Type Cards -->
    <div class="flex flex-column gap-2">
      <!-- Student Selection -->
      <div
        class="flex align-items-center gap-3 p-3 border-round-lg cursor-pointer transition-all duration-200 hover:bg-surface-50 border-2"
        [class.border-primary-500]="selectionType() === StudentGroupSelectionType.STUDENT"
        [class.bg-primary-50]="selectionType() === StudentGroupSelectionType.STUDENT"
        [class.surface-border]="selectionType() !== StudentGroupSelectionType.STUDENT"
        (click)="onSelectionTypeChange(StudentGroupSelectionType.STUDENT)">

        <p-radioButton
          name="selectionType"
          [value]="StudentGroupSelectionType.STUDENT"
          [(ngModel)]="selectionType"
          (onChange)="onSelectionTypeChange(StudentGroupSelectionType.STUDENT)">
        </p-radioButton>

        <div class="flex align-items-center justify-content-center w-2rem h-2rem border-circle bg-primary-100 flex-shrink-0">
          <i class="pi pi-user text-primary-600 text-sm"></i>
        </div>

        <div class="flex-1">
          <div class="font-semibold text-sm text-900 mb-1">
            {{ getSelectionTypeLabel(StudentGroupSelectionType.STUDENT) }}
          </div>
          <div class="text-600 text-xs line-height-3">
            {{ getSelectionTypeDescription(StudentGroupSelectionType.STUDENT) }}
          </div>
        </div>
      </div>

      <!-- Group Selection -->
      <div
        class="flex align-items-center gap-3 p-3 border-round-lg cursor-pointer transition-all duration-200 hover:bg-surface-50 border-2"
        [class.border-primary-500]="selectionType() === StudentGroupSelectionType.GROUP"
        [class.bg-primary-50]="selectionType() === StudentGroupSelectionType.GROUP"
        [class.surface-border]="selectionType() !== StudentGroupSelectionType.GROUP"
        (click)="onSelectionTypeChange(StudentGroupSelectionType.GROUP)">

        <p-radioButton
          name="selectionType"
          [value]="StudentGroupSelectionType.GROUP"
          [(ngModel)]="selectionType"
          (onChange)="onSelectionTypeChange(StudentGroupSelectionType.GROUP)">
        </p-radioButton>

        <div class="flex align-items-center justify-content-center w-2rem h-2rem border-circle bg-primary-100 flex-shrink-0">
          <i class="pi pi-users text-primary-600 text-sm"></i>
        </div>

        <div class="flex-1">
          <div class="font-semibold text-sm text-900 mb-1">
            {{ getSelectionTypeLabel(StudentGroupSelectionType.GROUP) }}
          </div>
          <div class="text-600 text-xs line-height-3">
            {{ getSelectionTypeDescription(StudentGroupSelectionType.GROUP) }}
          </div>
        </div>
      </div>
    </div>


    <!-- Selection Details -->
    @if (selectionType()) {
      
      <!-- Student Selection -->
      @if (selectionType() === StudentGroupSelectionType.STUDENT) {
        <div class="student-selection-section">
          <div class="mb-3">
            <h4 class="text-xl font-semibold text-900 mb-2">
              <i class="pi pi-user mr-2 text-primary-600"></i>
              Select Student
            </h4>
            <p class="text-600 m-0">
              Choose the specific student for whom you want to request a teacher change.
            </p>
          </div>
          
          <div class="w-full">
            <lib-prime-students-selection
              [enablePagination]="true"
              [pageSize]="10"
              [autoLoadInitialData]="true"
              [enableSearch]="true"
              [searchPlaceholder]="'Search students...'"
              [selectionMode]="'single'"
              [enableInitialItemSelection]="true"
              [initialSelectedId]="selectedStudent()?.userId ?? null"
              styleClass="w-full"
              (itemClicked)="onStudentSelected($event)">
            </lib-prime-students-selection>
          </div>

          <!-- Selected Student Display -->
          @if (selectedStudent()) {
            <div class="mt-3">
              <p-card styleClass="border-left-3 border-primary-500 bg-primary-50">
                <div class="flex align-items-center gap-3">
                  <i class="pi pi-check-circle text-primary-600 text-xl"></i>
                  <div>
                    <p class="font-semibold text-900 m-0">
                      Selected Student: {{ selectedStudent()?.firstName }} {{ selectedStudent()?.lastName }}
                    </p>
                    <p class="text-600 text-sm m-0">
                      The teacher change request will apply to this student only.
                    </p>
                  </div>
                </div>
              </p-card>
            </div>
          }
        </div>
      }

      <!-- Group Selection -->
      @if (selectionType() === StudentGroupSelectionType.GROUP) {
        <div class="group-selection-section">
          <div class="mb-3">
            <h4 class="text-xl font-semibold text-900 mb-2">
              <i class="pi pi-users mr-2 text-primary-600"></i>
              Select Student Group
            </h4>
            <p class="text-600 m-0">
              Choose the student group for which you want to request a teacher change.
            </p>
          </div>
          
          <div class="w-full">
            <app-prime-student-group-selection
              [enablePagination]="true"
              [pageSize]="10"
              [autoLoadInitialData]="false"
              [enableSearch]="true"
              [searchPlaceholder]="'Search student groups...'"
              [selectionMode]="'single'"
              [initialSelectedId]="selectedGroup()?.id ?? null"
              [enableInitialItemSelection]="true"
              styleClass="w-full"
              (groupItemClicked)="onGroupSelected($event)"
              [resetSelectionSignal]="false">
            </app-prime-student-group-selection>
          </div>

          <!-- Selected Group Display -->
          @if (selectedGroup()) {
            <div class="mt-3">
              <p-card styleClass="border-left-3 border-primary-500 bg-primary-50">
                <div class="flex align-items-center gap-3">
                  <i class="pi pi-check-circle text-primary-600 text-xl"></i>
                  <div>
                    <p class="font-semibold text-900 m-0">
                      Selected Group: {{ selectedGroup()?.groupName || selectedGroup()?.teachingLanguageName }}
                    </p>
                    <p class="text-600 text-sm m-0">
                      The teacher change request will apply to all students in this group.
                    </p>
                  </div>
                </div>
              </p-card>
            </div>
          }
        </div>
      }
    }

    
    <!-- Teaching Language Selection -->

    <div class="teaching-language-section mb-2">
      <div class="mb-3">
        <h4 class="text-xl font-semibold text-900 mb-2">
          <i class="pi pi-globe mr-2 text-primary-600"></i>
          Select Teaching Language
        </h4>
        <p class="text-600 m-0">
          Choose the teaching language for the teacher change request.
        </p>
      </div>

      <div class="w-full">
        <!-- Message when no student/group selected -->
        @if (!selectionType() || (!selectedStudent() && !selectedGroup())) {
          <div class="p-3 border-round-lg bg-surface-100 border-1 border-surface-300">
            <div class="flex align-items-center gap-2 text-600">
              <i class="pi pi-info-circle"></i>
              <span class="text-sm">Please select a student or group first to see available teaching languages.</span>
            </div>
          </div>
        } @else if (teachingLanguages().length === 0) {
          <div class="p-3 border-round-lg bg-orange-50 border-1 border-orange-300">
            <div class="flex align-items-center gap-2 text-orange-700">
              <i class="pi pi-exclamation-triangle"></i>
              <span class="text-sm">No teaching languages available for the selected {{ selectionType() === StudentGroupSelectionType.STUDENT ? 'student' : 'group' }}.</span>
            </div>
          </div>
        } @else {
          <p-select
          [appendTo]="'body'"
            [options]="teachingLanguages()"
            [ngModel]="selectedTeachingLanguage()?.id"
            (ngModelChange)="onTeachingLanguageSelected($event)"
            optionLabel="name"
            optionValue="id"
            placeholder="Select teaching language..."
            [loading]="isLoadingTeachingLanguages()"
            [showClear]="!isTeachingLanguageDisabled()"
            [disabled]="isTeachingLanguageDisabled()"
            styleClass="w-full"
            (onClear)="onTeachingLanguageCleared()">

            <ng-template pTemplate="selectedItem">
              @if (selectedTeachingLanguage()) {
                <div class="flex align-items-center gap-2">
                  <i class="pi pi-globe text-primary-600"></i>
                  <span>{{ selectedTeachingLanguage()?.name }}</span>
                </div>
              }
            </ng-template>

            <ng-template pTemplate="item" let-language>
              <div class="flex align-items-center gap-2 p-2">
                <i class="pi pi-globe text-primary-600"></i>
                <span>{{ language.name }}</span>
              </div>
            </ng-template>
          </p-select>
        }
      </div>

      <!-- Fixed Language Info Message -->
      @if (isTeachingLanguageDisabled() && selectedTeachingLanguage()) {
        <div class="mt-2">
          <div class="p-2 border-round-lg bg-blue-50 border-1 border-blue-200">
            <div class="flex align-items-center gap-2 text-blue-700">
              <i class="pi pi-info-circle text-xs"></i>
              @if (selectionType() === StudentGroupSelectionType.GROUP) {
                <span class="text-xs">Teaching language is fixed for this group and cannot be changed.</span>
              } @else {
                <span class="text-xs">This student has only one teaching language available.</span>
              }
            </div>
          </div>
        </div>
      }

      <!-- Selected Teaching Language Display -->
      @if (selectedTeachingLanguage()) {
        <div class="mt-3">
          <p-card styleClass="border-left-3 border-primary-500 bg-primary-50">
            <div class="flex align-items-center gap-3">
              <i class="pi pi-check-circle text-primary-600 text-xl"></i>
              <div>
                <p class="font-semibold text-900 m-0">
                  Selected Language: {{ selectedTeachingLanguage()?.name }}
                </p>
                <p class="text-600 text-sm m-0">
                  Teachers will be filtered based on this teaching language.
                </p>
              </div>
            </div>
          </p-card>
        </div>
      }
    </div>


    <!-- Validation Status -->
    @if (selectionType() && !isValid()) {
      <div class="mt-3">
        <p-card styleClass="border-left-3 border-orange-500 bg-orange-50">
          <div class="flex align-items-center gap-3">
            <i class="pi pi-exclamation-triangle text-orange-600 text-xl"></i>
            <div>
              <p class="font-semibold text-900 m-0">Selection Required</p>
              <p class="text-600 text-sm m-0">
                @if (!selectedTeachingLanguage()) {
                  Please select a teaching language first.
                } @else if (!selectionType()) {
                  Please select whether you want to change teacher for a student or group.
                } @else if (selectionType() === StudentGroupSelectionType.STUDENT) {
                  Please select a student to continue.
                } @else if (selectionType() === StudentGroupSelectionType.GROUP) {
                  Please select a student group to continue.
                }
              </p>
            </div>
          </div>
        </p-card>
      </div>
    }

  </div>
</div>
