@import 'projects/shared-modules.library/src/lib/styles/mixins.scss';

:host {
  display: block;
}

// Content area minimum height
.min-h-20rem {
  min-height: 20rem;
}

// Paginator styling
::ng-deep .p-paginator {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 8px;
  padding: 0.75rem 1rem;
}

// Empty state styling
.empty-state-container {
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

// Error state styling
.error-state-container {
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;

  .error-content {
    text-align: center;
    max-width: 400px;
  }
}

// Loading skeleton adjustments with smooth animations
:host ::ng-deep lib-skeleton-loader {
  .p-skeleton {
    border-radius: 8px;
    animation: skeleton-loading 1.5s ease-in-out infinite alternate;
  }
}

// Smooth skeleton loading animation
@keyframes skeleton-loading {
  0% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

// Content loading overlay for smooth transitions
.content-loading-overlay {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(1px);
    z-index: 10;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
    pointer-events: none;
  }

  &.loading::before {
    opacity: 1;
  }
}

