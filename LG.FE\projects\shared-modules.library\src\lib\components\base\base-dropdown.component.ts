import { 
  Component, 
  Input, 
  Output, 
  EventEmitter, 
  OnInit, 
  OnDestroy, 
  ChangeDetectionStrategy,
  signal,
  computed,
  inject,
  DestroyRef,
  Injector
} from '@angular/core';
import { toObservable, takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { debounceTime, distinctUntilChanged, skip } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { EventBusService, EmitEvent } from '../../services/event-bus.service';
import { IBasedDataGridRequest, IDataGridResponse } from '../../GeneratedTsFiles';

// ===========================
// CORE INTERFACES
// ===========================

/** Generic item interface that all dropdown items must implement */
export interface DropdownItem {
  itemId?: string | number;
  [key: string]: any;
}

/** Selection modes supported by the dropdown */
export type SelectionMode = 'single' | 'multiple';

/** State interfaces for reactive management */
export interface PaginationState {
  currentPage: number;
  hasMoreData: boolean;
  totalRecords: number;
  isLoadingMore: boolean;
}

export interface SearchState {
  searchTerm: string;
  isSearching: boolean;
  isSearchMode: boolean;
  isTyping: boolean;
  searchResults: DropdownItem[];
}

export interface SelectionState<T extends DropdownItem> {
  selectedItem: T | null;
  selectedItems: T[];
}

export interface ComponentState {
  isInitialized: boolean;
  hasLoadedInitialData: boolean;
  isCurrentlyLoading: boolean;
  allItems: DropdownItem[];
}

/**
 * Abstract Base Dropdown Component
 * 
 * Provides core functionality for dropdown components including:
 * - Search with debouncing and typing indicators
 * - Pagination management with load more functionality
 * - API data loading and caching
 * - Selection state management (single/multiple)
 * - Loading states and error handling
 * - Initial data loading and auto-selection by ID
 * 
 * @template T - The type of items in the dropdown (must extend DropdownItem)
 * @template TRequest - The type of API request (must extend IBasedDataGridRequest)
 * @template TResponse - The type of API response (must extend IDataGridResponse<T>)
 */
@Component({
  template: '', // Abstract component - no template
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export abstract class BaseDropdownComponent<
  T extends DropdownItem,
  TRequest extends IBasedDataGridRequest,
  TResponse extends IDataGridResponse<T>
> implements OnInit, OnDestroy {

  // ===========================
  // INJECTED SERVICES
  // ===========================
  protected readonly eventBusService = inject(EventBusService);
  protected readonly destroyRef = inject(DestroyRef);
  protected readonly injector = inject(Injector);

  // ===========================
  // INPUT PROPERTIES (CONFIGURATION)
  // ===========================

  /** Selection mode: 'single' for single selection, 'multiple' for multi-selection */
  @Input() selectionMode: SelectionMode = 'single';

  /** Enable pagination functionality for server-side data loading */
  @Input() enablePagination = false;

  /** Enable search functionality */
  @Input() enableSearch = true;

  /** Auto-load initial data when component initializes */
  @Input() autoLoadInitialData = false;

  /** Number of items per page */
  @Input() pageSize = 10;

  /** Debounce time for search in milliseconds */
  @Input() searchDebounceTime = 400;

  /** Placeholder text for search input */
  @Input() dropdownPlaceholder = 'Search...';

  /** Placeholder text for search input */
  @Input() searchPlaceholder = 'Search...';

  /** Initial item ID to load and select when component initializes */
  @Input() initialSelectedId: string | number | null = null;

  /** Enable initial item selection by ID */
  @Input() enableInitialItemSelection = false;

  /** Property to use for ID matching (e.g., 'userId', 'id', 'groupId') */
  @Input() idProperty: keyof T = 'id' as keyof T;

  @Input() emptyMessageText = 'No items found.';

  // ===========================
  // OUTPUT EVENTS
  // ===========================

  /** Emitted when an item or items are selected */
  @Output() itemSelected = new EventEmitter<T | T[]>();

  /** Emitted when an item is deselected (multiple selection) */
  @Output() itemDeselected = new EventEmitter<T>();

  /** Emitted when the search term changes */
  @Output() searchTermChanged = new EventEmitter<string>();

  /** Emitted when the search is cleared */
  @Output() searchCleared = new EventEmitter<void>();

  /** Emitted when the "load more" button is clicked */
  @Output() loadMoreClicked = new EventEmitter<void>();

  /** Emitted when the page changes */
  @Output() pageChanged = new EventEmitter<number>();

  /** Emitted when the total records count changes */
  @Output() totalRecordsChanged = new EventEmitter<number>();

  // ===========================
  // STATE SIGNALS
  // ===========================
  
  // Pagination state
  readonly currentPage = signal<number>(1);
  readonly isLoadingMore = signal<boolean>(false);
  readonly hasMoreData = signal<boolean>(true);
  readonly totalRecords = signal<number>(0);

  // Search state
  readonly searchTerm = signal<string>('');
  readonly isSearching = signal<boolean>(false);
  readonly isSearchMode = signal<boolean>(false);
  readonly isTyping = signal<boolean>(false);
  readonly searchResults = signal<T[]>([]);

  // Selection state
  readonly selectedItem = signal<T | null>(null);
  readonly selectedItems = signal<T[]>([]);

  // Component state
  readonly isInitialized = signal<boolean>(false);
  readonly hasLoadedInitialData = signal<boolean>(false);
  readonly isCurrentlyLoading = signal<boolean>(false);
  readonly allItems = signal<T[]>([]);

  // ===========================
  // COMPUTED PROPERTIES
  // ===========================

  /** Items to display in the dropdown based on current mode */
  readonly displayItems = computed(() => {
    if (!this.enablePagination) {
      return this.allItems();
    }
    
    const items = this.isSearchMode() ? this.searchResults() : this.allItems();
    return Array.isArray(items) ? items.filter(item => item != null) : [];
  });

  /** Overall loading state */
  readonly isLoading = computed(() => 
    this.isLoadingMore() || this.isSearching() || this.isTyping() || this.isCurrentlyLoading()
  );

  /** Effective placeholder text based on current state */
  readonly effectivePlaceholder = computed(() => {
    if (this.isSearchMode() && this.searchTerm()) {
      return `Searching for "${this.searchTerm()}"...`;
    }
    return this.enableSearch ? this.searchPlaceholder : 'Select an item...';
  });

  /** Current pagination state */
  readonly paginationState = computed((): PaginationState => ({
    currentPage: this.currentPage(),
    hasMoreData: this.hasMoreData(),
    totalRecords: this.totalRecords(),
    isLoadingMore: this.isLoadingMore()
  }));

  /** Current search state */
  readonly searchState = computed((): SearchState => ({
    searchTerm: this.searchTerm(),
    isSearching: this.isSearching(),
    isSearchMode: this.isSearchMode(),
    isTyping: this.isTyping(),
    searchResults: this.searchResults()
  }));

  /** Current selection state */
  readonly selectionState = computed((): SelectionState<T> => ({
    selectedItem: this.selectedItem(),
    selectedItems: this.selectedItems()
  }));

  /** Current component state */
  readonly componentState = computed((): ComponentState => ({
    isInitialized: this.isInitialized(),
    hasLoadedInitialData: this.hasLoadedInitialData(),
    isCurrentlyLoading: this.isCurrentlyLoading(),
    allItems: this.allItems()
  }));

  // ===========================
  // TIMER MANAGEMENT
  // ===========================
  private searchInputDebounceTimer: any = null;
  private typingIndicatorTimer: any = null;
  private safetyTimeoutTimer: any = null;

  // ===========================
  // ABSTRACT METHODS (TO BE IMPLEMENTED BY SUBCLASSES)
  // ===========================

  /** Abstract method to get the API event for triggering requests */
  protected abstract getApiEvent(): any;

  /** Abstract method to build API request from base parameters */
  protected abstract buildApiRequest(baseRequest: any): TRequest;

  /** Abstract method to create state selector observable */
  protected abstract createStateSelector(): Observable<any>;

  // ===========================
  // LIFECYCLE METHODS
  // ===========================

  ngOnInit(): void {
    this.initializeComponent();
    this.setupSubscriptions();
  }

  ngOnDestroy(): void {
    this.clearAllTimers();
  }

  // ===========================
  // INITIALIZATION METHODS
  // ===========================

  private initializeComponent(): void {
    if (this.autoLoadInitialData && this.enablePagination) {
      console.log('Auto-loading initial data...');
      this.hasLoadedInitialData.set(true);
      this.loadInitialData();
    }

    // Enhanced initial selection logic
    if (this.initialSelectedId && this.enableInitialItemSelection) {
      this.loadAndSelectItemById(this.initialSelectedId);
    }

    this.isInitialized.set(true);
  }

  private setupSubscriptions(): void {
    this.setupStateSubscription();

    if (this.enableSearch && this.enablePagination) {
      this.setupSearchSubscription();
    }

    this.setupEventSubscriptions();
  }

  private setupStateSubscription(): void {
    if (!this.enablePagination) return;

    console.log('Setting up state subscription for pagination...');
    this.createStateSelector()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (state) => {
          console.log('📡 State subscription triggered');
          this.handleStateChange(state);
        }
      });
  }

  private setupSearchSubscription(): void {
    toObservable(this.searchTerm, { injector: this.injector })
      .pipe(
        debounceTime(this.searchDebounceTime),
        distinctUntilChanged(),
        skip(1), // Skip initial empty value
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe({
        next: (searchTerm: string) => {
          if (this.shouldHandleSearchChange()) {
            console.log('Search term changed via subscription:', searchTerm);
            this.handleSearchTermChange(searchTerm);
          }
        }
      });
  }

  private setupEventSubscriptions(): void {
    // Subscribe to search term changes for event emission
    toObservable(this.searchTerm, { injector: this.injector })
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(term => {
        if (term) {
          this.searchTermChanged.emit(term);
        }
      });

    // Subscribe to total records changes
    toObservable(this.totalRecords, { injector: this.injector })
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(total => {
        this.totalRecordsChanged.emit(total);
      });

    // Subscribe to page changes
    toObservable(this.currentPage, { injector: this.injector })
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(page => {
        this.pageChanged.emit(page);
      });
  }

  // ===========================
  // STATE CHANGE HANDLERS
  // ===========================

  private handleStateChange(state: any): void {
    console.log('State change handler called:', {
      loading: state.loading,
      hasData: !!state.data,
      hasError: state.hasError,
      searchMode: this.isSearchMode(),
      currentPage: this.currentPage()
    });

    if (state.loading) {
      this.handleLoadingState();
      return;
    }

    this.clearLoadingStates();

    if (state.data) {
      this.handleSuccessfulDataLoad(state.data);
    }

    if (state.hasError) {
      this.handleErrorState(state.error);
    }
  }

  private handleLoadingState(): void {
    if (this.isSearchMode()) {
      this.isSearching.set(true);
    } else {
      this.isLoadingMore.set(true);
    }
  }

  private clearLoadingStates(): void {
    this.isLoadingMore.set(false);
    this.isSearching.set(false);
    this.isCurrentlyLoading.set(false);
  }

  private handleSuccessfulDataLoad(response: TResponse): void {
    const newItems = (response.pageData || []).filter(item => item != null);

    this.updateItemsBasedOnMode(newItems);
    this.updatePaginationState(response);
    this.handleAutoSelectItemById(newItems);

    console.log('Data loaded successfully:', {
      page: this.currentPage(),
      searchMode: this.isSearchMode(),
      searchTerm: this.searchTerm(),
      newItems: newItems.length,
      totalItems: this.displayItems().length,
      totalRecords: this.totalRecords(),
      hasMore: this.hasMoreData()
    });
  }

  private updateItemsBasedOnMode(newItems: T[]): void {
    if (this.isSearchMode()) {
      if (this.currentPage() === 1) {
        this.searchResults.set(newItems);
      } else {
        this.searchResults.update(current => [...current, ...newItems]);
      }
    } else {
      if (this.currentPage() === 1) {
        this.allItems.set(newItems);
      } else {
        this.allItems.update(current => [...current, ...newItems]);
      }
    }
  }

  private updatePaginationState(response: TResponse): void {
    this.totalRecords.set(response.totalRecords || 0);
    const currentItems = this.isSearchMode() ? this.searchResults() : this.allItems();
    this.hasMoreData.set(currentItems.length < this.totalRecords());
  }

  private handleAutoSelectItemById(newItems: T[]): void {
    if (!this.initialSelectedId || !this.enableInitialItemSelection) {
      return;
    }

    console.log('Searching for item with ID:', this.initialSelectedId, 'using property:', this.idProperty);

    const foundItem = newItems.find(item => {
      const itemValue = item[this.idProperty];
      return itemValue === this.initialSelectedId ||
             itemValue?.toString() === this.initialSelectedId?.toString();
    });

    if (foundItem) {
      console.log('Auto-selecting item found by ID:', foundItem);
      this.selectItem(foundItem);
      this.hasLoadedInitialData.set(true);

      // Clear the initial ID to prevent repeated auto-selection
      this.initialSelectedId = null;
    } else {
      console.log('Item with ID', this.initialSelectedId, 'not found in results');
      console.log('Available items:', newItems.map(item => ({
        [this.idProperty]: item[this.idProperty],
        item: item
      })));
    }
  }

  private handleErrorState(error: any): void {
    console.error('Error loading data:', error);
    this.clearLoadingStates();

    // Reset to safe state on error
    if (this.currentPage() === 1) {
      if (this.isSearchMode()) {
        this.searchResults.set([]);
      } else {
        this.allItems.set([]);
      }
    }
  }

  // ===========================
  // SEARCH FUNCTIONALITY
  // ===========================

  private shouldHandleSearchChange(): boolean {
    return this.isInitialized() && this.hasLoadedInitialData();
  }

  private handleSearchTermChange(searchTerm: string): void {
    const trimmedTerm = searchTerm.trim();

    if (this.isEmptySearchTerm(trimmedTerm)) {
      this.handleEmptySearchTerm();
      return;
    }

    this.initiateSearch(trimmedTerm);
  }

  private isEmptySearchTerm(trimmedTerm: string): boolean {
    return trimmedTerm === '';
  }

  private handleEmptySearchTerm(): void {
    if (this.isSearchMode()) {
      this.clearSearch(false);
    }
  }

  private initiateSearch(trimmedTerm: string): void {
    console.log('Starting search for:', trimmedTerm);
    this.enterSearchMode();
    this.resetSearchPagination();
    this.triggerApiCall();
  }

  private enterSearchMode(): void {
    this.isSearchMode.set(true);
  }

  private resetSearchPagination(): void {
    this.currentPage.set(1);
    this.searchResults.set([]);
    this.hasMoreData.set(true);
  }

  // ===========================
  // PUBLIC API METHODS
  // ===========================

  /**
   * Load initial data (first page)
   */
  loadInitialData(): void {
    this.currentPage.set(1);
    this.triggerApiCall();
  }

  /**
   * Load more data (next page)
   */
  loadMoreData(): void {
    if (this.isLoading() || !this.hasMoreData()) {
      return;
    }

    this.currentPage.update(page => page + 1);
    this.triggerApiCall();
    this.loadMoreClicked.emit();
  }

  /**
   * Clear search and return to normal pagination
   */
  clearSearch(closeDropdown: boolean = false): void {
    const wasInSearchMode = this.isSearchMode();

    this.searchTerm.set('');
    this.isSearchMode.set(false);
    this.searchResults.set([]);
    this.currentPage.set(1);
    this.hasMoreData.set(true);
    this.isTyping.set(false);

    this.clearAllTimers();

    // Reload normal data if needed
    if (wasInSearchMode && this.autoLoadInitialData &&
        this.isInitialized() && this.allItems().length === 0) {
      console.log('Reloading normal data after clearing search...');
      this.triggerApiCall();
    }

    this.searchCleared.emit();
  }

  /**
   * Perform search with given term
   */
  performSearch(searchTerm: string): void {
    this.searchTerm.set(searchTerm);
  }

  /**
   * Select an item (single selection)
   */
  selectItem(item: T): void {
    if (this.selectionMode === 'single') {
      this.selectedItem.set(item);
      this.itemSelected.emit(item);
    } else {
      // For multiple selection, add to array if not already selected
      const currentItems = this.selectedItems();
      const isAlreadySelected = currentItems.some(selected => selected.itemId === item.itemId);

      if (!isAlreadySelected) {
        this.selectedItems.update(items => [...items, item]);
        this.itemSelected.emit(this.selectedItems());
      }
    }
  }

  /**
   * Deselect an item (multiple selection)
   */
  deselectItem(item: T): void {
    if (this.selectionMode === 'multiple') {
      this.selectedItems.update(items => items.filter(selected => selected.itemId !== item.itemId));
      this.itemDeselected.emit(item);
    }
  }

  /**
   * Clear all selections
   */
  clearSelection(): void {
    this.selectedItem.set(null);
    this.selectedItems.set([]);
  }

  /**
   * Load and select item by ID
   */
  loadAndSelectItemById(itemId: string | number): void {
    if (!itemId) {
      this.clearSelection();
      return;
    }

    console.log('loadAndSelectItemById called with ID:', itemId);

    // Set the initial selected ID for auto-selection
    this.initialSelectedId = itemId;
    this.enableInitialItemSelection = true;

    // Set search mode to find the specific item
    this.isSearchMode.set(true);
    this.searchTerm.set(itemId.toString());
    this.currentPage.set(1);
    this.searchResults.set([]);

    this.triggerApiCall();
  }

  // ===========================
  // SEARCH INPUT MANAGEMENT
  // ===========================

  /**
   * Handle search input changes with debouncing
   */
  onSearchInputChange(value: string): void {
    console.log('🔍 Search input changed:', value);

    this.isTyping.set(true);
    this.clearAllTimers();

    // Set typing indicator timer
    this.typingIndicatorTimer = setTimeout(() => {
      this.isTyping.set(false);
    }, this.searchDebounceTime + 100);

    // Set debounced search timer
    this.searchInputDebounceTimer = setTimeout(() => {
      this.isTyping.set(false);
      this.searchTerm.set(value);
      console.log('⏰ Debounced search triggered:', value);
    }, this.searchDebounceTime);
  }

  /**
   * Clear search input
   */
  clearSearchInput(): void {
    this.searchTerm.set('');
    this.isTyping.set(false);
    this.clearAllTimers();
    console.log('🧹 Search input cleared');
  }

  /**
   * Clear search and close dropdown (for footer button)
   */
  clearSearchAndClose(event?: Event): void {
    if (event) {
      event.stopPropagation();
    }

    this.clearSearch(true);
    console.log('🧹 Search cleared and dropdown should close');
  }

  // ===========================
  // UTILITY METHODS
  // ===========================

  /**
   * Reset pagination and search state
   */
  resetPagination(): void {
    this.currentPage.set(1);
    this.allItems.set([]);
    this.hasMoreData.set(true);
    this.totalRecords.set(0);
    this.isLoadingMore.set(false);
    this.isCurrentlyLoading.set(false);

    // Reset search state
    this.searchTerm.set('');
    this.isSearchMode.set(false);
    this.isSearching.set(false);
    this.searchResults.set([]);
    this.isTyping.set(false);
  }

  /**
   * Check if there are items to display
   * @returns True if has items
   */
  hasItemsToDisplay(): boolean {
    return this.displayItems().length > 0;
  }

  /**
   * Check if should show empty message
   * @returns True if should show empty message
   */
  shouldShowEmptyMessage(): boolean {
    return !this.isComponentLoading() && !this.hasItemsToDisplay();
  }



  /**
   * Get dynamic search placeholder based on current state
   * @returns Appropriate placeholder text
   */
  getSearchPlaceholder(): string {
    if (this.searchPlaceholder) {
      return this.searchPlaceholder;
    } else if (this.isSearching()) {
      return 'Searching...';
    } else if (this.isTyping()) {
      return 'Type to search...';
    }
    return this.searchPlaceholder;
  }

  // ===========================
  // DROPDOWN UI MANAGEMENT
  // ===========================

  /**
   * Handles the logic when dropdown is shown
   * Can be overridden by subclasses for custom behavior
   */
  protected handleDropdownShowLogic(): void {
    // Reset to default state when dropdown opens, regardless of initialSelectedId
    // This ensures fresh data is loaded from the beginning
    console.log('🔄 Resetting to default state on dropdown open...');
    this.resetToDefaultState();

    // Focus search input if enabled
    this.focusSearchInputIfEnabled();
  }

  /**
   * Handle dropdown show event
   * Can be overridden by subclasses
   */
  onDropdownShow(): void {
    console.log('🔽 Dropdown opened');
    this.handleDropdownShowLogic();
  }

  /**
   * Handle dropdown hide event
   * Can be overridden by subclasses
   */
  onDropdownHide(): void {
    console.log('🔼 Dropdown closed');
  }

  /**
   * Focuses on search input if search functionality is enabled
   */
  protected focusSearchInputIfEnabled(): void {
    if (this.enableSearch && this.enablePagination) {
      setTimeout(() => {
        this.focusSearchInput();
      }, 150);
    }
  }

  /**
   * Focus on the search input element
   * Can be overridden by subclasses for different selectors
   */
  protected focusSearchInput(): void {
    const searchInput = document.querySelector('.p-select-header input[type="text"]') as HTMLInputElement;
    if (searchInput) {
      searchInput.focus();
      console.log('🎯 Search input focused');
    }
  }

  /**
   * Forces repositioning of the dropdown panel when appendTo="body"
   * This fixes positioning issues after clearing search results
   * Can be overridden by subclasses for different dropdown types
   */
  protected repositionDropdown(): void {
    // Default implementation - can be overridden by subclasses
    console.log('🔄 Repositioning dropdown (base implementation)');
  }

  // ===========================
  // ADDITIONAL UTILITY METHODS
  // ===========================


  /**
   * Check if an item has a specific property
   * @param item The item to check
   * @param property The property name to check for
   * @returns True if the item has the property
   */
  protected hasProperty(item: T, property: string): boolean {
    return item && typeof item === 'object' && property in item;
  }

  /**
   * Get a property value from an item safely
   * @param item The item to get the property from
   * @param property The property name
   * @param defaultValue Default value if property doesn't exist
   * @returns The property value or default value
   */
  protected getPropertyValue<TValue = any>(item: T, property: string, defaultValue: TValue): TValue {
    if (this.hasProperty(item, property)) {
      return (item as any)[property] ?? defaultValue;
    }
    return defaultValue;
  }



  /**
   * Get items that match a specific property value
   * @param items Array of items to filter
   * @param property Property name to match
   * @param value Value to match
   * @returns Filtered array of items
   */
  protected getItemsByProperty<TValue = any>(items: T[], property: string, value: TValue): T[] {
    return items.filter(item => this.getPropertyValue(item, property, null) === value);
  }

  /**
   * Find an item by a specific property value
   * @param items Array of items to search
   * @param property Property name to match
   * @param value Value to match
   * @returns First matching item or null
   */
  protected findItemByProperty<TValue = any>(items: T[], property: string, value: TValue): T | null {
    return items.find(item => this.getPropertyValue(item, property, null) === value) || null;
  }

  /**
   * Check if the dropdown has any selected items
   * @returns True if has selections
   */
  hasSelections(): boolean {
    if (this.selectionMode === 'single') {
      return this.selectedItem() !== null;
    } else {
      return this.selectedItems().length > 0;
    }
  }

  /**
   * Get the count of selected items
   * @returns Number of selected items
   */
  getSelectionCount(): number {
    if (this.selectionMode === 'single') {
      return this.selectedItem() ? 1 : 0;
    } else {
      return this.selectedItems().length;
    }
  }

  /**
   * Check if a specific item is selected
   * @param item Item to check
   * @returns True if the item is selected
   */
  isItemSelected(item: T): boolean {
    if (!item) return false;

    if (this.selectionMode === 'single') {
      const selected = this.selectedItem();
      return selected ? selected[this.idProperty] === item[this.idProperty] : false;
    } else {
      return this.selectedItems().some(selected => selected[this.idProperty] === item[this.idProperty]);
    }
  }

  /**
   * Reset component to default state
   */
  resetToDefaultState(): void {
    console.log('🔄 Resetting component to default state...');
    this.resetPagination();
    this.clearSelection();
    this.clearAllTimers();

    if (this.isInitialized()) {
      console.log('Loading default data after reset...');
      this.triggerApiCall();
    }
  }

  // ===========================
  // PRIVATE UTILITY METHODS
  // ===========================

  private triggerApiCall(): void {
    if (!this.canMakeApiCall()) {
      console.log('🚫 Cannot make API call, skipping...');
      return;
    }

    this.prepareApiCall();
    const request = this.buildApiRequest(this.buildBaseRequest());
    this.logApiCall(request);
    this.executeApiCall(request);
    this.setupSafetyTimeout();
  }

  private canMakeApiCall(): boolean {
    return !this.isCurrentlyLoading();
  }

  private prepareApiCall(): void {
    this.isCurrentlyLoading.set(true);
  }

  private buildBaseRequest(): any {
    const baseRequest: any = {
      pageNumber: this.currentPage(),
      pageSize: this.pageSize
    };

    // Add search term if in search mode
    if (this.isSearchMode() && this.searchTerm().trim()) {
      baseRequest.searchTerm = this.searchTerm().trim();
    }

    return baseRequest;
  }

  private logApiCall(request: TRequest): void {
    const callStack = new Error().stack;
    console.log('🚀 API call triggered:', {
      page: this.currentPage(),
      searchMode: this.isSearchMode(),
      searchTerm: this.searchTerm(),
      isInitialized: this.isInitialized(),
      hasLoadedInitialData: this.hasLoadedInitialData(),
      allItemsCount: this.allItems().length,
      request: request,
      callSource: callStack?.split('\n')[2]?.trim()
    });
  }

  private executeApiCall(request: TRequest): void {
    this.eventBusService.emit(new EmitEvent(this.getApiEvent(), request));
  }

  private setupSafetyTimeout(): void {
    this.safetyTimeoutTimer = setTimeout(() => {
      if (this.isCurrentlyLoading()) {
        console.warn('⚠️ Safety timeout: Clearing loading flag after 10 seconds');
        this.isCurrentlyLoading.set(false);
      }
    }, 10000);
  }

  private clearAllTimers(): void {
    if (this.searchInputDebounceTimer) {
      clearTimeout(this.searchInputDebounceTimer);
      this.searchInputDebounceTimer = null;
    }
    if (this.typingIndicatorTimer) {
      clearTimeout(this.typingIndicatorTimer);
      this.typingIndicatorTimer = null;
    }
    if (this.safetyTimeoutTimer) {
      clearTimeout(this.safetyTimeoutTimer);
      this.safetyTimeoutTimer = null;
    }
  }
  

  /**
   * Check if component is in loading state
   * @returns True if loading
   */
  isComponentLoading(): boolean {
    return this.isLoading();
  }

  /**
   * Check if should show load more button
   * @returns True if should show load more button
   */
  shouldShowLoadMore(): boolean {
    return this.enablePagination && this.hasMoreData() && !this.isLoading();
  }

  // ===========================
  // PRIME DROPDOWN SPECIFIC METHODS
  // ===========================

  /**
   * Generic selection change handler for prime dropdowns
   * Handles both single and multiple selection modes
   * @param event Selection change event from PrimeNG
   * @param singleSelectionSignal Signal for single selection
   * @param multipleSelectionSignal Signal for multiple selection
   * @param itemClickedEmitter EventEmitter to emit selection changes
   */
  protected handlePrimeSelectionChange<TItem extends DropdownItem>(
    event: { value: TItem | TItem[] },
    singleSelectionSignal: any,
    multipleSelectionSignal: any,
    itemClickedEmitter: EventEmitter<TItem | TItem[]>
  ): void {
    console.log('🎯 Prime selection changed:', event);

    if (this.selectionMode === 'single') {
      const item = event.value as TItem;
      singleSelectionSignal.set(item);

      // Update base component selection
      if (item) {
        this.selectItem(item as unknown as T);
      }

      itemClickedEmitter.emit(item);
    } else {
      const items = event.value as TItem[];
      multipleSelectionSignal.set(items);
      // Update base component selection for multiple items
      if (items && items.length > 0) {
        this.selectedItems.set(items as unknown as T[]);
      }
      itemClickedEmitter.emit(items);
    }
  }

  /**
   * Generic clear selection handler for prime dropdowns
   * Handles both single and multiple selection modes
   * @param singleSelectionSignal Signal for single selection
   * @param multipleSelectionSignal Signal for multiple selection
   * @param itemClickedEmitter EventEmitter to emit clear event
   */
  protected handlePrimeClearSelection(
    singleSelectionSignal: any,
    multipleSelectionSignal: any,
    itemClickedEmitter: EventEmitter<any>
  ): void {
    console.log('🗑️ Prime clear selection triggered');

    if (this.selectionMode === 'single') {
      singleSelectionSignal.set(undefined);
      this.selectedItem.set(null);
      itemClickedEmitter.emit(null as any);
    } else {
      multipleSelectionSignal.set([]);
      this.selectedItems.set([]);
      itemClickedEmitter.emit([]);
    }
  }

  /**
   * Generic clear selection handler for prime dropdowns WITHOUT emitting events
   * Use this for UI-only clearing (like clear button) to avoid triggering selection handlers
   * @param singleSelectionSignal Signal for single selection
   * @param multipleSelectionSignal Signal for multiple selection
   */
  protected handlePrimeClearSelectionUIOnly(
    singleSelectionSignal: any,
    multipleSelectionSignal: any
  ): void {
    console.log('🗑️ Prime clear selection UI only - no events emitted');

    if (this.selectionMode === 'single') {
      singleSelectionSignal.set(undefined);
      this.selectedItem.set(null);
      // ✅ NO EVENT EMISSION - this prevents re-selection loops
    } else {
      multipleSelectionSignal.set([]);
      this.selectedItems.set([]);
      // ✅ NO EVENT EMISSION - this prevents re-selection loops
    }
  }

  /**
   * Clear selection UI only without emitting events (for reset functionality)
   * Generic method that can be used by all prime dropdown components
   * @param primeSelectRef Reference to PrimeNG p-select component
   * @param primeMultiSelectRef Reference to PrimeNG p-multiselect component
   * @param singleSelectionSignal Signal for single selection
   * @param multipleSelectionSignal Signal for multiple selection
   * @param initialSelectedId Initial selection ID property
   * @param enableInitialItemSelection Enable initial item selection property
   */
  protected clearSelectionUIOnly(
    primeSelectRef: any,
    primeMultiSelectRef: any,
    singleSelectionSignal: any,
    multipleSelectionSignal: any,
    initialSelectedId: any,
    enableInitialItemSelection: any
  ): void {
    console.log('🔄 clearSelectionUIOnly called - performing complete reset');

    // Step 1: Clear the dropdown UI first
    if (this.selectionMode === 'single') {
      console.log('🔄 Clearing single select dropdown UI');
      primeSelectRef?.clear();
      singleSelectionSignal.set(undefined);
      this.selectedItem.set(null);
    } else {
      console.log('🔄 Clearing multi select dropdown UI');
      multipleSelectionSignal.set([]);
      if (primeMultiSelectRef) {
        primeMultiSelectRef.updateModel([]);
      }
      this.selectedItems.set([]);
    }

    // Step 2: Clear initial selection properties to ensure fresh behavior
    console.log('🔄 Clearing initial selection properties');
    if (initialSelectedId !== undefined) {
      initialSelectedId = null;
    }
    if (enableInitialItemSelection !== undefined) {
      enableInitialItemSelection = false;
    }

    // Step 3: Reset all base component state (search, pagination, data, etc.)
    console.log('🔄 Resetting pagination and search state');
    this.resetPagination();

    // Step 4: Clear any cached data and reset to fresh state
    console.log('🔄 Clearing cached data for fresh search');
    this.allItems.set([]);
    this.searchResults.set([]);
    this.isInitialized.set(true); // Keep initialized but reset data

    console.log('🔄 Reset complete - ready for fresh search');
  }

  /**
   * Enhanced dropdown show logic with initial selection preservation
   * Generic method that can be used by all prime dropdown components
   * @param initialSelectedId Initial selection ID
   * @param enableInitialItemSelection Whether to preserve initial selection
   */
  protected handlePrimeDropdownShowLogic(
    initialSelectedId: any,
    enableInitialItemSelection: boolean
  ): void {
    // Check if we have an initial selection that should be preserved
    if (initialSelectedId && enableInitialItemSelection) {
      console.log('🎯 Preserving initial selection on dropdown open:', initialSelectedId);
      // Don't reset - keep the search term and search mode for initial selection
      this.focusSearchInputIfEnabled();
      return;
    }

    // No initial selection - load fresh data from page 1
    console.log('🔄 No initial selection - loading fresh data');

    // Reset to ensure clean state
    this.resetPagination();

    // Load fresh data from page 1
    if (this.enablePagination && this.isInitialized()) {
      console.log('🔄 Loading initial data for fresh search');
      this.loadInitialData();
    }

    // Focus search input if enabled
    this.focusSearchInputIfEnabled();
  }

  /**
   * Enhanced search input change handler with event handling
   * @param event Event or string value
   * @param searchInputValueProperty Property to update with search value
   */
  protected handlePrimeSearchInputChange(
    event: Event | string,
    searchInputValueProperty: any
  ): void {
    // Handle both Event objects and string values
    const value = typeof event === 'string' ? event : (event.target as HTMLInputElement).value;

    // Prevent event bubbling to avoid dropdown closing (only for Event objects)
    if (typeof event !== 'string') {
      event.stopPropagation();
    }

    if (searchInputValueProperty !== undefined) {
      searchInputValueProperty = value;
    }

    console.log('🔍 Prime search input changed:', value);

    // Delegate to base component
    this.onSearchInputChange(value);
  }

  /**
   * Enhanced clear search input with event handling
   * @param event Optional event to prevent bubbling
   * @param searchInputValueProperty Property to clear
   */
  protected handlePrimeClearSearchInput(
    event?: Event,
    searchInputValueProperty?: any
  ): void {
    // Prevent event bubbling to avoid dropdown closing
    if (event) {
      event.stopPropagation();
    }

    if (searchInputValueProperty !== undefined) {
      searchInputValueProperty = '';
    }

    // Delegate to base component
    this.clearSearchInput();

    console.log('🧹 Prime search input cleared');
  }

  /**
   * Generic method to clear all selections for multiple selection mode
   * @param multipleSelectionSignal Signal for multiple selection
   * @param itemClickedEmitter EventEmitter to emit clear event
   */
  protected clearAllSelections(
    multipleSelectionSignal: any,
    itemClickedEmitter: EventEmitter<any>
  ): void {
    multipleSelectionSignal.set([]);
    this.selectedItems.set([]);
    itemClickedEmitter.emit([]);
  }

  /**
   * Utility method to validate and filter items
   * @param items Array of items to validate
   * @param requiredProperties Array of required property names
   * @returns Filtered array of valid items
   */
  protected filterValidItems<TItem extends DropdownItem>(
    items: TItem[],
    requiredProperties: string[] = []
  ): TItem[] {
    if (!Array.isArray(items)) {
      return [];
    }

    return items.filter(item => {
      if (!item) return false;

      // Check required properties
      for (const prop of requiredProperties) {
        if (!item.hasOwnProperty(prop) || item[prop] === null || item[prop] === undefined) {
          return false;
        }
      }

      return true;
    });
  }

  /**
   * Utility method to get display text for an item
   * @param item Item to get display text for
   * @param displayProperties Array of properties to use for display (in order of preference)
   * @returns Display text
   */
  protected getItemDisplayText<TItem extends DropdownItem>(
    item: TItem,
    displayProperties: string[] = ['name', 'title', 'label', 'text']
  ): string {
    if (!item) return '';

    for (const prop of displayProperties) {
      if (item[prop] && typeof item[prop] === 'string') {
        return item[prop];
      }
    }

    // Fallback to first string property
    for (const key in item) {
      if (typeof item[key] === 'string' && item[key].trim()) {
        return item[key];
      }
    }

    return 'Unknown';
  }

  /**
   * Utility method to check if an item matches search criteria
   * @param item Item to check
   * @param searchTerm Search term
   * @param searchProperties Properties to search in
   * @returns True if item matches search
   */
  protected itemMatchesSearch<TItem extends DropdownItem>(
    item: TItem,
    searchTerm: string,
    searchProperties: string[] = []
  ): boolean {
    if (!item || !searchTerm) return true;

    const term = searchTerm.toLowerCase().trim();
    if (!term) return true;

    // Search in specified properties
    for (const prop of searchProperties) {
      if (item[prop] && typeof item[prop] === 'string') {
        if (item[prop].toLowerCase().includes(term)) {
          return true;
        }
      }
    }

    // Fallback: search in all string properties
    for (const key in item) {
      if (typeof item[key] === 'string' && item[key].toLowerCase().includes(term)) {
        return true;
      }
    }

    return false;
  }
}
