import { Injectable, inject } from '@angular/core';
import { TimezoneService } from './timezone.service';

/**
 * Interface for date range filter configuration
 */
export interface IDateRangeFilterConfig {
  /** Source timezone for the date conversion (defaults to user's current timezone) */
  sourceTimezone?: string;
  /** Whether to include milliseconds in the output format (defaults to true) */
  includeMilliseconds?: boolean;
  /** Custom format pattern for ISO output (defaults to ISO 8601 with milliseconds) */
  customFormat?: string;
}

/**
 * Interface for date range filter result
 */
export interface IDateRangeFilterResult {
  /** Start of day in UTC ISO format */
  dateFrom: string | null;
  /** End of day in UTC ISO format */
  dateTo: string | null;
}

/**
 * Interface for date range input
 */
export interface IDateRangeInput {
  /** Start date (can be Date object, string, or null) */
  dateFrom: Date | string | null;
  /** End date (can be Date object, string, or null) */
  dateTo: Date | string | null;
}

/**
 * Generic Date Range Filter Service
 *
 * Provides utilities for converting date picker selections to proper start/end of day ranges
 * with timezone conversion for data grid filtering.
 *
 * Features:
 * - Converts dateFrom to start of day (00:00:00.000) in local timezone, then to UTC
 * - Converts dateTo to end of day (23:59:59.999) in local timezone, then to UTC
 * - Outputs ISO 8601 UTC format strings
 * - Handles null/undefined dates gracefully
 * - Configurable timezone support
 * - Type-safe with TypeScript interfaces
 */
@Injectable({
  providedIn: 'root'
})
export class DateRangeFilterService {
  private readonly timezoneService = inject(TimezoneService);

  /**
   * Default configuration for date range conversion
   */
  private readonly defaultConfig: Required<IDateRangeFilterConfig> = {
    sourceTimezone: this.timezoneService.getTimezone(),
    includeMilliseconds: true,
    customFormat: 'YYYY-MM-DDTHH:mm:ss.SSS[Z]'
  };

  /**
   * Converts date range input to UTC date range with proper start/end of day boundaries
   *
   * @param input - Date range input with dateFrom and dateTo
   * @param config - Optional configuration for conversion
   * @returns Date range result with UTC ISO strings
   *
   * @example
   * ```typescript
   * const result = dateRangeService.convertDateRangeToUtc({
   *   dateFrom: new Date('2025-06-24'),
   *   dateTo: new Date('2025-06-25')
   * });
   * // Result: {
   * //   dateFrom: "2025-06-24T21:00:00.000Z",
   * //   dateTo: "2025-06-25T21:00:00.000Z"
   * // }
   * ```
   */
  convertDateRangeToUtc(
    input: IDateRangeInput,
    config: IDateRangeFilterConfig = {}
  ): IDateRangeFilterResult {
    const finalConfig = { ...this.defaultConfig, ...config };

    return {
      dateFrom: this.convertDateFromToUtc(input.dateFrom, finalConfig),
      dateTo: this.convertDateToToUtc(input.dateTo, finalConfig)
    };
  }

  /**
   * Converts a single dateFrom to start of day in UTC
   *
   * @param date - Input date (Date, string, or null)
   * @param config - Configuration for conversion
   * @returns UTC ISO string for start of day or null
   */
  convertDateFromToUtc(
    date: Date | string | null,
    config: IDateRangeFilterConfig = {}
  ): string | null {
    if (!date) return null;

    const finalConfig = { ...this.defaultConfig, ...config };

    try {
      // Convert to start of day in local timezone, then to UTC
      const utcDate = this.timezoneService.convertLocalToUtc(date, {
        sourceTimezone: finalConfig.sourceTimezone,
        dayBoundary: 'start',
        outputFormat: 'moment'
      });

      return utcDate.format(finalConfig.customFormat);
    } catch (error) {
      console.error('Error converting dateFrom to UTC:', error);
      return null;
    }
  }

  /**
   * Converts a single dateTo to end of day in UTC
   *
   * @param date - Input date (Date, string, or null)
   * @param config - Configuration for conversion
   * @returns UTC ISO string for end of day or null
   */
  convertDateToToUtc(
    date: Date | string | null,
    config: IDateRangeFilterConfig = {}
  ): string | null {
    if (!date) return null;

    const finalConfig = { ...this.defaultConfig, ...config };

    try {
      // Convert to end of day in local timezone, then to UTC
      const utcDate = this.timezoneService.convertLocalToUtc(date, {
        sourceTimezone: finalConfig.sourceTimezone,
        dayBoundary: 'end',
        outputFormat: 'moment'
      });

      return utcDate.format(finalConfig.customFormat);
    } catch (error) {
      console.error('Error converting dateTo to UTC:', error);
      return null;
    }
  }

  /**
   * Converts date range for specific filter fields in a request object
   *
   * @param request - Request object to update
   * @param dateFields - Object mapping field names to date values
   * @param config - Optional configuration for conversion
   * @returns Updated request object with converted date fields
   *
   * @example
   * ```typescript
   * const updatedRequest = dateRangeService.convertDateFieldsInRequest(
   *   teachersRequest,
   *   {
   *     approvedDateFrom: selectedFromDate,
   *     approvedDateTo: selectedToDate
   *   }
   * );
   * ```
   */
  convertDateFieldsInRequest<T extends Record<string, any>>(
    request: T,
    dateFields: Record<string, Date | string | null>,
    config: IDateRangeFilterConfig = {}
  ): T {
    const updatedRequest = { ...request } as { [K in keyof T]: T[K] };

    Object.entries(dateFields).forEach(([fieldName, dateValue]) => {
      if (fieldName.toLowerCase().includes('from')) {
        (updatedRequest as any)[fieldName] = this.convertDateFromToUtc(dateValue, config);
      } else if (fieldName.toLowerCase().includes('to')) {
        (updatedRequest as any)[fieldName] = this.convertDateToToUtc(dateValue, config);
      } else {
        // For fields that don't specify from/to, treat as dateFrom (start of day)
        (updatedRequest as any)[fieldName] = this.convertDateFromToUtc(dateValue, config);
      }
    });

    return updatedRequest;
  }

  /**
   * Helper method to get current timezone
   *
   * @returns Current timezone identifier
   */
  getCurrentTimezone(): string {
    return this.timezoneService.getTimezone();
  }

  /**
   * Helper method to validate if a date is valid
   *
   * @param date - Date to validate
   * @returns Boolean indicating if date is valid
   */
  isValidDate(date: Date | string | null): boolean {
    if (!date) return false;

    try {
      const momentDate = this.timezoneService.convertLocalToUtc(date, {
        outputFormat: 'moment'
      });
      return momentDate.isValid();
    } catch {
      return false;
    }
  }

  /**
   * Creates a date range filter helper for specific field names
   *
   * @param fromFieldName - Name of the "from" field
   * @param toFieldName - Name of the "to" field
   * @returns Function that converts date range for the specified fields
   *
   * @example
   * ```typescript
   * const convertApprovedDateRange = dateRangeService.createFieldConverter(
   *   'approvedDateFrom',
   *   'approvedDateTo'
   * );
   *
   * const result = convertApprovedDateRange(fromDate, toDate);
   * // Returns: { approvedDateFrom: "...", approvedDateTo: "..." }
   * ```
   */
  createFieldConverter(fromFieldName: string, toFieldName: string) {
    return (
      dateFrom: Date | string | null,
      dateTo: Date | string | null,
      config: IDateRangeFilterConfig = {}
    ): Record<string, string | null> => {
      const result = this.convertDateRangeToUtc({ dateFrom, dateTo }, config);
      return {
        [fromFieldName]: result.dateFrom,
        [toFieldName]: result.dateTo
      };
    };
  }

  /**
   * Convenience method for teachers list approved date range
   *
   * @param dateFrom - Start date
   * @param dateTo - End date
   * @param config - Optional configuration
   * @returns Object with approvedDateFrom and approvedDateTo fields
   */
  convertApprovedDateRange(
    dateFrom: Date | string | null,
    dateTo: Date | string | null,
    config: IDateRangeFilterConfig = {}
  ): { approvedDateFrom: string | null; approvedDateTo: string | null } {
    const result = this.convertDateRangeToUtc({ dateFrom, dateTo }, config);
    return {
      approvedDateFrom: result.dateFrom,
      approvedDateTo: result.dateTo
    };
  }

  /**
   * Convenience method for students list registration date range
   *
   * @param dateFrom - Start date
   * @param dateTo - End date
   * @param config - Optional configuration
   * @returns Object with registeredFrom and registeredTo fields
   */
  convertRegistrationDateRange(
    dateFrom: Date | string | null,
    dateTo: Date | string | null,
    config: IDateRangeFilterConfig = {}
  ): { registeredFrom: string | null; registeredTo: string | null } {
    const result = this.convertDateRangeToUtc({ dateFrom, dateTo }, config);
    return {
      registeredFrom: result.dateFrom,
      registeredTo: result.dateTo
    };
  }
}
