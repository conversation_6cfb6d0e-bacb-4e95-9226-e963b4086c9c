<div (click)="onDivClick($event)" [class]="topRowClass()">
  <div class="flex">
    <!-- <div><span class="block text-900 font-medium mb-1"><PERSON><PERSON></span>
        <div class="text-600">UI/UX Designer</div>
    </div> -->

    <div class="card flex justify-content-center">

      
      @for (student of students; track student.id) {
        <div class="inline-flex mr-1 gap-1 surface-card border-1 surface-border p-1 border-round align-items-center">
          <lib-prime-profile-photo-single [width]="24" [height]="24" [alt]="'Profile Picture'" [priority]="false"
          [canuploadDirectlyToApi]="false"
          [userId]="student.userId!" customClass="mr-1  w-2rem h-2rem" [canUpload]="false"
          uploadButtonCssClass="img-btn smaller"></lib-prime-profile-photo-single>
          <span>{{student.firstName}}</span>
        </div>

      }
<!--       
      <p-avatarGroup styleClass="">

        @for (student of students; track student.id) {
       
          <p-avatar [label]="generalService.getUserInitials(student)" shape="circle" size="normal"
          [pTooltip]="student.firstName + ' ' + student.lastName" tooltipPosition="top"
                    [ngStyle]="{ 'background-color':  randomColors[student.id], color: '#ffffff' }" class="mr-1"/>
        }

      </p-avatarGroup> -->
    </div>

  </div>
  <div class="mt-2 md:mt-0 flex flex-nowrap">
    <p-button (click)="toggle($event)" icon="pi pi-eye" size="small" title="View Students"
              styleClass="p-button-text p-button-secondary"/>
   

    <!-- <button pbutton="" pripple="" icon="pi pi-twitter"
        class="p-element p-ripple p-button-text p-button-plain p-button-rounded mr-1 p-button p-component p-button-icon-only"><span
            class="p-button-icon pi pi-twitter" aria-hidden="true"></span><span
            class="p-ink"></span></button> -->
  </div>
</div>


<p-popover #op>
  @for (student of students; track student.id) {

    <div pRipple
         class="relative overflow-hidden cursor-not-allowed w-full p-link flex align-items-center p-2 text-color border-noround">
         <lib-prime-profile-photo-single [userId]="student.userId"
         customClass="mr-2 w-2rem flex" [width]="32" [height]="32">
         </lib-prime-profile-photo-single>
      <span class="inline-flex flex-column">
                  <span class="font-bold text-xs">{{ student.firstName }} {{ student.lastName }}</span>
                  <!-- <span class="text-xs flex align-items-center"> 
                      <i class="pi pi-globe mr-1 text-xs text-400"></i>
                         <span>{{student.countryOfResidence}}</span>

                  </span> -->
                  <span class="text-xs flex align-items-center"> 
                      <i class="pi pi-map-marker mr-1 text-xs text-400"
                         (mouseenter)="startUpdatingTime(student.timeZoneIana, student)"
                         (mouseleave)="stopUpdatingTime(student)"
                         [pTooltip]="studentCurrentTime[student.timeZoneIana]" tooltipPosition="right"
                         placeholder="Right"></i>
                         <span>{{student.timeZoneDisplayName}}</span>

                  </span>
                  <span class="text-xs flex align-items-center"> 
                    <i class="pi pi-clock mr-1 text-xs text-400" [pTooltip]="(student.timeZoneDisplayName)"
                    tooltipPosition="right" placeholder="Right"></i>  
                    <span>{{generalService.getUserLocalTime(student.timeZoneIana, true)}}</span>
                    
                  </span>
              </span>
    </div>
  }
</p-popover>
