<!-- Students List Component -->
<div class="students-list-container">

  <!-- Header Section -->
  <header class="admin-page-header surface-section border-bottom-1 surface-border mb-2">
    <div class="flex flex-column gap-2 py-2 px-3">
      <!-- Header Top Row -->
      <div class="flex justify-content-between align-items-center">
        <div class="flex align-items-center gap-3">



          <h1 class="text-xl font-medium primary-purple-color m-0">Students</h1>
        </div>
        <div class="flex align-items-center gap-2">
          <p-tag [value]="totalRecords().toString()" severity="info" class="text-sm">
          </p-tag>
        </div>
      </div>

    </div>
  </header>

  <!-- Applied Filters -->
  <div class="applied-filters-section mb-2" *ngIf="appliedFilters().length > 0">
    <app-applied-filters-tags [filters]="appliedFilters()" [config]="{
        showClearAll: true,
        clearAllLabel: 'Clear All Filters',
        headerText: 'Applied Filters:',
        headerIcon: 'pi pi-filter',
        responsive: true
      }" (filterRemoved)="onAppliedFilterRemove($event)" (clearAllClicked)="onAppliedFiltersClearAll($event)">
    </app-applied-filters-tags>
  </div>

  <!-- Data Grid -->
  <div class="data-grid-section">



    <!-- Data Table -->
    <p-table #dt [value]="studentsForTable()" [columns]="visibleColumns()" [loading]="isLoading()"
      [totalRecords]="totalRecords()" [rows]="pageSize()" [first]="(currentPage() - 1) * pageSize()" [paginator]="true"
      [rowsPerPageOptions]="rowsPerPageOptions()" [showCurrentPageReport]="true"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} students" [lazy]="true"
      (onPage)="onPageChange($event)" (onSort)="onSortChange($event)" [sortField]="queryParams().sortColumn"
      [sortOrder]="queryParams().sortDirection === 'asc' ? 1 : -1"
      styleClass="p-datatable-gridlines p-datatable-striped" [tableStyle]="{ 'min-width': '60rem' }" [scrollable]="true"
      scrollHeight="flex" dataKey="userId" [expandedRowKeys]="expandedRows()" (onRowExpand)="onRowExpand($event)"
      (onRowCollapse)="onRowCollapse($event)">

      <ng-template pTemplate="caption">
        <div class="flex flex-column md:flex-row justify-content-between align-items-center p-0 gap-4">
          <div class="flex-1">
            <div class="field" style="width: 300px">
              <div class="search-input-container">
                <input type="text" pInputText id="searchTerm" styleClass="w-full"
                  [value]="queryParams().searchTerm || ''" (input)="updateSearchTerm($event)"
                  placeholder="Search by name, email, or ID" />
                <button *ngIf="queryParams().searchTerm" class="search-clear-button" type="button"
                  (click)="clearSearchTerm()">
                  <i class="pi pi-times"></i>
                </button>
              </div>
            </div>
          </div>
          <div class="flex align-items-center gap-3">
            <!-- Filters Toggle Button -->
            <p-button icon="pi pi-filter"
              [label]="'Filters' + (appliedFiltersCount() > 0 ? ' (' + appliedFiltersCount() + ')' : '')"
              severity="secondary" class="p-button-outlined" badgeClass="p-badge-info"
              (onClick)="toggleFiltersDrawer()">
            </p-button>

            <!-- Column Selector -->
            <p-multiSelect [options]="visibleColumns()" [ngModel]="selectedColumns()"
              (ngModelChange)="onColumnsChange($event)" [filter]="true" optionLabel="header"
              placeholder="Choose Columns" [selectedItemsLabel]="'{0}/' + visibleColumns().length + ' columns shown'"
              scrollHeight="400px" />

            <!-- Export Button -->
            <p-button icon="pi pi-download" label="Export" severity="secondary" class="p-button-sm"
              (click)="exportTable()" />
          </div>
        </div>
      </ng-template>

      <ng-template pTemplate="header">
        <!-- Table Header -->
        <app-data-grid-header-footer [columns]="selectedColumns()" [sortable]="true" [reorderable]="true"
          [showHeader]="true" [showFooter]="false" [showActionsColumn]="true" [showExpansionColumn]="true"
          [expansionColumnWidth]="'3rem'" actionsColumnHeader="Actions" actionsColumnWidth="100px">
        </app-data-grid-header-footer>
      </ng-template>

      <!-- Table Body -->
      <ng-template pTemplate="body" let-expanded="expanded" let-index="rowIndex" let-student>
        <tr>


          <!-- Expand Button Column (Frozen Left) -->
          <td alignFrozen="left" pFrozenColumn>
            <p-button type="button" pRipple [pRowToggler]="student" [text]="true" [rounded]="true" [plain]="true"
              [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'" pTooltip="Expand to view student details"
              tooltipPosition="top">
            </p-button>
          </td>

          <!-- Actions Column (Frozen Left) -->
          <td alignFrozen="left" pFrozenColumn>
            <div class="flex justify-content-center">
              <button pButton icon="pi pi-eye" class="p-button-text p-button-sm"></button>
            </div>
          </td>

          <td *ngFor="let col of selectedColumns()" [style.max-width]="col.maxWidth">

            <!-- First Name -->
            <span *ngIf="col.field === 'firstName'" class="font-medium">
              {{ student.firstName }}
            </span>

            <!-- Last Name -->
            <span *ngIf="col.field === 'lastName'" class="font-medium">
              {{ student.lastName }}
            </span>

            <!-- Gender -->
            <span *ngIf="col.field === 'gender'">
              {{ getGenderDisplayText(student.gender) }}
            </span>

            <!-- Country -->
            <span *ngIf="col.field === 'country'">
              {{ student.country }}
            </span>

            <!-- Country -->
            <span *ngIf="col.field === 'city'">
              {{ student.city }}
            </span>

            <!-- Speaking Languages -->
            <div *ngIf="col.field === 'speakingLanguages'" class="flex flex-wrap gap-1">
              <p-tag *ngFor="let lang of student.speakingLanguages" [value]="lang.language" severity="secondary"
                class="text-xs">
              </p-tag>
              <span *ngIf="!student.speakingLanguages || student.speakingLanguages.length === 0"
                class="text-gray-500 text-sm">No languages</span>
            </div>

            <!-- Registration Date -->
            <span *ngIf="col.field === 'registeredDate'">
              {{ formatDate(student.registeredDate) }}
            </span>

            <!-- Learning Languages (Student Teaching Languages) -->
            <div *ngIf="col.field === 'studentTeachingLanguageDto'" class="flex flex-wrap gap-1">
              <p-tag *ngFor="let teachingLang of student.studentTeachingLanguageDto"
                [value]="teachingLang.teachingLanguageName + ' (L' + teachingLang.languageLevel + ')'" severity="info"
                class="text-xs">
              </p-tag>
              <span *ngIf="!student.studentTeachingLanguageDto || student.studentTeachingLanguageDto.length === 0"
                class="text-gray-500 text-sm">No languages</span>
            </div>

            <!-- Last Account Status Update -->
            <span *ngIf="col.field === 'lastAccountStatusUpdate'">
              {{ formatDate(student.lastAccountStatusUpdate) }}
            </span>

            <!-- Account Status -->
            <p-tag *ngIf="col.field === 'accountStatus'" [value]="getAccountStatusText(student.accountStatus)"
              [severity]="student.accountStatus === 1 ? 'success' :
                              student.accountStatus === 2 ? 'warn' :
                              student.accountStatus === 3 ? 'danger' : 'info'">
            </p-tag>

            <!-- Blocked Status -->
            <p-tag *ngIf="col.field === 'isBlocked'" [value]="getBlockedStatusText(student.isBlocked)"
              [severity]="student.isBlocked ? 'danger' : 'success'">
            </p-tag>

          </td>
        </tr>
      </ng-template>


      <!-- Expand Template - Teacher Details -->
      <ng-template pTemplate="expandedrow" let-student>
        <tr>
          <td colspan="100%">

            <div class="expansion-content">
              <div class="flex align-items-center justify-content-between mb-3">
                <h5 class="m-0 text-900">
                  <i class="pi pi-user mr-2"></i>
                  Student Details: {{ student.firstName }} {{ student.lastName }}
                </h5>
              </div>


              <div class="grid gap-2">

                 <!-- Username -->
                <div class="col-12 md:col-6">
                  <div class="flex align-items-center gap-2">
                    <div class="flex flex-column">
                      <span class="text-xs text-500 mb-1">Profile Picture</span>

                         <lib-prime-profile-photo-single [width]="52" [height]="52" [alt]="'Profile Picture'" [priority]="false"
      [userId]="student.userId!" [src]="student.profilePhotoUrl!" customClass="mr-2  w-4rem h-4rem" [canUpload]="false"
      uploadButtonCssClass="img-btn smaller"></lib-prime-profile-photo-single>

                    </div>
                  </div>
                </div>

               

                <!-- Username -->
                <div class="col-12 md:col-6">
                  <div class="flex align-items-center gap-2">
                    <i class="pi pi-phone text-500"></i>
                    <div class="flex flex-column">
                      <span class="text-xs text-500 mb-1">Username</span>
                        <span class="text-sm text-900">{{ student.loginUsername || 'Not provided' }}</span>
                    </div>
                  </div>
                </div>

                <!-- Email -->
                <div class="col-12 md:col-6">
                  <div class="flex align-items-center gap-2">
                    <i class="pi pi-info-circle text-500"></i>
                    <div class="flex flex-column">
                      <span class="text-xs text-500 mb-1">Date of Birth</span>
                      <span class="text-sm text-900">
                        {{generalService.calculateAge(student.dateOfBirth) + ' years old' || 'Not provided'}}
                         - {{ student.dateOfBirth ? (student.dateOfBirth | date: 'mediumDate') : 'Not provided' }}</span>
                    </div>
                  </div>
                </div>

                <!-- Timezone -->
                <div class="col-12 md:col-6">
                  <div class="flex align-items-center gap-2">
                    <i class="pi pi-clock text-500"></i>
                    <div class="flex flex-column">
                      <span class="text-xs text-500 mb-1">Timezone</span>
                        <span class="text-sm text-900">{{ student.timeZoneDisplayName || 'Not provided' }}</span>
                    </div>
                  </div>
                </div>
                

              </div>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Table Footer -->
      <ng-template pTemplate="footer">
        <app-data-grid-header-footer [columns]="selectedColumns()" [sortable]="true" [reorderable]="true"
          [showHeader]="false" [showFooter]="true" [showActionsColumn]="true" [showExpansionColumn]="true"
          [expansionColumnWidth]="'3rem'" actionsColumnHeader="Actions" actionsColumnWidth="100px">
        </app-data-grid-header-footer>
      </ng-template>

      <!-- Empty State -->
      <ng-template pTemplate="emptymessage">
        <tr>
          <td [attr.colspan]="selectedColumns().length" class="text-center p-4">
            <div class="flex flex-column align-items-center gap-3 py-5">
              <i class="pi pi-users text-4xl text-400"></i>
              <p class="text-xl text-600 m-0">No students found</p>
              <p class="text-600 m-0">Try adjusting your search criteria or filters</p>
            </div>
          </td>
        </tr>
      </ng-template>

    </p-table>
  </div>

</div>

<!-- Filters Drawer -->
<app-filters-drawer-sidebar [visible]="isFiltersDrawerVisible()" [config]="filtersConfig"
  [filterContentTemplate]="filterContentTemplate" (visibleChange)="onFiltersDrawerVisibilityChange($event)"
  (actionClicked)="onFiltersDrawerAction($event)">
</app-filters-drawer-sidebar>

<ng-template #filterContentTemplate>
  <app-students-list-filters #studentsFiltersComponent [filterState]="{
      queryParams: queryParams(),
      teachingLanguages: teachingLanguages(),
      studentAgesRange: [queryParams().studentAgesMin || 2, queryParams().studentAgesMax || 17],
      isFilterOpen: isFiltersDrawerVisible()
    }" [config]="{
      enableAutoSearch: false,
      searchDebounceMs: 300
    }" (filterChanged)="onFilterChange($event)" (filterAction)="onFilterAction($event)">
  </app-students-list-filters>
</ng-template>