<div class="grid">
    <div class="col-12">
        <!-- {{authService.getUserRole() | json }} -->
        <app-inner-header-card [breadcrumbs]="breadcrumbs" [actionButtonLabel]="
        authService.getUserRole() === IUserRole.PARENT ? 'Register Student' : ''" actionButtonIcon="pi pi-user-plus"
            (actionButtonClick)="registerService.goToRegisterNewStudent()">
            <div breadcrumb-label>
                {{ breadcrumbs[0].label }}
            </div>

        </app-inner-header-card>

    </div>


    <div class="col-12">
        <div class=" surface-card shadow-2">
            <div class="surface-section py-3 lg:px-3">
                <div class="flex flex-column md:align-items-center md:justify-content-between md:flex-row">

                    <div class="flex flex-column md:flex-row md:justify-content-between">
                        <div class="flex align-items-center md:mt-0">

                            <p-iconfield>
                                <p-inputicon styleClass="pi pi-search" />
                                <input type="text" pInputText placeholder="Search by name" [value]="searchQuery()"
                                    (input)="onSearchInputChange($event)" />
                            </p-iconfield>
                        </div>
                    </div>
                    <div class="font-medium text-lg md:text-3xl text-900">&nbsp;</div>
                </div>
            </div>
        </div>
    </div>

</div>

<div class="content-loading-overlay" [class.loading]="isLoading() && hasLoadedInitialData()">
    <div class="grid">
        @if(isLoading() && !hasLoadedInitialData()) {
            <ng-container *ngTemplateOutlet="loading"></ng-container>
        } @else {
            <!-- {{students$() | json}} -->
            @if (totalRecords() === 0) {

      @let hasCreateStudentPermission = this.permissionService.hasPermission(this.authService.getUserClaims(),
        ['students',
        'create']);

                <div class="col-12">
                    <div class="w-full surface-card shadow-2 py-3 flex flex-column align-items-center justify-content-center">
                        <app-empty-data-image-text
                            [emptyDataText]="'No Students found.'"
                            [showAction]="hasCreateStudentPermission ? true : false"
                            [showActionButtonText]="'Register New Student'"
                            (onActionSelected)="this.registerService.goToRegisterNewStudent()">
                        </app-empty-data-image-text>
                    </div>
                </div>
            } @else {
                @for (item of studentsData(); track item.userId) {
                    <div class="col-12 md:col-6 xxl:col-3">
                        <app-student-mini-info-card [student]="item" [mode]="'existing'" [studentsNum]="1" />
                    </div>
                }
            }
        }

    </div>

    <p-paginator
        (onPageChange)="onPageChange($event)"
        [first]="first()"
        [rows]="rows()"
        [totalRecords]="totalRecords()"
        [rowsPerPageOptions]="[5, 10, 20, 30]"
        [showCurrentPageReport]="true"
        currentPageReportTemplate="Showing {first} to {last} of {totalRecords} students" />
</div>

<ng-template #loading>
    <div class="flex w-full">
        <div class="col-12 md:col-6 xxl:col-3">
            <lib-skeleton-loader shape="circle" layout="card" width="w-full" height="5rem" [count]="1"
                styleClass="mr-2 w-full sm:w-full" />
        </div>

        <div class="col-12 md:col-6 xxl:col-3">
            <lib-skeleton-loader shape="circle" layout="card" width="w-full" height="5rem" [count]="1"
                styleClass="mr-2 w-full sm:w-full" />
        </div>

        <div class="col-12 md:col-6 xxl:col-3">
            <lib-skeleton-loader shape="circle" layout="card" width="w-full" height="5rem" [count]="1"
                styleClass="mr-2 w-full sm:w-full" />
        </div>

        <div class="col-12 md:col-6 xxl:col-3">
            <lib-skeleton-loader shape="circle" layout="card" width="w-full" height="5rem" [count]="1"
                styleClass="mr-2 w-full sm:w-full" />
        </div>
    </div>
</ng-template>