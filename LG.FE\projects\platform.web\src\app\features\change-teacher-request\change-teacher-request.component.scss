@use "mixins";

:host {
  display: block;
  width: 100%;
}

.change-teacher-request-container {
  max-width: 800px;
  margin: 0 auto;

  // Use fluid spacing for better responsive design
  @include mixins.fluid-property(padding, 12, 24, 320, 768);

  @include mixins.breakpoint(mobile) {
    max-width: 100%;
    margin: 0;
  }

  @include mixins.breakpoint(tablet) {
    max-width: 90%;
  }

  @include mixins.breakpoint(desktop) {
    max-width: 800px;
  }

  .progress-section {
    margin-bottom: 2rem;

    @include mixins.breakpoint(mobile) {
      margin-bottom: 1.5rem;
    }
  }

  .content-section {
    .step-content {
      .step-header {
        text-align: center;
        margin-bottom: 2rem;

        @include mixins.breakpoint(mobile) {
          margin-bottom: 1.5rem;
        }

        h2 {
          color: var(--text-color);
          margin-bottom: 0.5rem;
          font-weight: 600;

          // Use fluid typography for better scaling
          @include mixins.fluid-typography(24, 32, 320, 768);

          @include mixins.breakpoint(mobile) {
            text-align: center;
          }
        }

        p {
          color: var(--text-color-secondary);
          max-width: 600px;
          margin: 0 auto;
        }
      }

      // Selection Step Styles
      &.selection-step {
        .selection-options {
          max-width: 600px;
          margin: 0 auto;

          .selection-card {
            cursor: pointer;
            transition: all 0.2s ease-in-out;
            border: 2px solid transparent;


            &.selected {
              background: var(--primary-50);
              box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.15);

              .selection-content {
                h3 {
                  color: var(--primary-color);
                }

                i {
                  color: var(--primary-color);
                }
              }
            }

            .selection-content {
              h3 {
                transition: color 0.2s ease-in-out;
              }

              i {
                transition: color 0.2s ease-in-out;
              }

              p {
                line-height: 1.5;
              }
            }

            // Radio button styling
            ::ng-deep {
              .p-radiobutton {
                .p-radiobutton-box {
                  width: 1.25rem;
                  height: 1.25rem;
                  margin-top: 0.125rem;
                }
              }
            }
          }
        }
      }

      // Placeholder content for future steps
      .placeholder-content {
        background: var(--surface-100);
        border-radius: 8px;
        padding: 3rem 2rem;
        text-align: center;
        border: 2px dashed var(--surface-300);

        @include mixins.breakpoint(mobile) {
          padding: 2rem 1rem;
        }
      }
    }
  }

  .navigation-section {
    border-top: 1px solid var(--surface-200);
    padding-top: 1.5rem;
    margin-top: 2rem;

    @include mixins.breakpoint(mobile) {
      padding-top: 1rem;
      margin-top: 1.5rem;

      .flex {
        flex-direction: column;
        gap: 1rem;

        .flex.gap-2 {
          flex-direction: row;
          justify-content: center;
        }
      }
    }

    // Button styling
    ::ng-deep {
      .p-button {
        padding: 0.75rem 1.5rem;
        font-weight: 500;

        @include mixins.breakpoint(mobile) {
          padding: 0.625rem 1.25rem;
          font-size: 0.875rem;
        }

        &.p-button-outlined {
          border-width: 2px;
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }
  }
}

// Responsive adjustments
@include mixins.breakpoint(mobile) {
  .change-teacher-request-container {
    .content-section {
      .step-content {
        &.selection-step {
          .selection-options {
            .selection-card {
              margin-bottom: 1rem;

              .flex.align-items-start {
                flex-direction: row;
                align-items: flex-start;
              }

              .selection-content {
                h3 {
                  font-size: 1rem;
                }

                p {
                  font-size: 0.875rem;
                }
              }
            }
          }
        }
      }
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .change-teacher-request-container {
    .content-section {
      .step-content {
        &.selection-step {
          .selection-options {
            .selection-card {
              border-width: 3px;

              &.selected {
                border-width: 4px;
              }
            }
          }
        }
      }
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .change-teacher-request-container {
    .content-section {
      .step-content {
        .placeholder-content {
          background: var(--surface-800);
          border-color: var(--surface-600);
        }
      }
    }
  }
}
