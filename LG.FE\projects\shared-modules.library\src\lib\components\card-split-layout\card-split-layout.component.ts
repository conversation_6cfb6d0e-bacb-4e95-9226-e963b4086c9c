import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input, type OnInit } from '@angular/core';

@Component({
    selector: 'app-card-split-layout',
    imports: [CommonModule],
    templateUrl: './card-split-layout.component.html',
    styleUrl: './card-split-layout.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class CardSplitLayoutComponent implements OnInit {
  @Input() maxWidth = '1200px';
  @Input() containerClass = 'px-2 py-4 md:py-6 md:px-6 lg:px-8';
  @Input() wrapperClass = 'form__box flex-column-reverse';
  @Input() leftSideClass = 'p-4 xl:p-7 surface-card left-login';
  @Input() showRightSide: boolean = true;
  @Input() showLeftSide: boolean = true;
  @Input() rightSideContainerClass = 'p-4 lg:p-7 grad-purple-1 bg-graphic surface-card';
  @Input() rightBgImage: string = ''; // Input for the background image

  ngOnInit(): void { }

}
