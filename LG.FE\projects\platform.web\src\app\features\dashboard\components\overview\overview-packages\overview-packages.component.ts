import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, Injector, signal, type OnInit, DestroyRef, ViewChild } from '@angular/core';
import { toObservable, takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { IconField } from 'primeng/iconfield';
import { InputIcon } from 'primeng/inputicon';
import { InputTextModule } from 'primeng/inputtext';
import { PaginatorModule, PaginatorState } from 'primeng/paginator';
import { debounceTime, distinctUntilChanged, skip } from 'rxjs';
import {
  AuthStateService,
  DataApiStateService,
  State,
  ITeachingLanguageDto,
  IGetAllTeachingLanguagesResponse,
  TeachingLanguagesRoutes,
  HandleApiResponseService,
  FiltersDrawerSidebarComponent,
  IFiltersDrawerConfig,
  AppliedFiltersTagsComponent,
  IAppliedFilterTag,
  nameOf,
  PermissionService,
  GeneralService,
  IGetPackagesResponse,
  IGetPackagesRequest,
  IPackage,
  SkeletonLoaderComponent,
  EventBusService
} from 'SharedModules.Library';
import { IUserRole, RegisterService } from 'SharedModules.Library';
import { ScrollPositionService } from '@platform.src/app/core/services/scroll-position.service';
import { PackagesListHelperService } from '@platform.src/app/core/services/packages-list-helper.service';
import { EmptyDataImageTextComponent } from '@platform.src/app/shared/components/empty-data-image-text/empty-data-image-text.component';
import { PackageMiniInfoCardComponent } from '@platform.src/app/shared/dashboard/package-mini-info-card/package-mini-info-card.component';
import { InnerHeaderCardComponent } from '@platform.src/app/shared/layout/inner-header-card/inner-header-card.component';
import { OverviewPackagesFiltersComponent, IOverviewPackagesFilterState, IOverviewPackagesFilterChangeEvent, IOverviewPackagesFilterActionEvent } from './overview-packages-filters/overview-packages-filters.component';

@Component({
  selector: 'app-overview-packages',
  imports: [
    CommonModule,
    ButtonModule,
    FormsModule,
    InputTextModule,
    PaginatorModule,
    PackageMiniInfoCardComponent,
    InnerHeaderCardComponent,
    EmptyDataImageTextComponent,
    InputIcon,
    IconField,
    SkeletonLoaderComponent,
    FiltersDrawerSidebarComponent,
    AppliedFiltersTagsComponent,
    OverviewPackagesFiltersComponent
  ],
  templateUrl: './overview-packages.component.html',
  styleUrl: './overview-packages.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OverviewPackagesComponent implements OnInit {
  // Constants
  readonly breadcrumbs = [{ label: 'Packages', url: '' }];
  readonly IUserRole = IUserRole;
  readonly Math = Math;

  // Angular services
  private readonly injector = inject(Injector);
  private readonly destroyRef = inject(DestroyRef);
  private readonly router = inject(Router);
  private readonly activatedRoute = inject(ActivatedRoute);

  // Application services
  readonly registerService = inject(RegisterService);
  readonly generalService = inject(GeneralService);
  readonly dataStateService = inject(DataApiStateService);
  readonly scrollPositionService = inject(ScrollPositionService);
  readonly authService = inject(AuthStateService);
  readonly eventBusService = inject(EventBusService);
  readonly permissionService = inject(PermissionService);
  readonly packagesHelperService = inject(PackagesListHelperService);
  private readonly handleApiService = inject(HandleApiResponseService);

  // ViewChild references
  @ViewChild('packagesFilters') packagesFiltersComponent?: OverviewPackagesFiltersComponent;

  // ============================================================================
  // SIGNALS & STATE
  // ============================================================================

  // Core state signals
  readonly filtersDrawerVisible = signal(false);
  readonly appliedFilters = signal<IAppliedFilterTag[]>([]);
  readonly queryParams = signal<IGetPackagesRequest>(this.packagesHelperService.createDefaultPackagesRequestForUser());
  readonly searchQuery = signal('');
  private readonly hasLoadedInitialData = signal(false);

  // Data signals
  readonly teachingLanguages = signal<ITeachingLanguageDto[]>([]);

  // Field name mapping for type safety
  private readonly fieldNames = nameOf<IGetPackagesRequest>();

  // Filters drawer configuration
  readonly filtersDrawerConfig: IFiltersDrawerConfig = {
    headerText: 'Filter Packages',
    headerIcon: 'pi pi-filter',
    position: 'right',
    width: '400px',
    showApplyButton: true,
    showResetButton: true,
    showCloseButton: true,
    applyButtonLabel: 'Apply Filters',
    resetButtonLabel: 'Reset All',
    closeButtonLabel: 'Close',
    applyButtonIcon: 'pi pi-check',
    resetButtonIcon: 'pi pi-refresh',
    closeButtonIcon: 'pi pi-times'
  };

  // Backend data
  readonly userToSignal = this.authService.userDecodedJWTData$;
  readonly packages$ = computed(() => this.dataStateService.parentPackages.state() || {} as State<IGetPackagesResponse>);

  // Data & pagination
  readonly packagesData = computed(() => {
    const response = this.packages$().data as IGetPackagesResponse || {} as IGetPackagesResponse;
    return response.pageData || [];
  });

  readonly totalRecords = computed(() => {
    const response = this.packages$().data as IGetPackagesResponse || {} as IGetPackagesResponse;
    return response.totalRecords || 0;
  });

  readonly currentPage = computed(() => {
    const response = this.packages$().data as IGetPackagesResponse || {} as IGetPackagesResponse;
    return response.currentPage || 1;
  });

  readonly pageSize = computed(() => {
    const response = this.packages$().data as IGetPackagesResponse || {} as IGetPackagesResponse;
    return response.pageSize || 15;
  });

  // Loading states
  readonly isLoading = computed(() => this.packages$().loading || false);
  readonly hasError = computed(() => this.packages$().hasError || false);
  readonly errorMessage = computed(() => this.packages$().error || '');

  // UI states
  readonly hasData = computed(() => this.packagesData().length > 0);
  readonly showEmptyState = computed(() => !this.isLoading() && !this.hasData() && this.hasLoadedInitialData());

  // Filter state for child component
  readonly filterState = computed((): IOverviewPackagesFilterState => ({
    queryParams: this.queryParams(),
    teachingLanguages: this.teachingLanguages(),
    isFilterOpen: this.filtersDrawerVisible()
  }));

  // Applied filters count
  readonly appliedFiltersCount = computed(() => this.appliedFilters().length);

  ngOnInit(): void {
    this.initializeComponent();
    this.setupRouteParamsSubscription();
    this.setupSearchQuerySubscription();
    this.setupAppliedFiltersSubscription();
    this.loadTeachingLanguages();
  }

  // ============================================================================
  // INITIALIZATION
  // ============================================================================

  private initializeComponent(): void {
    console.debug('Overview Packages Component initialized');
    // Data loading is handled by route params subscription
  }

  private setupRouteParamsSubscription(): void {
    this.activatedRoute.queryParams
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((params: Params) => {
        this.handleRouteParamsChange(params);
      });
  }

  private setupSearchQuerySubscription(): void {
    toObservable(this.searchQuery, { injector: this.injector })
      .pipe(
        skip(1),
        debounceTime(400),
        distinctUntilChanged(),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe((searchTerm) => {
        this.handleSearchQueryChange(searchTerm);
      });
  }

  private setupAppliedFiltersSubscription(): void {
    // Update applied filters when query params change
    toObservable(this.queryParams, { injector: this.injector })
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => this.updateAppliedFilters());

    // Update applied filters when teaching languages are loaded
    toObservable(this.teachingLanguages, { injector: this.injector })
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => this.updateAppliedFilters());
  }

  private handleRouteParamsChange(params: Params): void {
    const updatedParams = this.mapUrlParamsToRequest(params);
    this.queryParams.set(updatedParams);
    this.loadPackagesData();
    // Note: updateAppliedFilters() will be called automatically by the subscription
  }

  private handleSearchQueryChange(searchTerm: string): void {
    const currentParams = this.queryParams();
    const updatedParams = {
      ...currentParams,
      searchTerm: searchTerm || null,
      pageNumber: 1 // Reset to first page when searching
    };
    this.queryParams.set(updatedParams);
    this.updateQueryParams(updatedParams);
    // Note: loadPackagesData() will be called automatically by the route params subscription
  }

  // ============================================================================
  // DATA LOADING
  // ============================================================================

  private loadPackagesData(): void {
    const request = this.queryParams();
    const cleanedRequest = this.packagesHelperService.cleanRequestForApi(request);

    this.handleApiService.getApiData<IGetPackagesResponse>({
      url: IPackage.getPackages,
      method: 'GET'
    }, cleanedRequest)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (response) => {
          if (response) {
            this.dataStateService.parentPackages.setState({
              loading: false,
              data: response,
              error: null,
              initialized: true,
              hasError: false,
              errorStatusCode: null
            });
            this.hasLoadedInitialData.set(true);
          }
        },
        error: (error) => {
          console.error('Failed to load packages:', error);
          this.dataStateService.parentPackages.setState({
            loading: false,
            data: null,
            error: 'Failed to load packages',
            initialized: true,
            hasError: true,
            errorStatusCode: error.status || 500
          });
          this.hasLoadedInitialData.set(true);
        }
      });
  }

  private loadTeachingLanguages(): void {
    this.handleApiService.getApiData<IGetAllTeachingLanguagesResponse>({
      url: TeachingLanguagesRoutes.getAllTeachingLanguages,
      method: 'GET'
    })
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (response) => {
          if (response?.teachingLanguages) {
            this.teachingLanguages.set(response.teachingLanguages);
          }
        },
        error: (error) => console.error('Failed to load teaching languages:', error)
      });
  }

  // ============================================================================
  // URL PARAMS MAPPING
  // ============================================================================

  private mapUrlParamsToRequest(params: Params): IGetPackagesRequest {
    const defaultRequest = this.packagesHelperService.createDefaultPackagesRequestForUser();

    return {
      ...defaultRequest,
      pageNumber: parseInt(params[this.fieldNames.pageNumber as string]) || defaultRequest.pageNumber,
      pageSize: parseInt(params[this.fieldNames.pageSize as string]) || defaultRequest.pageSize,
      sortColumn: params[this.fieldNames.sortColumn as string] || defaultRequest.sortColumn,
      sortDirection: params[this.fieldNames.sortDirection as string] || defaultRequest.sortDirection,
      searchTerm: params[this.fieldNames.searchTerm as string] || null,
      teachingLanguageId: params[this.fieldNames.teachingLanguageId as string] || null,
      purchasedFrom: params[this.fieldNames.purchasedFrom as string] ? new Date(params[this.fieldNames.purchasedFrom as string]) : null,
      purchasedTo: params[this.fieldNames.purchasedTo as string] ? new Date(params[this.fieldNames.purchasedTo as string]) : null,
      expiresFrom: params[this.fieldNames.expiresFrom as string] ? new Date(params[this.fieldNames.expiresFrom as string]) : null,
      expiresTo: params[this.fieldNames.expiresTo as string] ? new Date(params[this.fieldNames.expiresTo as string]) : null,
      hasAddOnExtension: params[this.fieldNames.hasAddOnExtension as string] === 'true' ? true : params[this.fieldNames.hasAddOnExtension as string] === 'false' ? false : null
    };
  }

  private updateQueryParams(request: IGetPackagesRequest): void {
    const queryParams: Params = {};

    // Add non-null parameters to query params
    if (request.pageNumber && request.pageNumber !== 1) queryParams[this.fieldNames.pageNumber as string] = request.pageNumber;
    if (request.pageSize && request.pageSize !== 15) queryParams[this.fieldNames.pageSize as string] = request.pageSize;
    if (request.sortColumn) queryParams[this.fieldNames.sortColumn as string] = request.sortColumn;
    if (request.sortDirection) queryParams[this.fieldNames.sortDirection as string] = request.sortDirection;
    if (request.searchTerm) queryParams[this.fieldNames.searchTerm as string] = request.searchTerm;
    if (request.teachingLanguageId) queryParams[this.fieldNames.teachingLanguageId as string] = request.teachingLanguageId;
    if (request.purchasedFrom) queryParams[this.fieldNames.purchasedFrom as string] = request.purchasedFrom.toISOString();
    if (request.purchasedTo) queryParams[this.fieldNames.purchasedTo as string] = request.purchasedTo.toISOString();
    if (request.expiresFrom) queryParams[this.fieldNames.expiresFrom as string] = request.expiresFrom.toISOString();
    if (request.expiresTo) queryParams[this.fieldNames.expiresTo as string] = request.expiresTo.toISOString();
    if (request.hasAddOnExtension !== null) queryParams[this.fieldNames.hasAddOnExtension as string] = request.hasAddOnExtension;

    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams,
      queryParamsHandling: 'replace'
    });
  }

  // ============================================================================
  // APPLIED FILTERS MANAGEMENT
  // ============================================================================

  private updateAppliedFilters(): void {
    const filters: IAppliedFilterTag[] = [];
    const params = this.queryParams();

    // Teaching Language filter
    if (params.teachingLanguageId) {
      const language = this.teachingLanguages().find(lang => lang.id === params.teachingLanguageId);
      if (language) {
        filters.push({
          id: this.fieldNames.teachingLanguageId!,
          label: `Teaching Language: ${language.name}`,
          type: 'select',
          icon: 'pi pi-globe',
          removable: true,
          removeData: { filterName: this.fieldNames.teachingLanguageId! }
        });
      }
    }

    // Purchase date filters
    if (params.purchasedFrom) {
      filters.push({
        id: this.fieldNames.purchasedFrom!,
        label: `Purchased From: ${new Date(params.purchasedFrom).toLocaleDateString()}`,
        type: 'date',
        icon: 'pi pi-calendar',
        removable: true,
        removeData: { filterName: this.fieldNames.purchasedFrom! }
      });
    }

    if (params.purchasedTo) {
      filters.push({
        id: this.fieldNames.purchasedTo!,
        label: `Purchased To: ${new Date(params.purchasedTo).toLocaleDateString()}`,
        type: 'date',
        icon: 'pi pi-calendar',
        removable: true,
        removeData: { filterName: this.fieldNames.purchasedTo! }
      });
    }

    // Expiry date filters
    if (params.expiresFrom) {
      filters.push({
        id: this.fieldNames.expiresFrom!,
        label: `Expires From: ${new Date(params.expiresFrom).toLocaleDateString()}`,
        type: 'date',
        icon: 'pi pi-calendar',
        removable: true,
        removeData: { filterName: this.fieldNames.expiresFrom! }
      });
    }

    if (params.expiresTo) {
      filters.push({
        id: this.fieldNames.expiresTo!,
        label: `Expires To: ${new Date(params.expiresTo).toLocaleDateString()}`,
        type: 'date',
        icon: 'pi pi-calendar',
        removable: true,
        removeData: { filterName: this.fieldNames.expiresTo! }
      });
    }

    // Add-on extension filter
    if (params.hasAddOnExtension !== null) {
      filters.push({
        id: this.fieldNames.hasAddOnExtension!,
        label: `Has Extension: ${params.hasAddOnExtension ? 'Yes' : 'No'}`,
        type: 'boolean',
        icon: 'pi pi-plus-circle',
        removable: true,
        removeData: { filterName: this.fieldNames.hasAddOnExtension! }
      });
    }

    this.appliedFilters.set(filters);
  }

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  onSearchQueryChange(searchTerm: string): void {
    this.searchQuery.set(searchTerm);
  }

  onSearchInputChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.onSearchQueryChange(target.value);
  }

  onPageChange(event: PaginatorState): void {
    const currentParams = this.queryParams();
    const first = event.first ?? 0;
    const rows = event.rows ?? this.pageSize();
    const pageNumber = event.page !== undefined ? event.page + 1 : Math.floor(first / rows) + 1;
    const updatedParams = {
      ...currentParams,
      pageNumber: pageNumber,
      pageSize: rows
    };
    this.queryParams.set(updatedParams);
    this.updateQueryParams(updatedParams);
    // Note: loadPackagesData() will be called automatically by the route params subscription
  }

  onFiltersDrawerToggle(): void {
    this.filtersDrawerVisible.set(!this.filtersDrawerVisible());
  }

  onFiltersDrawerAction(event: { action: string; data?: any }): void {
    const action = typeof event === 'string' ? event : event.action;

    if (action === 'apply') {
      this.packagesFiltersComponent?.emitSearchAction();
      this.filtersDrawerVisible.set(false);
    } else if (action === 'reset') {
      // Reset only clears temporary filter state, doesn't apply the reset
      // The reset will only be applied when user clicks "Apply Filters"
      this.packagesFiltersComponent?.resetFiltersToDefault();
      // Do NOT close the drawer - user should be able to see the reset state
      // and decide whether to apply it or make further changes
    }
  }

  onFilterChange(event: IOverviewPackagesFilterChangeEvent): void {
    // Handle real-time filter changes if needed
    console.debug('Filter changed:', event);
  }

  onFilterAction(event: IOverviewPackagesFilterActionEvent): void {
    const updatedParams = {
      ...event.filters,
      pageNumber: 1 // Reset to first page when applying filters
    };

    this.queryParams.set(updatedParams);
    this.updateQueryParams(updatedParams);
    // Note: loadPackagesData() will be called automatically by the route params subscription
    // Note: updateAppliedFilters() will be called automatically by the subscription
  }

  onAppliedFilterRemove(event: { filter?: IAppliedFilterTag; event?: MouseEvent } | string): void {
    const filterName = typeof event === 'string' ? event : event.filter?.removeData?.filterName;
    const currentParams = this.queryParams();
    const updatedParams = { ...currentParams };

    // Remove the specific filter
    switch (filterName) {
      case this.fieldNames.teachingLanguageId:
        updatedParams.teachingLanguageId = null;
        break;
      case this.fieldNames.purchasedFrom:
        updatedParams.purchasedFrom = null;
        break;
      case this.fieldNames.purchasedTo:
        updatedParams.purchasedTo = null;
        break;
      case this.fieldNames.expiresFrom:
        updatedParams.expiresFrom = null;
        break;
      case this.fieldNames.expiresTo:
        updatedParams.expiresTo = null;
        break;
      case this.fieldNames.hasAddOnExtension:
        updatedParams.hasAddOnExtension = null;
        break;
    }

    updatedParams.pageNumber = 1; // Reset to first page
    this.queryParams.set(updatedParams);
    this.updateQueryParams(updatedParams);
    // Note: loadPackagesData() will be called automatically by the route params subscription
    // Note: updateAppliedFilters() will be called automatically by the subscription
  }

  onAppliedFiltersReset(): void {
    const defaultParams = this.packagesHelperService.createDefaultPackagesRequestForUser();
    this.queryParams.set(defaultParams);
    this.updateQueryParams(defaultParams);
    // Note: loadPackagesData() will be called automatically by the route params subscription
    // Note: updateAppliedFilters() will be called automatically by the subscription
  }
}
