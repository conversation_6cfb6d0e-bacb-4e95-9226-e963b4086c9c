@use 'mixins';

.students-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .page-header {
    flex-shrink: 0;
    
    h1 {
      @include mixins.breakpoint(mobile) {
        font-size: 1.5rem;
      }
    }
    
    .flex {
      @include mixins.breakpoint(mobile) {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
      }
    }
  }

  .applied-filters-section {
    flex-shrink: 0;
  }

  .data-grid-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;

    .search-section {
      flex-shrink: 0;
      
      @include mixins.breakpoint(mobile) {
        .p-input-icon-left {
          width: 100% !important;
        }
      }
    }

    .p-datatable-wrapper {
      flex: 1;
      min-height: 0;
    }
  }
}

// Table styling
:host ::ng-deep {
  .p-datatable {
    .p-datatable-header {
      background: var(--surface-section);
      border: 1px solid var(--surface-border);
      padding: 1rem;
    }

    .p-datatable-thead > tr > th {
      background: var(--surface-section);
      border-color: var(--surface-border);
      font-weight: 600;
      padding: 0.75rem;
      
      @include mixins.breakpoint(mobile) {
        padding: 0.5rem;
        font-size: 0.875rem;
      }
    }

    .p-datatable-tbody > tr > td {
      border-color: var(--surface-border);
      padding: 0.75rem;
      
      @include mixins.breakpoint(mobile) {
        padding: 0.5rem;
        font-size: 0.875rem;
      }
    }

    .p-datatable-tbody > tr:hover {
      background: var(--surface-hover);
    }

    // Action buttons styling
    .p-button {
      &.p-button-rounded.p-button-text {
        width: 2rem;
        height: 2rem;
        
        @include mixins.breakpoint(mobile) {
          width: 1.75rem;
          height: 1.75rem;
          
          .p-button-icon {
            font-size: 0.75rem;
          }
        }
      }
    }

    // Responsive table
    @include mixins.breakpoint(mobile) {
      .p-datatable-wrapper {
        overflow-x: auto;
      }
      
      table {
        min-width: 800px;
      }
    }
  }

  // Paginator styling
  .p-paginator {
    background: var(--surface-section);
    border: 1px solid var(--surface-border);
    border-top: none;
    padding: 0.75rem;
    
    @include mixins.breakpoint(mobile) {
      padding: 0.5rem;
      
      .p-paginator-pages {
        display: none;
      }
      
      .p-dropdown {
        .p-dropdown-label {
          padding: 0.25rem 0.5rem;
          font-size: 0.875rem;
        }
      }
    }
  }

  // Tags styling
  .p-tag {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    
    @include mixins.breakpoint(mobile) {
      font-size: 0.625rem;
      padding: 0.125rem 0.375rem;
    }
  }

  // Button styling
  .p-button {
    @include mixins.breakpoint(mobile) {
      &:not(.p-button-rounded) {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
      }
    }
  }

  // Badge styling
  .p-badge {
    min-width: 1.25rem;
    height: 1.25rem;
    line-height: 1.25rem;
    font-size: 0.625rem;
    
    @include mixins.breakpoint(mobile) {
      min-width: 1rem;
      height: 1rem;
      line-height: 1rem;
      font-size: 0.5rem;
    }
  }
}

// Responsive adjustments
@include mixins.breakpoint(mobile) {
  .students-list-container {
    padding: 1rem;
  }
}

// Loading state
:host ::ng-deep {
  .p-datatable-loading-overlay {
    background: rgba(255, 255, 255, 0.8);
    
    .p-datatable-loading-icon {
      font-size: 2rem;
      color: var(--primary-color);
    }
  }
}

// Dark theme adjustments
:host-context(.dark-theme) {
  :host ::ng-deep {
    .p-datatable-loading-overlay {
      background: rgba(0, 0, 0, 0.8);
    }
  }
}

// Empty state styling
:host ::ng-deep {
  .p-datatable-emptymessage {
    td {
      border: none !important;
      background: var(--surface-ground) !important;
    }
  }
}

// ============================================================================
// SEARCH INPUT STYLING
// ============================================================================
.search-input-container {
  position: relative;
  width: 100%;

  input {
    padding-right: 30px;
    /* Make room for the button */
    width: 100%;
  }

  .search-clear-button {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;

    &:hover {
      color: #000;
    }

    i {
      font-size: 14px;
    }
  }
}
