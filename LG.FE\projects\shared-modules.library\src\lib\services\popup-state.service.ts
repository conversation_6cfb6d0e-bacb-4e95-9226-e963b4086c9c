import { Injectable } from '@angular/core';
interface PopupState {
  show: boolean;
  data?: {
    expiresAt?: string;
    [key: string]: unknown;
  };
}

interface PopupStates {
  [key: string]: PopupState;
}

export interface OtpPopupState {
  show: boolean;
  data: {
      emailAddress: string;
  };
}

export interface OtpDialogParams {
  sendOtpRequest: boolean;
  canStartTimer: boolean;
  dialogId: string;
  emailAddress: string;
}

@Injectable({ providedIn: 'root' })
export class PopupStateService {
  private readonly POPUP_STATE_KEY = 'popup_states'; // Key for SessionStorage

  /**
   * Set or update the state of a specific popup in SessionStorage
   * @param popupId Unique identifier for the popup
   * @param state State object to store (e.g., { show: true, data: {...} })
   */
  setPopupState(popupId: string, state: PopupState): void {
    const currentStates = this.getAllPopupStates();
    currentStates[popupId] = state;
    this.saveAllPopupStates(currentStates);
  }

  /**
   * Get the state of a specific popup from SessionStorage
   * @param popupId Unique identifier for the popup
   * @returns Stored state or null if not found
   */
  getPopupState<T extends PopupState>(popupId: string): T | null {
    const allStates = this.getAllPopupStates();
    return (allStates[popupId] as T) || null;
  }

  /**
   * Check if a popup should be shown based on its state
   * @param popupId Unique identifier for the popup
   * @param callback Optional callback to execute when popup should be shown
   * @returns Boolean indicating if the popup should be visible
   */
  checkPopupState(popupId: string, callback?: (state: PopupState) => void): boolean {
    const state = this.getPopupState<PopupState>(popupId);
    console.log(`Checking state for ${popupId}:`, state); // Debug

    if (this.isPopupExpired(popupId)) {
      return false;
    } else if (state?.show) {
      if (callback) {
        callback(state);
      }
      return true;
    }
    return false;
  }

  /**
   * Clear the state of a specific popup from SessionStorage
   * @param popupId Unique identifier for the popup
   * @returns Boolean indicating if the state was cleared successfully
   */
  clearPopupState(popupId: string): boolean {
    try {
      const currentStates = this.getAllPopupStates();
      if (currentStates[popupId] !== undefined) {
        delete currentStates[popupId];
        this.saveAllPopupStates(currentStates);
        console.log(`Cleared state for ${popupId}. New state:`, this.getAllPopupStates()); // Debug
        return true;
      }
      console.log(`No state found for ${popupId} to clear.`); // Debug
      return false;
    } catch (error) {
      console.error(`Failed to clear state for ${popupId}:`, error);
      return false;
    }
  }

  /**
   * Clear all popup states from SessionStorage
   * @returns Boolean indicating if all states were cleared successfully
   */
  clearAllPopupStates(): boolean {
    try {
      sessionStorage.removeItem(this.POPUP_STATE_KEY);
      console.log('All popup states cleared. Current state:', this.getAllPopupStates()); // Debug
      return true;
    } catch (error) {
      console.error('Failed to clear all popup states:', error);
      return false;
    }
  }

  /**
   * Update specific properties of a popup's state
   * @param popupId Unique identifier for the popup
   * @param updates Partial state updates
   */
  updatePopupState(popupId: string, updates: Partial<PopupState>): void {
    const currentState = this.getPopupState<any>(popupId) || {};
    this.setPopupState(popupId, { ...currentState, ...updates });
  }

  /**
   * Check if a popup's state has expired (if expiration is set)
   * @param popupId Unique identifier for the popup
   * @returns Boolean indicating if the popup is expired
   */
  isPopupExpired(popupId: string): boolean {
    const state = this.getPopupState<any>(popupId);
    if (state?.data?.expiresAt) {
      const expiresAt = new Date(state.data.expiresAt);
      if (expiresAt < new Date()) {
        this.clearPopupState(popupId);
        return true;
      }
    }
    return false;
  }

  /**
   * Get all popup states from SessionStorage
   * @returns Object with popup IDs as keys and their states as values
   */
  private getAllPopupStates(): PopupStates {
    try {
      const storedData = sessionStorage.getItem(this.POPUP_STATE_KEY);
      return storedData ? JSON.parse(storedData) : {};
    } catch (error) {
      console.error('Failed to parse popup states from SessionStorage:', error);
      return {};
    }
  }

  /**
   * Save all popup states to SessionStorage
   * @param states Object containing all popup states
   * @throws Error if saving fails
   */
  private saveAllPopupStates(states: PopupStates): void {
    try {
      sessionStorage.setItem(this.POPUP_STATE_KEY, JSON.stringify(states));
      console.log('Saved states to SessionStorage:', states); // Debug
    } catch (error) {
      console.error('Failed to save popup states to SessionStorage:', error);
      throw error; // Re-throw to allow callers to handle
    }
  }
}