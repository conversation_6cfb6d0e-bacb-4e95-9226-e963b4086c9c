// student-list-component-helper.service.ts
import { Injectable, inject } from '@angular/core';
import { Table } from 'primeng/table';
import { ISearchStudentDto } from 'SharedModules.Library';

import {
  IGetStudentsResponse,
  EnumDropdownOptionsService,
  IDataGridFields,
  nameOf,
  IGetStudentsRequest,
  IGenderEnum,
} from "SharedModules.Library";
import moment from 'moment-timezone';
import { Params } from "@angular/router";
import { BaseListHelperService } from './base-list-helper.service';

const ISearchStudentDtoParamsMap = nameOf<ISearchStudentDto>();

@Injectable({
  providedIn: 'root'
})
export class StudentListComponentHelperService extends BaseListHelperService<
  IGetStudentsRequest,
  IGetStudentsResponse,
  ISearchStudentDto
> {
  enumDropdownOptionsService = inject(EnumDropdownOptionsService);

  // Default sort configuration - centralized in one place
  public static readonly DEFAULT_SORT_COLUMN = ISearchStudentDtoParamsMap.registeredDate!;
  public static readonly DEFAULT_SORT_DIRECTION = 'desc';
  public static readonly DEFAULT_PAGE_SIZE = 10;

  constructor() {
    super();
  }

  // ============================================================================
  // ABSTRACT METHOD IMPLEMENTATIONS - Required by BaseListHelperService
  // ============================================================================

  getDefaultSortColumn(): string {
    return StudentListComponentHelperService.DEFAULT_SORT_COLUMN;
  }

  getDefaultSortDirection(): 'asc' | 'desc' {
    return StudentListComponentHelperService.DEFAULT_SORT_DIRECTION as 'asc' | 'desc';
  }

  getDefaultPageSize(): number {
    return StudentListComponentHelperService.DEFAULT_PAGE_SIZE;
  }

  getEntityName(): string {
    return 'students';
  }

  getFieldNames(): any {
    return nameOf<IGetStudentsRequest>();
  }

  createDefaultRequest(): IGetStudentsRequest {
    return {
      pageNumber: 1,
      pageSize: this.getDefaultPageSize(),
      sortColumn: this.getDefaultSortColumn(),
      sortDirection: this.getDefaultSortDirection(),
      searchTerm: null,
      teachingLanguage: null,
      teachingLanguageLevel: null,
      accountStatus: null,
      gender: 0,
      includeBlocked: false,
      studentAgesMin: 2,
      studentAgesMax: 17,
      registeredFrom: null,
      registeredTo: null
    };
  }

  mapUrlParamsToRequest(params: Params): IGetStudentsRequest {
    // Use the base class method for common parameters, then add student-specific ones
    return this.mapQueryParamsToRequest(params);
  }

  /**
   * Maps URL query parameters to an IGetStudentsRequest object
   * @param params Route parameters from ActivatedRoute
   * @param defaultRequest Optional default request to use as base (will create one if not provided)
   * @returns IGetStudentsRequest populated with values from URL parameters
   */
  mapQueryParamsToStudentsRequest(params: Params, defaultRequest?: IGetStudentsRequest): IGetStudentsRequest {
    // Use the base class method for common parameters, then add student-specific ones
    return this.mapQueryParamsToRequest(params, defaultRequest);
  }

  /**
   * Override to handle student-specific URL parameters
   */
  protected override mapEntitySpecificParams(params: Params, request: IGetStudentsRequest): void {
    const paramsMap = nameOf<IGetStudentsRequest>();

    // Student-specific parameters using nameOf for type safety
    if (params[paramsMap.gender] !== undefined) {
      request.gender = +params[paramsMap.gender];
    }
    if (params[paramsMap.registeredFrom!] !== undefined && params[paramsMap.registeredFrom!] !== 'null') {
      request.registeredFrom = new Date(params[paramsMap.registeredFrom!]);
    }
    if (params[paramsMap.registeredTo!] !== undefined && params[paramsMap.registeredTo!] !== 'null') {
      request.registeredTo = new Date(params[paramsMap.registeredTo!]);
    }
    if (params[paramsMap.teachingLanguage!] !== undefined && params[paramsMap.teachingLanguage!] !== 'null') {
      request.teachingLanguage = params[paramsMap.teachingLanguage!];
    }
    if (params[paramsMap.teachingLanguageLevel!] !== undefined && params[paramsMap.teachingLanguageLevel!] !== 'null') {
      request.teachingLanguageLevel = +params[paramsMap.teachingLanguageLevel!];
    }
    if (params[paramsMap.includeBlocked!] !== undefined) {
      request.includeBlocked = params[paramsMap.includeBlocked!] === 'true';
    }
    if (params[paramsMap.accountStatus!] !== undefined && params[paramsMap.accountStatus!] !== 'null') {
      request.accountStatus = +params[paramsMap.accountStatus!];
    }
    if (params[paramsMap.studentAgesMin] !== undefined) {
      request.studentAgesMin = +params[paramsMap.studentAgesMin];
    }
    if (params[paramsMap.studentAgesMax] !== undefined) {
      request.studentAgesMax = +params[paramsMap.studentAgesMax];
    }
    if (params[paramsMap.speakingLanguage!] !== undefined && params[paramsMap.speakingLanguage!] !== 'null') {
      request.speakingLanguage = params[paramsMap.speakingLanguage!];
    }
    if (params[paramsMap.isNativeSpeakingLanguage!] !== undefined) {
      request.isNativeSpeakingLanguage = params[paramsMap.isNativeSpeakingLanguage!] === 'true';
    }
    if (params[paramsMap.teacherId!] !== undefined && params[paramsMap.teacherId!] !== 'null') {
      request.teacherId = params[paramsMap.teacherId!];
    }
    if (params[paramsMap.parentId!] !== undefined && params[paramsMap.parentId!] !== 'null') {
      request.parentId = params[paramsMap.parentId!];
    }
  }

  // ============================================================================
  // STUDENT-SPECIFIC METHODS
  // ============================================================================

  /**
   * Creates a default IGetStudentsRequest object with standard values
   * @param searchStudentDtoFieldNames Optional object containing field names to customize the default sortColumn
   * @returns IGetStudentsRequest with default values
   */
  createDefaultStudentsRequest(searchStudentDtoFieldNames?: ISearchStudentDto): IGetStudentsRequest {
    const request = this.createDefaultRequest();
    if (searchStudentDtoFieldNames?.firstName) {
      request.sortColumn = searchStudentDtoFieldNames.firstName;
    }
    return request;
  }

  /**
   * Export student table data to Excel
   */
  exportStudentTable(table: Table, cols: IDataGridFields[], studentsResponse: IGetStudentsResponse): void {
    // Use the base class export functionality
    this.exportTable(table, cols, studentsResponse);
  }

  /**
   * Initialize table columns with student-specific configuration
   * Implementation of abstract method from BaseListHelperService
   */
  initializeTableColumns(): IDataGridFields[] {
    const searchStudentDtoFieldNames = nameOf<ISearchStudentDto>();

    return [
      { field: searchStudentDtoFieldNames.firstName, header: 'First Name', sortable: true, maxWidth: "150px" },
      { field: searchStudentDtoFieldNames.lastName, header: 'Last Name', sortable: true, maxWidth: "150px" },
      { field: searchStudentDtoFieldNames.gender!, header: 'Gender', sortable: true, maxWidth: "100px" },
      { field: searchStudentDtoFieldNames.country, header: 'Country', sortable: true, maxWidth: "120px" },
      { field: searchStudentDtoFieldNames.city!, header: 'City', sortable: false, maxWidth: "120px" },
      { field: searchStudentDtoFieldNames.speakingLanguages, header: 'Speaking Languages', sortable: false, maxWidth: "200px" },
      { field: searchStudentDtoFieldNames.studentTeachingLanguageDto, header: 'Learning Languages', sortable: false, maxWidth: "200px" },
      { field: searchStudentDtoFieldNames.registeredDate!, header: 'Registration Date', sortable: true, maxWidth: "140px" },
      { field: searchStudentDtoFieldNames.lastAccountStatusUpdate!, header: 'Last Acc Status Update', sortable: false, hide: true }, // Hidden by default
      { field: searchStudentDtoFieldNames.accountStatus, header: 'Account Status', sortable: true, maxWidth: "120px" },
      { field: searchStudentDtoFieldNames.isBlocked, header: 'Blocked', sortable: true, maxWidth: "80px" }
    ];
  }

  /**
   * Get display columns for students table (public method)
   */
  getStudentDisplayColumns(): IDataGridFields[] {
    return this.initializeTableColumns();
  }

  /**
   * Formats a single student record for CSV export
   * This contains all the student-specific formatting logic
   * Implementation of abstract method from BaseListHelperService
   */
  formatItemForExport(student: ISearchStudentDto, cols: IDataGridFields[]): Record<string, string> {
    const exportObject: Record<string, string> = {};
    const studentFields = nameOf<ISearchStudentDto>();

    // Initialize all column fields to empty strings to ensure consistent structure
    for (const col of cols) {
      exportObject[col.field] = '';
    }

    // Format each field with proper display values using nameOf for type safety
    if (cols.some(col => col.field === studentFields.firstName)) {
      exportObject[studentFields.firstName] = this.getSafeStringValue(student.firstName);
    }
    if (cols.some(col => col.field === studentFields.lastName)) {
      exportObject[studentFields.lastName] = this.getSafeStringValue(student.lastName);
    }
    if (cols.some(col => col.field === studentFields.userId)) {
      exportObject[studentFields.userId] = this.getSafeStringValue(student.userId);
    }
    if (cols.some(col => col.field === studentFields.country)) {
      exportObject[studentFields.country] = this.getSafeStringValue(student.country);
    }
    if (cols.some(col => col.field === studentFields.registeredDate!)) {
      exportObject[studentFields.registeredDate!] = this.formatDateForExport(student.registeredDate);
    }
    if (cols.some(col => col.field === studentFields.accountStatus)) {
      exportObject[studentFields.accountStatus] = this.getAccountStatusDisplayText(student.accountStatus || 0);
    }
    if (cols.some(col => col.field === studentFields.isBlocked)) {
      exportObject[studentFields.isBlocked] = student.isBlocked ? 'Blocked' : 'Active';
    }
    if (cols.some(col => col.field === studentFields.gender!)) {
      exportObject[studentFields.gender!] = this.getGenderDisplayText(student.gender || 0);
    }

    return exportObject;
  }

  /**
   * Format UTC date to admin localized format
   */
  formatUtcDateToAdminLocalized(date: string | Date): string {
    return date ? moment.utc(date).tz('Europe/Athens').format('DD/MM/YYYY HH:mm') : '';
  }

  /**
   * Get gender display text
   */
  getGenderDisplayText(gender: IGenderEnum): string {
    const genderOption = this.enumDropdownOptionsService.genderOptions.find(option => option.value === gender);
    return genderOption?.label || 'Unknown';
  }

  /**
   * Get account status display text
   */
  getAccountStatusDisplayText(status: number): string {
    const statusOption = this.enumDropdownOptionsService.userAccountStatusOptions.find(option => option.value === status);
    return statusOption?.label || 'Unknown';
  }

  /**
   * Clean request for API call (remove null/undefined values, format dates)
   */
  cleanRequestForApi(request: IGetStudentsRequest): Partial<IGetStudentsRequest> {
    const cleanedRequest: Partial<IGetStudentsRequest> = {};

    // Copy non-null values
    Object.keys(request).forEach(key => {
      const value = (request as any)[key];
      if (value !== null && value !== undefined && value !== '') {
        if (key === 'registeredFrom' || key === 'registeredTo') {
          // Format dates for API
          cleanedRequest[key as keyof IGetStudentsRequest] = value instanceof Date
            ? value.toISOString().split('T')[0]
            : value;
        } else {
          cleanedRequest[key as keyof IGetStudentsRequest] = value;
        }
      }
    });

    return cleanedRequest;
  }
}
