import { CommonModule } from '@angular/common';
import { Component, computed, EventEmitter, inject, Injector, input, type OnInit, Output, signal } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { TextareaModule } from 'primeng/textarea';
import { DropdownModule } from 'primeng/dropdown';
import { toObservable } from '@angular/core/rxjs-interop';

import { ButtonModule } from 'primeng/button';
import { AvailabilityPickerDaysComponent } from '../../../availability-picker-days/availability-picker-days.component';
import { AvailabilityTimezoneSelectorComponent, ITimeZoneIdData } from '../../../availability-picker-days/availability-timezone-selector/availability-timezone-selector.component';

import { Severity } from '../../../../models/severity';
import { GroupDialogState, IUserRole } from '../../../../models/general.model';
import { IApiResponseBase, IAvailability, ICreateStudentGroupRequest, IFindCommonTimeSlotsResponse, IGetAvailabilityResponse, IStudentGroupDto, IStudentLevelEnum, IWeekDayTimeSlotDto } from '../../../../GeneratedTsFiles';
import { DataApiStateService } from '../../../../services/data-api-state.service';
import { GeneralService } from '../../../../services/general.service';
import { HandleApiResponseService } from '../../../../services/handle-api-response.service';
import { AuthStateService } from '../../../../services/auth-state.service';
import { untilDestroyed } from '../../../../helpers/until-destroyed';
import { StudentGroupService } from '../../../../services/student-group.service';

@Component({
  selector: 'app-student-group-selection-availability-step',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TextareaModule,
    DropdownModule,
    ButtonModule,
    AvailabilityPickerDaysComponent,
    AvailabilityTimezoneSelectorComponent,
  ],
  templateUrl: './student-group-selection-availability-step.component.html',
  styleUrl: './student-group-selection-availability-step.component.scss'
})
export class StudentGroupSelectionAvailabilityStepComponent implements OnInit {
  // Constants and Enums
  protected readonly GroupDialogState = GroupDialogState;
  readonly EditGroupStateEnum = GroupDialogState;
  readonly IStudentLevelEnum = IStudentLevelEnum;
  readonly IStudentAvailabilityEnum = [];

  // Form Options
  readonly levels = [
    { label: 'Select a Level *', value: null },
    { label: 'No Experience', value: IStudentLevelEnum.NoExperience },
    { label: 'Beginner', value: IStudentLevelEnum.Beginner },
    { label: 'Intermediate', value: IStudentLevelEnum.Intermediate },
    { label: 'Advanced', value: IStudentLevelEnum.Advanced }
  ];

  // Services
  private readonly formBuilder = inject(FormBuilder);
  private readonly studentGroupService = inject(StudentGroupService);
  private readonly dataApiStateService = inject(DataApiStateService);
  private readonly apiService = inject(HandleApiResponseService);
  readonly authService = inject(AuthStateService);
  readonly generalService = inject(GeneralService);
  private readonly untilDestroyed = untilDestroyed();

  // Inputs and Outputs
  studentGroupItem = input.required<IStudentGroupDto>({});
  editGroupState = input<GroupDialogState>(GroupDialogState.None);
  @Output() groupStateChanged = new EventEmitter<any>();
  @Output() onAvailabilityStepValuesChanged = new EventEmitter<Partial<ICreateStudentGroupRequest>>();
  @Output() onAvailabilityStepValidityChanged = new EventEmitter<boolean>();
  @Output() availabilityDetailsFormValid = new EventEmitter<boolean>();

  // Component State
  times: { time: string; flag: any; checked: boolean; day: string }[] = [];
  selectedFlags: number | undefined | any = undefined;
  availabilityForm: FormGroup = new FormGroup({});
  availabilityData = signal<IWeekDayTimeSlotDto[]>([]);
  getGroupResponse = signal<IGetAvailabilityResponse>({} as IGetAvailabilityResponse);
  availabilityTimezone: ITimeZoneIdData = {} as ITimeZoneIdData;
  availabilityDataLoaded = signal(false);
  areAllCommonTimeSlotsEmptyBool = signal(true);
  invalidFields: string[] = [];
  students$ = computed(() => this.dataApiStateService.parentStudents.state() || []);
  user$ = computed(() => this.authService.getUserClaims());
  private readonly injector = inject(Injector);
  isPanelClosed = false;

  ngOnInit(): void {
    console.log(this.studentGroupItem());
    this.editGroupState() === GroupDialogState.EditGroup
      ? this.getGroupAvailability(this.studentGroupItem().availabilityId!)
      : this.initializeComponent();

      console.log('students$', this.students$());


      console.log('this.studentGroupService.getCreateStudentGroupRequest().studentsToAdd', this.studentGroupService.getCreateStudentGroupRequest().studentsToAdd);
  }

  // API Methods
  fetchCommonTimeSlots(studentIds: string[]): void {
    console.log(studentIds);
    this.apiService.findCommonTimeSlots({ studentIds })
      .pipe(this.untilDestroyed())
      .subscribe({
        next: (response: IFindCommonTimeSlotsResponse) => this.handleFetchSuccess(response),
        error: (error: IApiResponseBase) => this.handleFetchError(error),
      });
  }

  getGroupAvailability(availabilityId: string): void {
    this.apiService.getApiData<IGetAvailabilityResponse>(
      { url: IAvailability.getAvailability, method: 'GET' },
      { availabilityId }
    ).pipe(this.untilDestroyed()).subscribe({
      next: (response: IGetAvailabilityResponse) => this.handleGetAvailabilitySuccess(response),
      error: () => this.availabilityDataLoaded.set(true),
    });
  }

  // Event Handlers
  onTimeSlotsChange(timeSlots: IWeekDayTimeSlotDto[]): void {
    console.log(timeSlots);

    if (this.editGroupState() === GroupDialogState.EditGroup) {
      this.studentGroupService.updateEditStudentGroupRequest({
        availability: {
          ...this.studentGroupService.getEditStudentGroupRequest().availability,
          weekDayTimeSlots: timeSlots
        }
      });
    } else {
      this.updateStudentGroupRequest( { availabilityDto: {
        weekDayTimeSlots: timeSlots,
        groupId: this.studentGroupItem().id,
      } });
    }
    console.log(this.studentGroupService.getEditStudentGroupRequest());

  }

  onTimeZoneChanged(timezone: ITimeZoneIdData): void {
    console.log(timezone);
    this.studentGroupService.updateEditStudentGroupRequest({
      availability: {
        ...this.studentGroupService.getEditStudentGroupRequest().availability,
        timeZoneDisplayName: timezone.timeZoneDisplayName,
        timeZoneIana: timezone.timeZoneIana,
      }
    });
  }

  onAvailabilityDaysFormValid(valid: boolean): void {
    this.onAvailabilityStepValidityChanged.emit(valid);
  }

  // Form Methods
  private initializeForm(): void {
    this.availabilityForm = this.formBuilder.group({
      moreDetails: [undefined, [Validators.maxLength(1500)]],
      studentLevel: [undefined, [Validators.required]],
    });
  }

  // Subscription Methods
  private subscribeToFormChanges(): void {
    this.availabilityForm.valueChanges.pipe(this.untilDestroyed()).subscribe(values => {
      console.log('Form Values Changed:', values);
    });

    this.availabilityForm.statusChanges.pipe(this.untilDestroyed()).subscribe(status => {
      this.handleFormStatusChange(status);
    });
  }

  private subscribeToFindCommonTimeSlots(): void {
    toObservable(this.students$, { injector: this.injector }).pipe(this.untilDestroyed()).subscribe({
      next: (user: any) => {
        if (user.data?.pageData) {
          this.fetchCommonTimeSlots(this.studentGroupService.getCreateStudentGroupRequest().studentsToAdd);
        }
      },
    });
  }

  // Helper Methods
  private areAllCommonTimeSlotsEmpty(data: IWeekDayTimeSlotDto[]): boolean {
    return data.every(item => item.timeSlots.length === 0);
  }

  private updateStudentGroupRequest(updates: Partial<ICreateStudentGroupRequest>): void {
    this.studentGroupService.updateCreateStudentGroupRequest({
      ...this.studentGroupService.getCreateStudentGroupRequest(),
      ...updates,
    });
  }

  private handleValidForm(): void {
    const formValues = this.availabilityForm.value;
    this.updateStudentGroupRequest({
      teachingLanguageId: this.studentGroupService.getCreateStudentGroupRequest().teachingLanguageId,
      studentLevel: formValues.studentLevel,
      moreDetails: formValues.moreDetails,
    });
    this.onAvailabilityStepValuesChanged.emit(formValues);
  }

  // Handler Methods
  private handleFetchSuccess(response: IFindCommonTimeSlotsResponse): void {
    console.log(response);
    this.availabilityDataLoaded.set(true);
    this.availabilityData.set(response.availability.weekDayTimeSlots);
    this.areAllCommonTimeSlotsEmptyBool.set(this.areAllCommonTimeSlotsEmpty(response.availability.weekDayTimeSlots));
  }

  private handleFetchError(error: any): void {
    console.error('Error fetching common time slots:', error);
    this.availabilityDataLoaded.set(true);
  }

  private handleGetAvailabilitySuccess(response: IGetAvailabilityResponse): void {
    this.getGroupResponse.set(response);
    this.availabilityTimezone = {
      timeZoneDisplayName: response.availability.timeZoneDisplayName,
      timeZoneIana: response.availability.timeZoneIana,
    };
    this.availabilityDataLoaded.set(true);
    this.availabilityData.set(response.availability.weekDayTimeSlots);

    this.studentGroupService.updateEditStudentGroupRequest({
      availability: {
        ...this.studentGroupService.getEditStudentGroupRequest().availability,
        ...this.availabilityTimezone,
        availabilityId: response.availability.availabilityId,
        weekDayTimeSlots: response.availability.weekDayTimeSlots,
        timeZoneDisplayName: response.availability.timeZoneDisplayName,
        timeZoneId: response.availability.timeZoneId,
        timeZoneIana: response.availability.timeZoneIana,
      }
    });

    console.log("🚀 ~ StudentGroupSelectionAvailabilityStepComponent ~ handleGetAvailabilitySuccess ~ availabilityData:", this.studentGroupService.getEditStudentGroupRequest());

  }

  private handleFormStatusChange(status: string): void {
    console.log('Form Status Changed:', status);
    this.invalidFields = this.generalService.findInvalidControls(this.availabilityForm);
    this.availabilityDetailsFormValid.emit(status === 'VALID');
    if (status === 'INVALID') {
      console.error('Form is invalid');
    } else {
      console.log('Form is valid');
      this.handleValidForm();
    }
    this.onAvailabilityStepValidityChanged.emit(status === 'VALID');
  }

  private initializeComponent(): void {
    this.initializeForm();
    this.subscribeToFormChanges();
    this.subscribeToFindCommonTimeSlots();
  }
}