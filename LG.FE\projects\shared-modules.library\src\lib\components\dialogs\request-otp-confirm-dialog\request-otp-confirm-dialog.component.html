<app-custom-dialog-popup [header]="'Title'" [maximizable]="false" [styleClass]="dialogStyleClass()"
    [style]="dialogStyle$()" [visible]="dialogVisible()" (visibleChange)="onDialogVisibleChange($event)">
    <ng-container dialogHeader>
        <div
            class=" w-full inline-flex align-items-center text-white font-semibold justify-content-center gap-2 fluid-title">
         Confirm Email
        </div>
        <!-- Header content here -->
    </ng-container>
    <div class="w-full flex flex-column align-items-center justify-content-center mb-3 text-md primary-purple-color">


        <div class="mt-2">
            <app-actionable-alert iconUrl="/assets/images/graphic/6427857_connection_contact_email_website_icon.svg"
                title="Verify Email" message="Verify your email to activate this address."
                buttonIcon="pi pi-eye" alertClass="bg-indigo-100 border-1 border-indigo-200 border-4" imageClass="''"
                alertTextHeaderClass="" alertTextSubHeaderClass="">
            </app-actionable-alert>
        </div>

    </div>
    <app-otp-input [emailAddress]="emailAddress()" [shouldSaveTimerKey]="false" [sendOtpOnLoad]="false" [timerValueSeconds]="120" [requestOtp]="false"
        [showSubmitButton]="false" [canStartTimer]="canStartTimer()" [resendDisabled]="canStartTimer()" (otpCompleted)="onOtpCompleted($event)"
        (otpResendRequested)="otpRequest(true)"></app-otp-input>
  
    <ng-container dialogFooter>
        <div class="flex flex-column md:flex-row align-items-center w-full justify-content-center mt-3 md:px-5">
            <!-- <p-button [rounded]="true" [outlined]="true" label="Skip for now" pRipple role="button"
                styleClass="primary-purple-color" (click)="onDialogVisibleChange(false)"></p-button> -->
            <!-- <p-button [rounded]="true" [routerLink]="['/auth/login']" (click)="closeDialog()"
                class="border-round-3xl gradient-purple-btn text-sm" icon="pi pi-chevron-right" iconPos="right"
                label="Log in my Account" role="button" styleClass=" border-round-3xl" type="submit"></p-button> -->
        </div>
    </ng-container>

</app-custom-dialog-popup>