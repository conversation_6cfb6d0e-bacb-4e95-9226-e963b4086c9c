import { Params } from '@angular/router';
import { IBasedDataGridRequest, IBasedDataGridResponse } from '../../GeneratedTsFiles';
import { IDataGridFields } from '../../models/datagrid-fields.model';
import { IAppliedFilterTag } from '../applied-filters-tags/applied-filters-tags.component';

/**
 * Strongly typed sort direction values
 */
export type SortDirection = 'asc' | 'desc';

/**
 * Strongly typed filter action types
 */
export type FilterActionType = 'search' | 'reset';

/**
 * Strongly typed pagination and sort events from PrimeNG Table
 */
export interface IPaginationEvent {
  first: number; 
  rows: number; 
  page: number; 
  pageCount: number;
}

export interface ISortEvent {
  field: string; 
  order: 1 | -1; 
  multiSortMeta?: Array<{ field: string; order: 1 | -1 }>;
}

/**
 * Filter event interfaces
 */
export interface IDataGridFilterChangeEvent<TRequest> {
  filterName: keyof TRequest; 
  value: any; 
  resetPage?: boolean;
}

export interface IDataGridFilterActionEvent<TRequest> {
  action: FilterActionType; 
  filters: TRequest;
}

/**
 * Configuration for applied filters display and behavior in data grids
 */
export interface IDataGridAppliedFiltersConfig<TRequest> {
  convertToFilterTags?: (request: TRequest, urlParams: Params) => IAppliedFilterTag[];
  getFiltersCount?: (filters: IAppliedFilterTag[]) => number;
}

/**
 * Filter drawer types and interfaces
 */
export type FilterDrawerActionType = 'apply' | 'reset' | 'close';
export type FilterDrawerPosition = 'left' | 'right' | 'top' | 'bottom';

export interface IFilterDrawerActionEvent {
  action: FilterDrawerActionType; 
  data?: any;
}

export interface IFilterDrawerConfig {
  enabled?: boolean; 
  position?: FilterDrawerPosition; 
  width?: string; 
  headerText?: string; 
  headerIcon?: string;
}

/**
 * Additional type definitions
 */
export interface IApiError {
  status?: number; 
  message?: string; 
  messages?: string[]; 
  error?: any;
}

export type RowsPerPageOption = number;

export interface IColumnSelectionEvent {
  columns: IDataGridFields[];
}

/**
 * Main configuration object for BaseDataGrid
 * Contains all settings needed to set up a data grid
 */
export interface IBaseDataGridConfig<TRequest extends IBasedDataGridRequest> {
  // === CORE SETTINGS ===
  /** Default request object with initial values */
  defaultRequest: TRequest;
  /** API endpoint URL for fetching data */
  apiEndpoint: string;
  /** Prefix for error messages (e.g., 'Failed to load teachers') */
  errorPrefix?: string;

  // === URL & REQUEST MAPPING ===
  /** Converts URL parameters to request object */
  mapUrlParamsToRequest?: (params: Params) => TRequest;
  /** Creates a fresh default request object */
  createDefaultRequest?: () => TRequest;

  // === FILTER CONFIGURATION ===
  /** Settings for applied filters display */
  appliedFiltersConfig?: IDataGridAppliedFiltersConfig<TRequest>;
  /** Settings for filter drawer/sidebar */
  filterDrawerConfig?: IFilterDrawerConfig;
  /** Field names object for type safety */
  fieldNames?: any;
}
