<div class="grid">
    <div class="col-12">
        <app-inner-header-card [breadcrumbs]="breadcrumbs" actionButtonLabel="Buy Package"
            actionButtonIcon="pi pi-shopping-cart-add"
            (click)="this.generalService.goToBuyPackage()"
            [canShowActionButton]="this.permissionService.hasPermission(this.authService.userDecodedJWTData$(), ['packages', 'create'])">
            <div breadcrumb-label>
                {{ breadcrumbs[0].label }}
            </div>
        </app-inner-header-card>
    </div>

    <!-- Applied Filters -->
    @if (appliedFiltersCount() > 0) {
    <div class="col-12">
        <div class="surface-card shadow-1">
            <app-applied-filters-tags [filters]="appliedFilters()" [config]="{
                        showClearAll: true,
                        clearAllLabel: 'Clear All Filters',
                        headerText: 'Applied Filters:',
                        headerIcon: 'pi pi-filter'
                    }" (filterRemoved)="onAppliedFilterRemove($event)" (clearAllClicked)="onAppliedFiltersReset()">
            </app-applied-filters-tags>
        </div>
    </div>
    }

    <div class="col-12">
        <div class="surface-card shadow-2">
            <div class="surface-section py-3 lg:px-3">
                <div class="w-full flex flex-column md:align-items-center md:justify-content-between md:flex-row">
                    <div class="w-full flex flex-column md:flex-row md:justify-content-between">
                        <div class="w-full flex align-items-center justify-content-between md:mt-0 gap-2">
                            <p-iconfield>
                                <p-inputicon styleClass="pi pi-search" />
                                <input type="text" pInputText placeholder="Search packages..." [value]="searchQuery()"
                                    (input)="onSearchInputChange($event)" />
                            </p-iconfield>

                            <!-- Filters Button -->
                            <p-button label="Filters" icon="pi pi-filter" [outlined]="true"
                                (click)="onFiltersDrawerToggle()" [badge]="appliedFiltersCount().toString()" badgeClass="p-badge-info">
                            </p-button>
                        </div>
                    </div>
                    <div class="font-medium text-lg md:text-3xl text-900">&nbsp;</div>
                </div>
            </div>
        </div>
    </div>

</div>

<div class="">
    <div class="grid">
        @defer (on timer(300ms)) {

        @if(isLoading()) {
        <ng-container *ngTemplateOutlet="loading"></ng-container>
        } @else if (hasError()) {
        <!-- Error State -->
        <div class="col-12">
            <div class="w-full surface-card shadow-2 py-3 flex flex-column align-items-center justify-content-center">
                <i class="pi pi-exclamation-triangle text-6xl text-red-500 mb-3"></i>
                <h3 class="text-xl font-semibold text-900 mb-2">Error Loading Packages</h3>
                <p class="text-600 mb-3">{{ errorMessage() }}</p>
                <p-button label="Try Again" icon="pi pi-refresh" (click)="ngOnInit()" styleClass="p-button-outlined">
                </p-button>
            </div>
        </div>
        } @else {
        @if (totalRecords() === 0) {

        @let hasCreatePackagePermission = this.permissionService.hasPermission(this.authService.getUserClaims(),
        ['packages', 'create']);

        <div class="col-12">
            <div class="w-full surface-card shadow-2 py-3 flex flex-column align-items-center justify-content-center">
                <app-empty-data-image-text [emptyDataText]="'No Packages found.'"
                    [showAction]="hasCreatePackagePermission ? true : false" [showActionButtonText]="'Buy Package'"
                    (onActionSelected)="this.generalService.goToBuyPackage()">
                </app-empty-data-image-text>
            </div>
        </div>
        } @else {
        @for (item of packagesData(); track item.id) {
        <div class="col-12 md:col-6 xxl:col-3">
            <app-package-mini-info-card [item]="item" />
        </div>
        }
        }
        }
        } @placeholder {

        <ng-container *ngTemplateOutlet="loading"></ng-container>

        }

    </div>

    <p-paginator (onPageChange)="onPageChange($event)" [first]="(currentPage() - 1) * pageSize()" [rows]="pageSize()" [totalRecords]="totalRecords()"
        [rowsPerPageOptions]="[15, 30, 50]" [showCurrentPageReport]="true"
        currentPageReportTemplate="Showing {first} to {last} of {totalRecords} packages" />
</div>

<ng-template #loading>
    <div class="flex w-full">
        <div class="col-12 md:col-6 xxl:col-3">
            <lib-skeleton-loader shape="circle" layout="card" width="w-full" height="5rem" [count]="1"
                styleClass="mr-2 w-full sm:w-full" />
        </div>

        <div class="col-12 md:col-6 xxl:col-3">
            <lib-skeleton-loader shape="circle" layout="card" width="w-full" height="5rem" [count]="1"
                styleClass="mr-2 w-full sm:w-full" />
        </div>

        <div class="col-12 md:col-6 xxl:col-3">
            <lib-skeleton-loader shape="circle" layout="card" width="w-full" height="5rem" [count]="1"
                styleClass="mr-2 w-full sm:w-full" />
        </div>

        <div class="col-12 md:col-6 xxl:col-3">
            <lib-skeleton-loader shape="circle" layout="card" width="w-full" height="5rem" [count]="1"
                styleClass="mr-2 w-full sm:w-full" />
        </div>
    </div>
</ng-template>



<!-- Filters Drawer Sidebar -->
<app-filters-drawer-sidebar [visible]="filtersDrawerVisible()" [config]="filtersDrawerConfig"
    [filterContentTemplate]="filterContentTemplate" (visibleChange)="filtersDrawerVisible.set($event)"
    (actionClicked)="onFiltersDrawerAction($event)">
</app-filters-drawer-sidebar>

<ng-template #filterContentTemplate>
    <app-overview-packages-filters #packagesFilters [filterState]="filterState()"
        (filterChanged)="onFilterChange($event)" (filterAction)="onFilterAction($event)">
    </app-overview-packages-filters>
</ng-template>