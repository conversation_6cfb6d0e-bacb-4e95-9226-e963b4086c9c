<!-- ============================================================================ -->
<!-- TEACHER CHANGE REQUESTS LIST FILTERS COMPONENT TEMPLATE -->
<!-- ============================================================================ -->

<div class="teacher-change-requests-filters-container flex flex-column gap-4 p-0">
  

  <!-- Status Filter -->
  <div class="filter-group">
    <label class="filter-label block mb-2 text-sm font-medium text-color">
      Status
    </label>
    <p-dropdown
      [options]="statusOptions()"
      [ngModel]="selectedStatus()"
      (ngModelChange)="onStatusChange($event)"
      placeholder="Select status"
      optionLabel="label"
      optionValue="value"
      [showClear]="true"
      [filter]="false"
      styleClass="w-full"
      [disabled]="false"
    >
 
    </p-dropdown>
    <small class="text-color-secondary mt-1 block">
      Filter by request status (Pending, Approved, Rejected, Cancelled)
    </small>
  </div>

  <!-- Filter Summary -->
  <div class="filter-summary" *ngIf="hasActiveFilters()">
    <div class="bg-primary-50 border-1 border-primary-200 border-round p-3">
      <div class="flex align-items-center gap-2 mb-2">
        <i class="pi pi-filter text-primary"></i>
        <span class="text-sm font-medium text-primary">Active Filters</span>
        <p-tag 
          [value]="getActiveFiltersCount().toString()" 
          severity="info" 
          styleClass="text-xs"
        ></p-tag>
      </div>
      
      <div class="flex flex-wrap gap-2">
        <!-- Status Filter Tag -->
        <p-tag 
          *ngIf="selectedStatus() !== null && selectedStatus() !== undefined"
          [value]="'Status: ' + getStatusLabel(selectedStatus()!)"
          severity="info"
          styleClass="text-xs"
        ></p-tag>
      </div>
    </div>
  </div>

  <!-- No Filters Applied -->
  <div class="no-filters-message" *ngIf="!hasActiveFilters()">
    <div class="bg-surface-50 border-1 border-surface-200 border-round p-3 text-center">
      <i class="pi pi-filter-slash text-2xl text-color-secondary mb-2 block"></i>
      <p class="m-0 text-sm text-color-secondary">
        No filters applied. All teacher change requests will be shown.
      </p>
    </div>
  </div>

  <!-- Filter Actions Info -->
 

</div>
