<!-- Alternative Solution 2: Dropdown Language Display -->
<div class="language-proficiency-section-dropdown">
  @if (student.languageLevels.length === 0) {
    <div class="professional-language-pill no-languages">
      <span class="language-name">No languages</span>
      <span class="level-indicator">N/A</span>
    </div>
  } @else if (student.languageLevels.length === 1) {
    <!-- Single language - show full pill -->
    <div class="professional-language-pill" [ngClass]="getLanguageBadgeClass(student.languageLevels[0])">
      <span class="language-name">{{ student.languageLevels[0].languageName }}</span>
      <span class="level-indicator">{{ student.languageLevels[0].levelDisplay }}</span>
    </div>
  } @else {
    <!-- Multiple languages - dropdown approach -->
    <div class="language-dropdown-container">
      <button type="button" 
              class="language-dropdown-trigger"
              [class.expanded]="isLanguageDropdownOpen(student.userId)"
              (click)="toggleLanguageDropdown(student.userId)">
        <div class="primary-language">
          <span class="language-count">{{ student.languageLevels.length }}</span>
          <span class="languages-text">languages</span>
        </div>
        <i class="pi pi-chevron-down dropdown-icon" 
           [class.rotated]="isLanguageDropdownOpen(student.userId)"></i>
      </button>
      
      @if (isLanguageDropdownOpen(student.userId)) {
        <div class="language-dropdown-content">
          @for (languageLevel of student.languageLevels; track languageLevel.teachingLanguageId + languageLevel.level) {
            <div class="dropdown-language-item" [ngClass]="getLanguageBadgeClass(languageLevel)">
              <span class="language-name">{{ languageLevel.languageName }}</span>
              <span class="level-indicator">{{ languageLevel.levelDisplay }}</span>
            </div>
          }
        </div>
      }
    </div>
  }
</div>

<!-- Alternative Solution 3: Stacked/Layered Language Display -->
<div class="language-proficiency-section-stacked">
  @if (student.languageLevels.length === 0) {
    <div class="professional-language-pill no-languages">
      <span class="language-name">No languages</span>
      <span class="level-indicator">N/A</span>
    </div>
  } @else if (student.languageLevels.length <= 2) {
    <!-- Show up to 2 languages normally -->
    @for (languageLevel of student.languageLevels; track languageLevel.teachingLanguageId + languageLevel.level) {
      <div class="professional-language-pill compact" [ngClass]="getLanguageBadgeClass(languageLevel)">
        <span class="language-name">{{ languageLevel.languageName }}</span>
        <span class="level-indicator">{{ languageLevel.levelDisplay }}</span>
      </div>
    }
  } @else {
    <!-- Stacked display for 3+ languages -->
    <div class="stacked-languages-container">
      <div class="language-stack">
        @for (languageLevel of student.languageLevels.slice(0, 2); track languageLevel.teachingLanguageId + languageLevel.level; let i = $index) {
          <div class="stacked-language-pill" 
               [ngClass]="getLanguageBadgeClass(languageLevel)"
               [style.z-index]="10 - i"
               [style.transform]="'translateX(' + (i * 8) + 'px)'">
            <span class="language-name">{{ languageLevel.languageName }}</span>
            <span class="level-indicator">{{ languageLevel.levelDisplay }}</span>
          </div>
        }
        
        @if (student.languageLevels.length > 2) {
          <div class="more-languages-indicator"
               [style.transform]="'translateX(' + (2 * 8) + 'px)'"
               [pTooltip]="getAdditionalLanguagesTooltip(student)"
               tooltipPosition="top">
            <span class="more-count">+{{ student.languageLevels.length - 2 }}</span>
          </div>
        }
      </div>
    </div>
  }
</div>

<!-- Alternative Solution 4: Horizontal Scrollable Display -->
<div class="language-proficiency-section-scrollable">
  @if (student.languageLevels.length === 0) {
    <div class="professional-language-pill no-languages">
      <span class="language-name">No languages</span>
      <span class="level-indicator">N/A</span>
    </div>
  } @else {
    <div class="scrollable-languages-container">
      <div class="languages-scroll-area">
        @for (languageLevel of student.languageLevels; track languageLevel.teachingLanguageId + languageLevel.level) {
          <div class="professional-language-pill mini" [ngClass]="getLanguageBadgeClass(languageLevel)">
            <span class="language-name">{{ languageLevel.languageName }}</span>
            <span class="level-indicator">{{ languageLevel.levelDisplay }}</span>
          </div>
        }
      </div>
      
      @if (student.languageLevels.length > 3) {
        <div class="scroll-indicator">
          <i class="pi pi-angle-right"></i>
        </div>
      }
    </div>
  }
</div>

<!-- Alternative Solution 5: Unified Language Badge -->
<div class="language-proficiency-section-unified">
  @if (student.languageLevels.length === 0) {
    <div class="unified-language-badge no-languages">
      <i class="pi pi-globe"></i>
      <span class="badge-text">No languages</span>
    </div>
  } @else if (student.languageLevels.length === 1) {
    <div class="unified-language-badge single" [ngClass]="getLanguageBadgeClass(student.languageLevels[0])">
      <i class="pi pi-globe"></i>
      <span class="badge-text">{{ student.languageLevels[0].languageName }} ({{ student.languageLevels[0].levelDisplay }})</span>
    </div>
  } @else {
    <div class="unified-language-badge multiple"
         [pTooltip]="getAllLanguagesTooltip(student)"
         tooltipPosition="top"
         [tooltipStyleClass]="'language-tooltip'">
      <i class="pi pi-globe"></i>
      <span class="badge-text">{{ student.languageLevels.length }} languages</span>
      <div class="level-indicators">
        @for (languageLevel of student.languageLevels.slice(0, 3); track languageLevel.teachingLanguageId + languageLevel.level) {
          <span class="mini-level-dot" [ngClass]="'dot-' + getLanguageBadgeClass(languageLevel)"></span>
        }
        @if (student.languageLevels.length > 3) {
          <span class="more-dots">...</span>
        }
      </div>
    </div>
  }
</div>
