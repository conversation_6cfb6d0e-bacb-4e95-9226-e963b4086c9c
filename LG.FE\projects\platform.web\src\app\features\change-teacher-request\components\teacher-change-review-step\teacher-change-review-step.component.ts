import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, signal, computed, OnInit, Input, Output, EventEmitter } from '@angular/core';

// PrimeNG Imports
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { DividerModule } from 'primeng/divider';
import { SkeletonModule } from 'primeng/skeleton';
import { TagModule } from 'primeng/tag';
import { AccordionModule } from 'primeng/accordion';


// Shared Library
import {
  ITeacherToChangeWithAffectedResourcesDto,
  ICalendarLessonDto,
  IAffectedStudentPackageDto,
  IBasicProfileInfoDto,
  IPackageStatusEnum,
  ILessonStatusEnum,
  IPackageTypeEnum,
  ICreateTeacherChangeRequestRequest,
  PrimeProfilePhotoSingleComponent,
  IGetTeachersToChangeRequest,
  IStudentPackageDto
} from 'SharedModules.Library';

// Local Interfaces
import { ICompleteTeacherChangeStepData } from '../complete-teacher-change-step/complete-teacher-change-step.component';
import { IStudentGroupSelectionStepData } from '../student-group-selection-step/student-group-selection-step.component';
import { TeacherChangeType } from '../../interfaces/change-teacher-request.interfaces';

// Services
import { CalendarUtilsService } from '../../../dashboard/weekly-availability-calendar/calendar-utils.service';
import { TeacherChangeRequestService } from '../../../../shared/services/teacher-change-request.service';

export interface ITeacherChangeReviewData {
  request: ICreateTeacherChangeRequestRequest;
  affectedResources: IAffectedStudentPackageDto[];
  changeType: TeacherChangeType;
  studentGroupData?: IStudentGroupSelectionStepData;
}

@Component({
  selector: 'app-teacher-change-review-step',
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,
    CardModule,
    DividerModule,
    SkeletonModule,
    TagModule,
    AccordionModule,
    PrimeProfilePhotoSingleComponent
  ],
  templateUrl: './teacher-change-review-step.component.html',
  styleUrl: './teacher-change-review-step.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TeacherChangeReviewStepComponent implements OnInit {
  // Injected services
  private readonly calendarUtilsService = inject(CalendarUtilsService);
  private readonly teacherChangeRequestService = inject(TeacherChangeRequestService);

  // Inputs
  @Input() teacherChangeData?: ICompleteTeacherChangeStepData;
  @Input() changeType: TeacherChangeType = TeacherChangeType.COMPLETE_CHANGE;
  @Input() studentGroupData?: IStudentGroupSelectionStepData;

  // Enums for template
  readonly TeacherChangeType = TeacherChangeType;

  // Outputs
  @Output() confirmRequest = new EventEmitter<ITeacherChangeReviewData>();

  // Component state
  isLoading = signal(false);
  reviewData = signal<ITeacherChangeReviewData | null>(null);
  affectedLessons = signal<ICalendarLessonDto[]>([]);
  affectedStudents = signal<IBasicProfileInfoDto[]>([]);

  // Computed properties for template
  selectedTeacher = computed(() => this.teacherChangeData?.teacher);
  selectedTeacherId = computed(() => this.teacherChangeData?.teacherId);
  reason = computed(() => this.teacherChangeData?.reason);

  // Computed properties
  totalAffectedLessons = computed(() => this.affectedLessons().length);
  totalAffectedStudents = computed(() => this.affectedStudents().length);



  ngOnInit(): void {
    this.loadReviewData();
  }

  private loadReviewData(): void {
    if (!this.teacherChangeData?.teacherId || !this.teacherChangeData?.teacher) {
      console.error('Missing teacher change data');
      return;
    }

    this.isLoading.set(true);

    // Determine parameters based on change type
    const loadParams: IGetTeachersToChangeRequest = {} as IGetTeachersToChangeRequest;

    if (this.changeType === TeacherChangeType.STUDENT_GROUP_CHANGE && this.studentGroupData) {
      loadParams.teachingLanguageId = this.studentGroupData.teachingLanguageId;
      loadParams.studentId = this.studentGroupData.studentId;
      loadParams.groupId = this.studentGroupData.groupId;

      console.log('Loading teachers for STUDENT_GROUP_CHANGE with params:', loadParams);
    } else {
      console.log('Loading teachers for COMPLETE_CHANGE with default params');
    }

    // Use the service method to load available teachers
    this.teacherChangeRequestService.loadAvailableTeachers(loadParams).subscribe({
      next: (teachersData) => {
        this.processReviewDataFromService(teachersData);
        this.isLoading.set(false);
      },
      error: (error) => {
        console.error('Error loading review data:', error);
        this.isLoading.set(false);
      }
    });
  }

  private processReviewDataFromService(teachersData: ITeacherToChangeWithAffectedResourcesDto[]): void {
    const selectedTeacherData = teachersData
      .find(t => t.teacherToChangeId === this.teacherChangeData?.teacherId);

    if (!selectedTeacherData || !this.teacherChangeData) {
      console.error('Selected teacher data not found');
      return;
    }

    // Extract all affected lessons
    const allLessons = selectedTeacherData.affectedStudentPackages
      .flatMap(pkg => pkg.affectedLessons);

    this.affectedLessons.set(allLessons);

    // Extract unique affected students from packages
    const uniqueStudents = new Map<string, IBasicProfileInfoDto>();
    selectedTeacherData.affectedStudentPackages.forEach(pkg => {
      const packageWithStudents = pkg as IStudentPackageDto;
      if (packageWithStudents.students && Array.isArray(packageWithStudents.students)) {
        packageWithStudents.students.forEach((student: IBasicProfileInfoDto) => {
          uniqueStudents.set(student.userId, student);
        });
      }
    });
    this.affectedStudents.set(Array.from(uniqueStudents.values()));

    // Build the API request object
    const baseRequest = this.teacherChangeRequestService.createBaseRequest();
    const apiRequest: ICreateTeacherChangeRequestRequest = {
      parentId: baseRequest.parentId!,
      source: baseRequest.source!,
      teacherId: this.teacherChangeData.teacherId!,
      reason: this.teacherChangeData.reason || ''
    };

    // Add student/group specific data if applicable
    if (this.changeType === TeacherChangeType.STUDENT_GROUP_CHANGE && this.studentGroupData) {
      apiRequest.studentId = this.studentGroupData.studentId;
      apiRequest.groupId = this.studentGroupData.groupId;
      apiRequest.teachingLanguageId = this.studentGroupData.teachingLanguageId;
    }

    // Set review data
    const reviewData: ITeacherChangeReviewData = {
      request: apiRequest,
      affectedResources: selectedTeacherData.affectedStudentPackages,
      changeType: this.changeType,
      studentGroupData: this.studentGroupData
    };
    this.reviewData.set(reviewData);
  }



  // Method to get current review data for parent component
  getCurrentReviewData(): ITeacherChangeReviewData | null {
    return this.reviewData();
  }

  // Method to confirm the review (called by parent component)
  confirmReview(): void {
    const data = this.reviewData();
    if (data) {
      this.confirmRequest.emit(data);
    }
  }



  getLessonTypeLabel(lessonType: IPackageTypeEnum): string {
    // Use the centralized calendar utils service for consistent package type labels
    return this.calendarUtilsService.getPackageTypeLabel(lessonType);
  }

  getLessonStatusLabel(status: ILessonStatusEnum): string {
    // Use the centralized calendar utils service for consistent status labels
    return this.calendarUtilsService.getStatusDisplayLabel(status);
  }

  getLessonStatusSeverity(status: ILessonStatusEnum): 'success' | 'info' | 'warn' | 'danger' {
    // Map lesson status enum to PrimeNG tag severity using contract interface
    switch (status) {
      case ILessonStatusEnum.Completed:
        return 'success';
      case ILessonStatusEnum.Scheduled:
        return 'info';
      case ILessonStatusEnum.AwaitingEvaluation:
        return 'info';
      case ILessonStatusEnum.PendingConfirmationByTeacher:
        return 'warn';
      case ILessonStatusEnum.StudentNoShow:
      case ILessonStatusEnum.TeacherNoShow:
      case ILessonStatusEnum.CancelledByParent:
      case ILessonStatusEnum.CancelledByTeacher:
      case ILessonStatusEnum.CancelledByAdmin:
        return 'danger';
      default:
        return 'info';
    }
  }

  getPackageStatusLabel(status: IPackageStatusEnum): string {
    // Map package status enum to display labels
    switch (status) {
      case IPackageStatusEnum.Active:
        return 'Active';
      case IPackageStatusEnum.InActive:
        return 'Inactive';
      case IPackageStatusEnum.Refunded:
        return 'Refunded';
      case IPackageStatusEnum.Expired:
        return 'Expired';
      case IPackageStatusEnum.Completed:
        return 'Completed';
      default:
        return 'Unknown';
    }
  }

  getPackageStatusSeverity(status: IPackageStatusEnum): 'success' | 'info' | 'warn' | 'danger' {
    // Map package status enum to PrimeNG tag severity
    switch (status) {
      case IPackageStatusEnum.Active:
        return 'success';
      case IPackageStatusEnum.InActive:
        return 'warn';
      case IPackageStatusEnum.Refunded:
        return 'info';
      case IPackageStatusEnum.Expired:
        return 'danger';
      case IPackageStatusEnum.Completed:
        return 'info';
      default:
        return 'info';
    }
  }

  // Helper methods to safely access package properties
  getPackageLanguageName(packageItem: IAffectedStudentPackageDto): string {
    return packageItem.teachingLanguageName || 'Language Package';
  }

  getPackageRemainingLessons(packageItem: IAffectedStudentPackageDto): number {
    return packageItem.remainingLessons || 0;
  }

  getPackageTotalLessons(packageItem: IAffectedStudentPackageDto): number {
    return packageItem.totalLessons || 0;
  }

  getPackageDuration(packageItem: IAffectedStudentPackageDto): number {
    return packageItem.durationInMinutes || 0;
  }

  getPackageStudentsCount(packageItem: IAffectedStudentPackageDto): number {
    return packageItem.students?.length || 0;
  }

  getPackageStatus(packageItem: IAffectedStudentPackageDto): IPackageStatusEnum {
    return (packageItem).packageStatus || IPackageStatusEnum.Active;
  }

  // Helper methods for package date properties
  getPackageOriginalExpirationDate(packageItem: IAffectedStudentPackageDto): string | null {
    if (!packageItem.originalExpirationDateUtc) return null;
    if (typeof packageItem.originalExpirationDateUtc === 'string') return packageItem.originalExpirationDateUtc;
    if (packageItem.originalExpirationDateUtc instanceof Date) return packageItem.originalExpirationDateUtc.toISOString();
    return null;
  }

  getPackageCourtesyExpirationDate(packageItem: IAffectedStudentPackageDto): string | null {
    if (!packageItem.courtesyExpirationDateUtc) return null;
    if (typeof packageItem.courtesyExpirationDateUtc === 'string') return packageItem.courtesyExpirationDateUtc;
    if (packageItem.courtesyExpirationDateUtc instanceof Date) return packageItem.courtesyExpirationDateUtc.toISOString();
    return null;
  }

  getPackageExpiresOnDate(packageItem: IAffectedStudentPackageDto): string | null {
    if (!packageItem.expiresOnDateUtc) return null;
    if (typeof packageItem.expiresOnDateUtc === 'string') return packageItem.expiresOnDateUtc;
    if (packageItem.expiresOnDateUtc instanceof Date) return packageItem.expiresOnDateUtc.toISOString();
    return null;
  }

  getPackageDateCreated(packageItem: IAffectedStudentPackageDto): string | null {
    if (!packageItem.dateCreatedUtc) return null;
    if (typeof packageItem.dateCreatedUtc === 'string') return packageItem.dateCreatedUtc;
    if (packageItem.dateCreatedUtc instanceof Date) return packageItem.dateCreatedUtc.toISOString();
    return null;
  }

  getPackageHasBeenExtended(packageItem: IAffectedStudentPackageDto): boolean {
    return packageItem.hasBeenExtended || false;
  }

  // Helper method to format dates for display
  formatPackageDate(dateString: string | null): string {
    if (!dateString) return 'N/A';

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return 'Invalid Date';
    }
  }
}
