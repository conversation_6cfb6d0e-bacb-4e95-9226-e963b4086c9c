import {CommonModule} from '@angular/common';
import {ChangeDetectionStrategy, Component, EventEmitter, input, Output, type OnInit} from '@angular/core';
import { ButtonModule } from 'primeng/button';

@Component({
    selector: 'app-student-group-selection-suggestion-text-step',
    imports: [
        CommonModule,
        ButtonModule,
    ],
    templateUrl: './student-group-selection-suggestion-text-step.component.html',
    styleUrl: './student-group-selection-suggestion-text-step.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class StudentGroupSelectionSuggestionTextStepComponent implements OnInit {

  @Output() buttonClicked: EventEmitter<any> = new EventEmitter();
  isDialogPopup = input(false);

  ngOnInit(): void {
  }

  onItemClicked(): void {
    this.buttonClicked.emit();
  }
}
