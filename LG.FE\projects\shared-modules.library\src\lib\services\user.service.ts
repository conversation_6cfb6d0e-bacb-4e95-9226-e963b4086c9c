import { HttpClient, HttpErrorResponse, HttpHeaders, HttpParams, HttpRequest, HttpResponse } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable, catchError, filter, map, retry, throwError } from 'rxjs';

import { IApiResponse, IApiResponseBase, ICreateStudentGroupResponse, IdentityRoutes, IGetAllTeachingLanguagesResponse, IGetStudentsResponse, IHttpStatusCode, ILoginRequest, ILoginRequiresOtpRequest, ILoginRequiresOtpResponse, ILoginResponse, ImpersonateStudentRequest, IRefreshTokenRequest, IRefreshTokenResponse, IRegisterParentRequest, IRegisterParentResponse, IRegisterStudentRequest, IRequestOtpRequest, IRequestOtpResponse, ISearchStudentDto, IStudentDirectRegistrationRequest, IStudentDirectRegistrationResponse, ITeachingLanguageDto, StudentGroupRoutes } from '../GeneratedTsFiles';
import { ToastService } from './toast.service';
import { State } from './data-api-state.service';
import { environment } from '../environments/environment';
const USER_KEY = 'auth-user';
const BACKEND_LMS_URL = environment.apiUrl + "";

const ORIGIN = 'http://localhost:4200';
@Injectable({
  providedIn: 'root'
})
export class UserService {

  loginUrl = BACKEND_LMS_URL + IdentityRoutes.postLogin;
  registerParentUrl = BACKEND_LMS_URL + IdentityRoutes.postRegisterParent;
  requestOtpUrl = BACKEND_LMS_URL + IdentityRoutes.postRequestOtp;
  constructor(private http: HttpClient, private toastService: ToastService) {

  }

  login(data: ILoginRequest): Observable<ILoginResponse> {
    return customHandleApiResponse<ILoginResponse>(this.http, IdentityRoutes.postLogin, 'POST', data);
  }

  registerParent(data: IRegisterParentRequest): Observable<IRegisterParentResponse> {
    return customHandleApiResponse<IRegisterParentResponse>(this.http, IdentityRoutes.postRegisterParent, 'POST', data);
  }

  registerStudent(data: any): Observable<IRegisterStudentRequest> {
    return customHandleApiResponse<IRegisterStudentRequest>(this.http, IdentityRoutes.postRegisterStudent, 'POST', data);
  }

  studentRequestTrial(data: IStudentDirectRegistrationRequest): Observable<IStudentDirectRegistrationResponse> {
    return customHandleApiResponse<IStudentDirectRegistrationResponse>(this.http, IdentityRoutes.postStudentRequestTrial, 'POST', data);
  }

  requestOtp(data: IRequestOtpRequest): Observable<IRequestOtpResponse> {
    return customHandleApiResponse<IRequestOtpResponse>(this.http, IdentityRoutes.postRequestOtp, 'POST', data);
  }

  refreshToken(data: IRefreshTokenRequest): Observable<IRefreshTokenResponse> {
    return customHandleApiResponse<IRefreshTokenResponse>(this.http, IdentityRoutes.postRefreshToken, 'POST', data);
  }

  loginRequiredOTP(data: ILoginRequiresOtpRequest): Observable<ILoginRequiresOtpResponse> {
    return customHandleApiResponse<ILoginRequiresOtpResponse>(this.http, IdentityRoutes.postLoginRequiresOtp, 'POST', data);
  }

  createStudentGroup(data: any): Observable<ICreateStudentGroupResponse> {
    return customHandleApiResponse<ICreateStudentGroupResponse>(this.http, StudentGroupRoutes.postCreateStudentGroup, 'POST', data);
  }

  impersonateStudent(data: ImpersonateStudentRequest): Observable<ImpersonateStudentRequest> {
    return customHandleApiResponse<ImpersonateStudentRequest>(this.http, IdentityRoutes.postImpersonateStudent, 'POST', data);
  }

  handleError(error: HttpErrorResponse) {
    let errorMessage = 'An error occurred';
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
    }
    console.error(errorMessage);
    return throwError(errorMessage);
  }


  findStudentById(id: string, students: State<IGetStudentsResponse>): ISearchStudentDto {
    console.log(students);
    const studentsData: ISearchStudentDto[] = students.data.pageData;
    return (
      studentsData.find(
        (student: ISearchStudentDto) => student.userId === id
      ) || {} as ISearchStudentDto
    );
  }

  findTeachingLanguagesForStudent(student: ISearchStudentDto, teachingLanguages: State<IGetAllTeachingLanguagesResponse>): ITeachingLanguageDto[] {
    const allLanguages = teachingLanguages.data?.teachingLanguages || [];

    if (!student || !allLanguages.length) return [];

    const studentLanguageIds = (student.studentTeachingLanguageDto || []).map(
      (lang: any) => lang.teachingLanguageId
    );

    return allLanguages.filter(
      (lang: ITeachingLanguageDto) => studentLanguageIds.includes(lang.id as string)
    );
  }
}


// Define a generic function to handle API responses
export function customHandleApiResponse<T>(http: HttpClient, url: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH', data?: any,
    headers?: HttpHeaders, params?: { [param: string]: string | string[] | T }): Observable<T> {
    const endpointUrl = environment.apiUrl;
    headers = headers ? headers.append('Client-Domain', ORIGIN) : new HttpHeaders({ 'Client-Domain': ORIGIN });

    // Initialize HttpParams if provided
    let httpParams = new HttpParams();
    if (params) {
        for (const key in params) {
            if (params.hasOwnProperty(key)) {
                httpParams = httpParams.append(key, params[key] as string);
            }
        }
    }
    console.log(params);
    const request = new HttpRequest<IApiResponse<T>>(
        method,
        `${endpointUrl}/${url}`,
        data,
        {
            headers: headers,
            params: httpParams,
            responseType: 'json'
        }
    );

    // Send the request and handle the response
    return http.request<IApiResponse<T>>(request).pipe(
        filter((event): event is HttpResponse<IApiResponse<T>> => event instanceof HttpResponse), // Ensure we only handle HttpResponse events
        map((response: HttpResponse<IApiResponse<T>>) => {
            if (isSuccessStatusCode(response.body?.statusCode!)) {
                return response.body?.data as T;
            }
            throw new Error('Request failed with status code: ' + response.body?.statusCode);
        }),
        catchError((errorResponse: HttpErrorResponse) => {
            // Handle error responses here
            console.error('API Error:', errorResponse);
            return throwError(() => handleErrorResponse(errorResponse.error)); // Use a function that returns the error
        }),
    );
}

export function handleErrorResponse(error: IApiResponseBase) {
    const apiResponse: IApiResponseBase | null = error as IApiResponseBase;
    if (error) {

        if (apiResponse) {
            // alert(apiResponse);
            // return apiResponse;
                switch (apiResponse.statusCode) {
                    case IHttpStatusCode.Unauthorized:
                        // Unauthorized error handling
                        console.error('Unauthorized:', apiResponse.messages);
                        break;
                    case IHttpStatusCode.NotFound:
                        // Not found error handling
                        console.error('Not Found:', apiResponse.messages);
                        break;
                    // Add more cases as needed for other status codes
                    default:
                        // Default error handling
                        console.error('An error occurred:', apiResponse.messages);
                        break;
                }

                if (apiResponse.formValidationErrors) {
                    console.error('Form Validation Errors:', apiResponse.formValidationErrors);
                }
            } else {
                // Default error handling if response doesn't conform to IApiResponseBase
                console.error('An error occurred:', error);

                return {
                    statusCode: IHttpStatusCode.InternalServerError,
                    messages: ['An error occurred. Please try again later.'],
                };
            }
    }

    return apiResponse;
}

export function extractData<T>(response: IApiResponse<T>): T {
    return response.data!;
}


export function isSuccessStatusCode(statusCode: IHttpStatusCode): boolean {
    return statusCode >= IHttpStatusCode.OK && statusCode <= IHttpStatusCode.IMUsed;
}
