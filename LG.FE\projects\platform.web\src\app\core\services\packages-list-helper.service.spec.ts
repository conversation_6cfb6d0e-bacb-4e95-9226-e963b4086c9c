import { TestBed } from '@angular/core/testing';
import { PackagesListHelperService } from './packages-list-helper.service';
import { IGetPackagesRequest } from 'SharedModules.Library';

describe('PackagesListHelperService', () => {
  let service: PackagesListHelperService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(PackagesListHelperService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('cleanRequestForApi', () => {
    it('should remove null values from request', () => {
      const request: IGetPackagesRequest = {
        pageNumber: 1,
        pageSize: 15,
        sortColumn: 'dateCreatedUtc',
        sortDirection: 'desc',
        searchTerm: null,
        packageType: null,
        packageStatus: null,
        purchasedFrom: null,
        purchasedTo: null,
        expiresFrom: null,
        expiresTo: null,
        teacherId: null,
        parentId: null,
        studentId: null,
        groupId: null,
        teachingLanguageId: 'en',
        hasAddOnExtension: true,
        parentAccountDeleted: null,
      };

      const cleanedRequest = service.cleanRequestForApi(request);

      // Should include required properties
      expect(cleanedRequest.pageNumber).toBe(1);
      expect(cleanedRequest.pageSize).toBe(15);
      expect(cleanedRequest.sortColumn).toBe('dateCreatedUtc');
      expect(cleanedRequest.sortDirection).toBe('desc');

      // Should include non-null optional properties
      expect(cleanedRequest.teachingLanguageId).toBe('en');
      expect(cleanedRequest.hasAddOnExtension).toBe(true);

      // Should NOT include null properties
      expect(cleanedRequest.searchTerm).toBeUndefined();
      expect(cleanedRequest.packageType).toBeUndefined();
      expect(cleanedRequest.packageStatus).toBeUndefined();
      expect(cleanedRequest.purchasedFrom).toBeUndefined();
      expect(cleanedRequest.purchasedTo).toBeUndefined();
      expect(cleanedRequest.expiresFrom).toBeUndefined();
      expect(cleanedRequest.expiresTo).toBeUndefined();
      expect(cleanedRequest.teacherId).toBeUndefined();
      expect(cleanedRequest.parentId).toBeUndefined();
      expect(cleanedRequest.studentId).toBeUndefined();
      expect(cleanedRequest.groupId).toBeUndefined();
      expect(cleanedRequest.parentAccountDeleted).toBeUndefined();
    });

    it('should format dates to ISO strings', () => {
      const testDate = new Date('2024-01-15T10:30:00Z');
      const request: IGetPackagesRequest = service.createDefaultPackagesRequestForUser();
      request.purchasedFrom = testDate;
      request.expiresTo = testDate;

      const cleanedRequest = service.cleanRequestForApi(request);

      expect(cleanedRequest.purchasedFrom).toBe('2024-01-15T10:30:00.000Z');
      expect(cleanedRequest.expiresTo).toBe('2024-01-15T10:30:00.000Z');
    });

    it('should exclude empty strings', () => {
      const request: IGetPackagesRequest = service.createDefaultPackagesRequestForUser();
      request.searchTerm = '';
      request.teachingLanguageId = '   '; // whitespace only

      const cleanedRequest = service.cleanRequestForApi(request);

      expect(cleanedRequest.searchTerm).toBeUndefined();
      expect(cleanedRequest.teachingLanguageId).toBeUndefined();
    });
  });

  describe('createDefaultPackagesRequestForUser', () => {
    it('should create a valid default request', () => {
      const defaultRequest = service.createDefaultPackagesRequestForUser();

      expect(defaultRequest.pageNumber).toBe(1);
      expect(defaultRequest.pageSize).toBe(15);
      expect(defaultRequest.sortColumn).toBe('dateCreatedUtc'); // This should match nameOf<ISearchPackageDto>().dateCreatedUtc
      expect(defaultRequest.sortDirection).toBe('desc');
      
      // All optional filters should be null by default
      expect(defaultRequest.searchTerm).toBeNull();
      expect(defaultRequest.teachingLanguageId).toBeNull();
      expect(defaultRequest.hasAddOnExtension).toBeNull();
    });
  });
});
