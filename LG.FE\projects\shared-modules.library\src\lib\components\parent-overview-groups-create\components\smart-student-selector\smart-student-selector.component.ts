import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output, signal, computed, OnChanges, SimpleChanges } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';
import { AvatarModule } from 'primeng/avatar';
import { RippleModule } from 'primeng/ripple';
import { TooltipModule } from 'primeng/tooltip';
import { ISearchStudentDto, ILanguageLevelsEnum } from '../../../../GeneratedTsFiles';
import { GeneralService } from '../../../../services/general.service';

export interface StudentLanguageLevel {
  teachingLanguageId: string;
  languageName: string;
  level: ILanguageLevelsEnum;
  levelDisplay: string;
}

export interface StudentCardData extends ISearchStudentDto {
  languageLevels: StudentLanguageLevel[];
  isSelectable: boolean;
  isSelected: boolean;
}

@Component({
  selector: 'app-smart-student-selector',
  imports: [CommonModule, FormsModule, InputTextModule, ButtonModule, AvatarModule, RippleModule, TooltipModule],
  templateUrl: './smart-student-selector.component.html',
  styleUrl: './smart-student-selector.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SmartStudentSelectorComponent implements OnChanges {
  @Input() students: ISearchStudentDto[] = [];
  @Input() selectedStudents: ISearchStudentDto[] = [];
  @Input() loading = false;
  @Input() selectedLanguageName = ''; // Display the selected language
  @Input() computedGroupLanguageLevels: ILanguageLevelsEnum[] = []; // Group language levels
  @Input() computedGroupLevelsText = ''; // Formatted text for group levels
  @Input() isEditMode = false; // Whether component is in edit mode
  @Input() validateStudentSelection?: (student: ISearchStudentDto) => boolean; // Validation callback
  @Output() studentSelected = new EventEmitter<ISearchStudentDto[]>();

  // Internal state
  private readonly generalService = new GeneralService();
  studentCards = signal<StudentCardData[]>([]);
  compatibleLanguageLevels = signal<StudentLanguageLevel[]>([]);

  // Search functionality
  searchQuery = signal<string>('');
  showSearch = computed(() => this.students.length > 1); // Show search when more than 8 students

  // View mode
  compactView = signal<boolean>(true); // Default to compact view for better space utilization

  // Filtered students based on search
  filteredStudentCards = computed(() => {
    const cards = this.studentCards();
    const query = this.searchQuery().toLowerCase().trim();

    if (!query) return cards;

    return cards.filter(card =>
      card.firstName.toLowerCase().includes(query) ||
      card.lastName.toLowerCase().includes(query) ||
      `${card.firstName} ${card.lastName}`.toLowerCase().includes(query)
    );
  });

  // Validation state
  hasMinimumStudents = computed(() => this.selectedStudents.length >= 2);
  validationMessage = computed(() => {
    if (this.selectedStudents.length === 0) {
      return 'Please select students for the group';
    } else if (this.selectedStudents.length === 1) {
      return 'At least 2 students are required to create a group';
    }
    return '';
  });

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['students'] || changes['selectedStudents'] || changes['isEditMode']) {
      this.updateStudentCards();
    }
  }

  private updateStudentCards(): void {
    const cards = this.students.map(student => this.createStudentCard(student));
    this.studentCards.set(cards);
    this.updateCompatibility();
  }

  private createStudentCard(student: ISearchStudentDto): StudentCardData {
    const languageLevels = this.extractLanguageLevels(student);
    return {
      ...student,
      languageLevels,
      isSelectable: true,
      isSelected: this.isStudentSelected(student)
    };
  }

  private extractLanguageLevels(student: ISearchStudentDto): StudentLanguageLevel[] {
    if (!student.studentTeachingLanguageDto) return [];
    
    return student.studentTeachingLanguageDto.map(lang => ({
      teachingLanguageId: lang.teachingLanguageId,
      languageName: lang.teachingLanguageName,
      level: lang.languageLevel,
      levelDisplay: this.getLevelDisplay(lang.languageLevel)
    }));
  }

  getLevelDisplay(level: ILanguageLevelsEnum): string {
    if (level === ILanguageLevelsEnum.None) {
      return 'N/A';
    }
    return this.generalService.getILanguageLevelsEnumText(level, false);
  }

  private isStudentSelected(student: ISearchStudentDto): boolean {
    return this.selectedStudents.some(selected => selected.userId === student.userId);
  }

  private updateCompatibility(): void {
    const selected = this.selectedStudents;

    // In edit mode, since students are pre-filtered by language in parent component,
    // all students should be selectable (they already have the correct teaching language)
    if (this.isEditMode) {
      console.log('🔧 Edit mode: Setting all students as selectable');
      this.compatibleLanguageLevels.set([]);
      this.setAllStudentsSelectable(true);
      return;
    }

    // Create mode compatibility logic (existing behavior)
    if (selected.length === 0) {
      // No students selected - all are compatible
      this.compatibleLanguageLevels.set([]);
      this.setAllStudentsSelectable(true);
      return;
    }

    // Extract compatible language-level combinations from selected students
    const compatibleCombinations = this.getCompatibleLanguageLevels(selected);
    this.compatibleLanguageLevels.set(compatibleCombinations);

    // Update selectability of all students
    this.updateStudentSelectability(compatibleCombinations);
  }

  private getCompatibleLanguageLevels(selectedStudents: ISearchStudentDto[]): StudentLanguageLevel[] {
    if (selectedStudents.length === 0) return [];

    // Get all language-level combinations from selected students
    const allCombinations = new Set<string>();
    selectedStudents.forEach(student => {
      if (student.studentTeachingLanguageDto) {
        student.studentTeachingLanguageDto.forEach(lang => {
          const key = `${lang.teachingLanguageId}-${lang.languageLevel}`;
          allCombinations.add(key);
        });
      }
    });

    // Convert back to StudentLanguageLevel objects
    const combinations: StudentLanguageLevel[] = [];
    selectedStudents.forEach(student => {
      if (student.studentTeachingLanguageDto) {
        student.studentTeachingLanguageDto.forEach(lang => {
          const key = `${lang.teachingLanguageId}-${lang.languageLevel}`;
          if (allCombinations.has(key)) {
            const existing = combinations.find(c => 
              c.teachingLanguageId === lang.teachingLanguageId && c.level === lang.languageLevel
            );
            if (!existing) {
              combinations.push({
                teachingLanguageId: lang.teachingLanguageId,
                languageName: lang.teachingLanguageName,
                level: lang.languageLevel,
                levelDisplay: this.getLevelDisplay(lang.languageLevel)
              });
            }
          }
        });
      }
    });

    return combinations;
  }

  private updateStudentSelectability(compatibleCombinations: StudentLanguageLevel[]): void {
    const updatedCards = this.studentCards().map(card => {
      if (this.isStudentSelected({ userId: card.userId } as ISearchStudentDto)) {
        // Already selected students remain selectable
        return { ...card, isSelectable: true };
      }

      // Check if student has any compatible language-level combination
      const hasCompatibleLanguage = card.languageLevels.some(studentLang =>
        compatibleCombinations.some(compatible =>
          compatible.teachingLanguageId === studentLang.teachingLanguageId &&
          compatible.level === studentLang.level
        )
      );

      return { ...card, isSelectable: hasCompatibleLanguage };
    });

    this.studentCards.set(updatedCards);
  }

  private setAllStudentsSelectable(selectable: boolean): void {
    const updatedCards = this.studentCards().map(card => ({
      ...card,
      isSelectable: selectable
    }));

    console.log('🔧 Setting all students selectable:', {
      selectable,
      totalCards: updatedCards.length,
      selectableCount: updatedCards.filter(c => c.isSelectable).length
    });

    this.studentCards.set(updatedCards);
  }

  onStudentClick(student: StudentCardData): void {
    if (!student.isSelectable) return;

    const currentSelected = [...this.selectedStudents];
    const isCurrentlySelected = this.isStudentSelected(student);

    if (isCurrentlySelected) {
      // Remove student - no validation needed for removal
      const updatedSelected = currentSelected.filter(s => s.userId !== student.userId);
      this.studentSelected.emit(updatedSelected);
    } else {
      // Add student - validate if callback is provided
      if (this.validateStudentSelection && !this.validateStudentSelection(student)) {
        // Validation failed, don't add the student
        return;
      }

      const updatedSelected = [...currentSelected, student];
      this.studentSelected.emit(updatedSelected);
    }
  }

  getStudentInitials(student: StudentCardData): string {
    const firstName = student.firstName?.trim() || '';
    const lastName = student.lastName?.trim() || '';

    if (!firstName && !lastName) return 'ST';

    const firstInitial = firstName.charAt(0).toUpperCase();
    const lastInitial = lastName.charAt(0).toUpperCase();

    return firstInitial + lastInitial;
  }

  // Get student full name for display
  getStudentFullName(student: StudentCardData): string {
    const firstName = student.firstName?.trim() || '';
    const lastName = student.lastName?.trim() || '';
    return `${firstName} ${lastName}`.trim() || 'Unknown Student';
  }

  // Get tooltip text for student
  getStudentTooltip(student: StudentCardData): string {
    const name = this.getStudentFullName(student);
    const languageCount = student.languageLevels.length;
    const languageText = languageCount === 1 ? 'language' : 'languages';
    return `${name} • ${languageCount} ${languageText}`;
  }

  getLanguageBadgeClass(languageLevel: StudentLanguageLevel): string {
    // Return CSS class based on language level
    const levelClasses: { [key in ILanguageLevelsEnum]?: string } = {
      [ILanguageLevelsEnum.A1]: 'level-a1',
      [ILanguageLevelsEnum.A2]: 'level-a2',
      [ILanguageLevelsEnum.B1]: 'level-b1',
      [ILanguageLevelsEnum.B2]: 'level-b2',
      [ILanguageLevelsEnum.C1]: 'level-c1',
      [ILanguageLevelsEnum.C2]: 'level-c2',
      [ILanguageLevelsEnum.None]: 'level-none'
    };

    return levelClasses[languageLevel.level] || 'level-default';
  }

  // Get CSS class for group language level
  getGroupLevelBadgeClass(level: ILanguageLevelsEnum): string {
    const levelClasses: { [key in ILanguageLevelsEnum]?: string } = {
      [ILanguageLevelsEnum.A1]: 'level-a1',
      [ILanguageLevelsEnum.A2]: 'level-a2',
      [ILanguageLevelsEnum.B1]: 'level-b1',
      [ILanguageLevelsEnum.B2]: 'level-b2',
      [ILanguageLevelsEnum.C1]: 'level-c1',
      [ILanguageLevelsEnum.C2]: 'level-c2',
      [ILanguageLevelsEnum.None]: 'level-none'
    };

    return levelClasses[level] || 'level-default';
  }

  // Get display text for group language level
  getGroupLevelDisplayText(level: ILanguageLevelsEnum): string {
    return this.getLevelDisplay(level);
  }

  // Search functionality methods
  onSearchChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.searchQuery.set(target.value);
  }

  clearSearch(): void {
    this.searchQuery.set('');
  }

  toggleView(): void {
    this.compactView.set(!this.compactView());
  }

  // Get tooltip text for additional languages
  getAdditionalLanguagesTooltip(student: StudentCardData): string {
    if (student.languageLevels.length <= 1) return '';

    const additionalLanguages = student.languageLevels.slice(1);
    const languageTexts = additionalLanguages.map(lang =>
      `${lang.languageName} (${lang.levelDisplay})`
    );

    return `Additional languages:\n${languageTexts.join('\n')}`;
  }

  // Get tooltip text for all languages (unified badge)
  getAllLanguagesTooltip(student: StudentCardData): string {
    if (student.languageLevels.length === 0) return 'No languages available';

    const languageTexts = student.languageLevels.map(lang =>
      `${lang.languageName} (${lang.levelDisplay})`
    );

    return `Languages:\n${languageTexts.join('\n')}`;
  }

  // Language dropdown state management (for dropdown solution)
  private openLanguageDropdowns = new Set<string>();

  isLanguageDropdownOpen(studentId: string): boolean {
    return this.openLanguageDropdowns.has(studentId);
  }

  toggleLanguageDropdown(studentId: string): void {
    if (this.openLanguageDropdowns.has(studentId)) {
      this.openLanguageDropdowns.delete(studentId);
    } else {
      // Close all other dropdowns first
      this.openLanguageDropdowns.clear();
      this.openLanguageDropdowns.add(studentId);
    }
  }

  // Get tooltip text for remaining languages (after showing first 3)
  getRemainingLanguagesTooltip(student: StudentCardData): string {
    if (student.languageLevels.length <= 3) return '';

    const remainingLanguages = student.languageLevels.slice(3);
    const languageTexts = remainingLanguages.map(lang =>
      `${lang.languageName} (${lang.levelDisplay})`
    );

    return `Additional languages:\n${languageTexts.join('\n')}`;
  }

}
