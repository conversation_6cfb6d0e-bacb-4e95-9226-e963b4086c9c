// Angular imports
import { CommonModule, ViewportScroller } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, EventEmitter, inject, input, output, Output, signal } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormControl, FormGroup, ReactiveFormsModule, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';

// Third-party imports
import moment from 'moment';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { TextareaModule } from 'primeng/textarea';
import { FileUploadModule } from 'primeng/fileupload';
import { DropdownModule } from 'primeng/dropdown';
import { RadioButtonModule } from 'primeng/radiobutton';
import { DatePickerModule } from 'primeng/datepicker';
import { ConfirmationService } from 'primeng/api';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { BadgeModule } from 'primeng/badge';
import { InputSwitchModule } from 'primeng/inputswitch';
import { SelectModule } from 'primeng/select';
import { AccordionModule } from 'primeng/accordion';
import { TooltipModule } from 'primeng/tooltip';

// Local components
import { RequestOtpConfirmDialogComponent } from '../../dialogs/request-otp-confirm-dialog/request-otp-confirm-dialog.component';
import { PrimeReactiveFormInputComponent } from '../../prime/prime-reactive-form-input/prime-reactive-form-input.component';
import { PrimeTimezoneDropdownComponent } from '../../prime/prime-timezone-dropdown/prime-timezone-dropdown.component';
import { PrimeCountriesDropdownComponent } from '../../prime/prime-countries-dropdown/prime-countries-dropdown.component';
import { CountryPhoneInputComponent } from '../../prime/country-phone-input/country-phone-input.component';
import { FormFieldValidationMessageComponent } from '../../prime/form-field-validation-message/form-field-validation-message.component';
import { PrimeDateOfBirthPickerComponent } from '../../prime/prime-date-of-birth-picker/prime-date-of-birth-picker.component';
import { PrimeReactiveFormArrayInputComponent } from '../../prime/prime-reactive-form-array-input/prime-reactive-form-array-input.component';
import { LoaderDirective } from '../../../directives/loader.directive';
import { LanguageLevelSelectorComponent } from '../../language-level-selector/language-level-selector.component';

// Services
import { AuthStateService } from '../../../services/auth-state.service';
import { GeneralService } from '../../../services/general.service';
import { DataApiStateService, State } from '../../../services/data-api-state.service';
import { EventBusService, EmitEvent, Events } from '../../../services/event-bus.service';
import { HandleApiResponseService } from '../../../services/handle-api-response.service';
import { ToastService } from '../../../services/toast.service';
import { PopupStateService } from '../../../services/popup-state.service';
import { ParentService } from '../../../services/parent.service';
import { FormErrorScrollerService } from '../../../services/form-error-scroller.service';
import { ApiLoadingStateService } from '../../../services/api-loading-state.service';

// Helpers
import { untilDestroyed } from '../../../helpers/until-destroyed';
import { CustomValidators } from '../../../helpers/custom-validators';
import { getToastMessage, ToastMessages } from '../../../models/toast-messages';

// Models, DTOs, Enums, Types
import {
    IBasicProfileInfoDto,
    IGetProfileInfoResponse,
    IMobileNumberDto,
    IDialCodeDataDto,
    IAddressDto,
    IEmailAddressDto,
    ISpeakingLanguageDto,
    IPatchProfileInfoRequest,
    IRegTeacherPersonalInformationDto,
    IGenderEnum,
    ILanguageLevelsEnum,
    IGetTeacherWorkProfileResponse,
    IPatchProfileInfoResponse,
    IProfileInfo
} from '../../../GeneratedTsFiles';
import { IUserRole, nameOf, FormControlsFor, ProfileFieldConfig, RoleProfileConfig } from '../../../models/general.model';
import { Severity } from '../../../models/severity';

// Use the mapped type to create form controls for ISpeakingLanguageDto
type SpeakingLanguageFormControls = FormControlsFor<ISpeakingLanguageDto>;

// Now your form group is strongly typed to the DTO
type SpeakingLanguageFormGroup = FormGroup<SpeakingLanguageFormControls>;

const IBasicProfileInfoDtoParamsMap = nameOf<IBasicProfileInfoDto>();
const IGetProfileInfoResponseParamsMap = nameOf<IGetProfileInfoResponse>();
const IMobileNumberDtoParamsMap = nameOf<IMobileNumberDto>();
const IDialCodeDataDtoParamsMap = nameOf<IDialCodeDataDto>();
const IAddressDtoParamsMap = nameOf<IAddressDto>();
const IEmailAddressDtoParamsMap = nameOf<IEmailAddressDto>();
const ISpeakingLanguageDtoParamsMap = nameOf<ISpeakingLanguageDto>();
const IPatchProfileInfoRequestParamsMap = nameOf<IPatchProfileInfoRequest>();
const IRegTeacherPersonalInformationDtoParamsMap = nameOf<IRegTeacherPersonalInformationDto>();

const parentProfileFields: ProfileFieldConfig[] = [
    { fieldName: IBasicProfileInfoDtoParamsMap.firstName, required: true, propertyName: 'First Name' },
    { fieldName: IBasicProfileInfoDtoParamsMap.lastName, required: true, propertyName: 'Last Name' },
    { fieldName: IBasicProfileInfoDtoParamsMap.gender!, required: false, propertyName: 'Gender' },
    { fieldName: IBasicProfileInfoDtoParamsMap.dateOfBirth!, required: false, propertyName: 'Date of Birth' },
    { fieldName: IBasicProfileInfoDtoParamsMap.timeZoneIana!, required: true, propertyName: 'Timezone' },
    { fieldName: IDialCodeDataDtoParamsMap.dialCode!, required: true, propertyName: 'Dial Code' },
    { fieldName: 'emailAddress', required: true, disabled: true, propertyName: 'Email Address' },
    { fieldName: IAddressDtoParamsMap.addressLine1!, required: true },
    { fieldName: IAddressDtoParamsMap.addressLine2!, required: false },
    { fieldName: IAddressDtoParamsMap.city!, required: true, propertyName: 'City' },
    { fieldName: IAddressDtoParamsMap.state!, required: false, propertyName: 'State' },
    { fieldName: IAddressDtoParamsMap.postCode!, required: true, propertyName: 'Post Code' },
    { fieldName: IAddressDtoParamsMap.country!, required: true },
    { fieldName: IMobileNumberDtoParamsMap.mobileNumber!, required: true },
];

const profileConfig: RoleProfileConfig = {
    [IUserRole.STUDENT]: [
        { fieldName: IBasicProfileInfoDtoParamsMap.firstName, required: true, propertyName: "First Name" },
        { fieldName: IBasicProfileInfoDtoParamsMap.lastName, required: true, propertyName: "Last Name" },
        { fieldName: IBasicProfileInfoDtoParamsMap.gender!, required: true, propertyName: "Gender" },
        { fieldName: IBasicProfileInfoDtoParamsMap.dateOfBirth!, required: true, propertyName: "Date of Birth" },
        { fieldName: IBasicProfileInfoDtoParamsMap.timeZoneIana!, required: true, propertyName: "Timezone" },
        { fieldName: IAddressDtoParamsMap.addressLine1!, required: false, propertyName: "Address Line 1" },
        { fieldName: IAddressDtoParamsMap.addressLine2!, required: false, propertyName: "Address Line 2" },
        { fieldName: IAddressDtoParamsMap.city!, required: true, propertyName: "City" },
        { fieldName: IAddressDtoParamsMap.state!, required: false, propertyName: "State" },
        { fieldName: IAddressDtoParamsMap.postCode!, required: false, propertyName: "Post Code" },
        { fieldName: IAddressDtoParamsMap.country!, required: true, propertyName: "Country" },
    ],
    [IUserRole.PARENT]: [
        ...parentProfileFields
    ],
    [IUserRole.TEACHER]: [
        ...parentProfileFields,
        {
            fieldName: IBasicProfileInfoDtoParamsMap.zoom!,
            required: false,
            propertyName: 'Zoom username',
            disabled: true
        },
        {
            fieldName: IBasicProfileInfoDtoParamsMap.msTeamsEmailAddress!,
            required: false,
            propertyName: IBasicProfileInfoDtoParamsMap.msTeamsEmailAddress!,
            disabled: true
        },
    ],
};

@Component({
    selector: 'app-settings-user-info-form',
    imports: [
        CommonModule, ReactiveFormsModule, InputTextModule, TextareaModule, ButtonModule,
        DropdownModule, RadioButtonModule, DatePickerModule, FileUploadModule,
        InputSwitchModule,
        SelectModule,
        AccordionModule,
        TooltipModule,
        PrimeReactiveFormInputComponent, PrimeTimezoneDropdownComponent, PrimeCountriesDropdownComponent,
        CountryPhoneInputComponent, FormFieldValidationMessageComponent, PrimeDateOfBirthPickerComponent,
        PrimeReactiveFormArrayInputComponent, LoaderDirective, ConfirmDialogModule, BadgeModule,
        LanguageLevelSelectorComponent,
    ],
    providers: [ConfirmationService],
    templateUrl: './settings-user-info-form.component.html',
    styleUrl: './settings-user-info-form.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class SettingsUserInfoFormComponent {

    // Dependencies
    private sanitizer = inject(DomSanitizer);
    authService = inject(AuthStateService);
    generalService = inject(GeneralService);
    dataApiStateService = inject(DataApiStateService);
    private eventBusService = inject(EventBusService);
    private apiResponseService = inject(HandleApiResponseService);
    private toastService = inject(ToastService);
    private popupStateService = inject(PopupStateService);

    // Inputs
    mainTitle = input('My Account');
    userInfo = input({} as IGetProfileInfoResponse);
    showTitleButton = input(false);
    isParentEditingStudent = input(false);
    userRole = input(this.authService.getUserRole());
    displayMode = signal(true);

    // Signals and Outputs
    countryCode = signal<string>('');
    removedEmails = signal<IEmailAddressDto[]>([]);
    @Output() titleButtonClick = new EventEmitter<void>();
    @Output() formSubmitted = new EventEmitter<boolean>(false);
    viewportScroller = inject(ViewportScroller);
    formErrorScroller = inject(FormErrorScrollerService);
    confirmationService = inject(ConfirmationService);
    apiLoadingStateService = inject(ApiLoadingStateService);
    private fb = inject(FormBuilder);
    private router = inject(Router);
    private initialConfirmedEmails: IEmailAddressDto[] = [];
    teachingLanguages = signal<string[]>([]);
    // Component State
    userForm!: FormGroup;
    fileObject: SafeUrl | string = 'assets/images/dummy-family.png';
    uploadedFiles: File[] = [];
    invalidFields = signal([] as string[]);
    submissionMessage = signal<string | null>(null);
    dateFormatToSubmit = 'YYYY-MM-DDT00:00:00';
    profileConfig = profileConfig;
    private untilDestroyed = untilDestroyed();
    IUserRole = IUserRole;
    Severity = Severity;
    IGenderEnum = IGenderEnum;
    nativeLanguages$ = computed(() => this.dataApiStateService.getNativeLanguages.state() || []);

    getUserProfileInfo$ = computed(() => {
        return this.dataApiStateService.getUserProfileInfo.state() || [] as State<IGetProfileInfoResponse>[];
    });

    userInfo$ = computed(() => {
        return this.dataApiStateService.getUserProfileInfo.state().data || {} as IGetProfileInfoResponse;
    })

    ngOnInit(): void {
        this.initializeComponent();

        // TODO: Remove this code once API is implemented
        // this.apiResponseService.getApiData<IPatchProfileInfoRequest>({ 
        //     url: IProfileInfo.getTeacherWorkProfile, method: 'GET' }, 
        //     {TeacherId: this.authService.getUserClaims().id})
        //     .subscribe({
        //         next: (response: IPatchProfileInfoResponse) => {
        //             console.log(response);
        //         },
        //         error: (err) => this.handleErrorResponse(err),
        //     });

    }

    finalRole(): IUserRole {
        return this.isParentEditingStudent() ? IUserRole.STUDENT : this.userRole();
    }

    getUserInfoEmailsExceptPrimary(): IEmailAddressDto[] {
        const emailAddresses = this.userForm.get('emailAddresses')?.value as IEmailAddressDto[];
        const primaryEmail = this.userForm.get('emailAddress')?.value as string;
        return emailAddresses?.filter(email => email.email !== primaryEmail) || [];
    }

    onTitleButtonClick(): void {
        this.titleButtonClick.emit();
    }

    onSelectedDateOfBirth(event: Date | null) {
        if (event && moment(event).isValid()) {
            const localStartOfDay = moment(event).startOf('day').toDate();
            this.userForm.patchValue({ dateOfBirth: localStartOfDay });
            console.log('Stored Local Start of Day:', localStartOfDay);
        }
    }

    onCountryCodeChange(data: string): void {
        console.log(data);
        this.countryCode.set(data);
        this.userForm.patchValue({ mobileNumberCountryCode: this.countryCode() });
        this.userForm.updateValueAndValidity();
    }

    onDialCodeChange(event: IDialCodeDataDto): void {
        console.log(event);
        this.userForm.patchValue({ dialCode: event, mobileNumber: this.userForm.getRawValue().mobileNumber });
        this.userForm.updateValueAndValidity();
        this.userForm.markAsDirty();
    }

    initialNonPrimaryEmailValues() {
        return this.userInfo().emails?.filter(e => !e.isPrimary) || [];
    }

    get emailAddresses(): FormArray {
        return this.userForm.get('emailAddresses') as FormArray;
    }

    get newEmailAddresses(): FormArray {
        return this.userForm.get('NewEmailAddresses') as FormArray;
    }

    get speakingLanguages(): FormArray<SpeakingLanguageFormGroup> {
        return this.userForm.get(IRegTeacherPersonalInformationDtoParamsMap.speakingLanguages) as FormArray<SpeakingLanguageFormGroup>;
    }

    getFormArrayErrorMessage(errors: { minLengthArray?: { requiredLength: number } }): string {
        return errors?.minLengthArray ? `At least ${errors.minLengthArray.requiredLength} emails are required` : 'Unknown FormArray error';
    }

    openConfirmEmailDialog(emailAddress: string) {
        const popupId = 'otpConfirmEmail';
        this.popupStateService.setPopupState(popupId, { show: true, data: { emailAddress } });
        this.generalService.openComponent(RequestOtpConfirmDialogComponent, { dialogId: popupId, emailAddress });
    }

    removeEmail(index: number): void {
        this.userInfo().emails!.splice(index, 1);
        this.emailAddresses.removeAt(index);
        this.userForm.updateValueAndValidity();
    }

    toggleDisplayMode(): void {
        this.displayMode.set(!this.displayMode());
    }

    // Update onEdit() method to use displayMode state
    onEdit(): void {
        this.displayMode.set(true);

        this.generalService.setupDynamicStateValidation(this.userForm, 'country', 'state');
    }

    onCancel() {
        this.displayMode.set(false);
        this.userForm.reset(); // Reset form to original values
    }

    onSubmit(): void {
        this.userForm.markAllAsTouched();
        this.userForm.markAsDirty();

        if (!this.formErrorScroller.validateAndScrollToFirstError(this.userForm)) {
            console.log('Form is invalid', this.userForm.value);
            return;
        }

        if (this.userForm.valid) {

            if (this.removedEmails().length > 0) {
                this.promptEmailRemovalConfirmation(this.removedEmails());
            } else {
                this.submitForm(this.buildPayload(this.userForm.getRawValue()));
            }
        } else {
            this.invalidFields.set(this.generalService.findInvalidControls(this.userForm));
            this.viewportScroller.setOffset([0, 77]);
            this.viewportScroller.scrollToAnchor('invalid-controls');
        }
    }

    getFilteredLanguageOptions(index: number): string[] {
        // Get all currently selected languages except the one at the current index
        const selectedLanguages = this.speakingLanguages.controls
            .map((control, i) => i !== index ? control.get('language')?.value : null)
            .filter(lang => lang); // Remove null/undefined values

        // Filter out already selected languages from nativeLanguages$
        const allLanguages = this.nativeLanguages$()?.data?.languages || [];
        return allLanguages.filter((lang: ISpeakingLanguageDto) => !selectedLanguages.includes(lang.language));
    }

    private initializeComponent(): void {

        if (this.finalRole() === IUserRole.TEACHER) {
            this.eventBusService.emit(new EmitEvent(Events.StateLoadNativeLanguages, undefined));
            this.fetchTeacherWorkProfile();
        }
        this.userForm = this.createForm();
        this.userForm.updateValueAndValidity();
        this.generalService.setupDynamicStateValidation(this.userForm, 'country', 'state');
        // Initialize initialConfirmedEmails
        this.initialConfirmedEmails = this.getUserInfoEmailsExceptPrimary()
            .filter((e: IEmailAddressDto) => e.isConfirmed)
            .map((e: IEmailAddressDto) => ({ ...e })); // Deep copy to avoid reference issues
        this.subscribeToFormStatusChanges();
        this.trackEmailChanges();
    }

    private subscribeToFormStatusChanges(): void {
        this.userForm.statusChanges.pipe(this.untilDestroyed()).subscribe(() => {
            this.invalidFields.set(CustomValidators.findInvalidControls(this.userForm));
        });
    }

    private fetchTeacherWorkProfile(): void {
        this.apiResponseService
            .getApiData<IGetTeacherWorkProfileResponse>(
                { url: IProfileInfo.getTeacherWorkProfile, method: 'GET' },
                { TeacherId: this.authService.getUserClaims().id }
            )
            .subscribe({
                next: (response: IGetTeacherWorkProfileResponse) => {
                    // Extract teaching languages from the response
                    const teachingLangs = response.workProfile?.teachingLanguages?.map(lang => lang.teachingLanguageName);
                    this.teachingLanguages.set(teachingLangs as string[]);
                },
                error: () => {
                    this.toastService.show(getToastMessage(ToastMessages.UserSettingsSaved.error, {}));
                },
            });
    }

    // Helper method to check if a language is a teaching language
    isTeachingLanguage(language: string): boolean {
        return this.teachingLanguages().includes(language);
    }

    private createForm(): FormGroup {
        const userInfo = this.userInfo();
        const roleConfig = this.profileConfig[userInfo?.basicProfileInfoDto?.discriminator] || [];

        const form = this.fb.group(
            roleConfig.reduce((controls, field) => this.addFormControl(controls, field, userInfo), {} as { [key: string]: FormControl | FormArray })
        );


        if (this.finalRole() === IUserRole.TEACHER) {
            // Add speakingLanguages FormArray
            form.addControl(IRegTeacherPersonalInformationDtoParamsMap.speakingLanguages, this.fb.array(
                userInfo.speakingLanguages?.map(lang => this.createLanguageControl(lang)) || [] as FormGroup[],
                [Validators.required, CustomValidators.atLeastOneNativeLanguageValidator(),
                (formArray: AbstractControl): ValidationErrors | null => {
                    if (!(formArray instanceof FormArray)) {
                        return null;
                    }

                    let hasErrors = false;
                    const errors: { [key: string]: any } = {};

                    formArray.controls.forEach((control, index) => {
                        const languageLevel = control.get('languageLevel')?.value;
                        if (languageLevel) {
                            const validator = CustomValidators.validateLanguageLevelAsRange(true);
                            const languageLevelControl = control.get('languageLevel');
                            const validationResult = languageLevelControl ? validator(languageLevelControl) : null;

                            if (validationResult) {
                                hasErrors = true;
                                errors[`language${index}`] = validationResult;
                            }
                        }
                    });

                    return hasErrors ? { invalidLanguageLevels: errors } : null;
                }
                ]
            ));
        }

        return form;
    }


    // Helper method to create language control
    private createLanguageControl(language?: ISpeakingLanguageDto): SpeakingLanguageFormGroup {
        return this.fb.group({
            language: this.fb.control(
                language?.language || '',
                [Validators.required, CustomValidators.nonEmptyStringValidator()]
            ),
            isNative: this.fb.control(language?.isNative || false),
            languageLevel: this.fb.control(
                language?.languageLevel || null,
                [Validators.required, CustomValidators.validateLanguageLevelAsRange(true)]
            )
        }) as SpeakingLanguageFormGroup;
    }

    // Add new language
    addLanguage(): void {
        this.speakingLanguages.push(this.createLanguageControl());
    }

    // Remove language
    removeLanguage(index: number): void {
        this.speakingLanguages.removeAt(index);
    }


    onNativeLanguageLevelChange(value: ILanguageLevelsEnum, index: number): void {
        const speakingLanguagesArray = this.userForm.get(IRegTeacherPersonalInformationDtoParamsMap.speakingLanguages) as FormArray;
        const languageControl = speakingLanguagesArray.at(index);

        // Apply the validation
        const validator = CustomValidators.validateLanguageLevelAsRange(true);
        const validationResult = validator(new FormControl(value));

        if (validationResult) {
            // If validation fails, set both the value and the error
            languageControl.patchValue({
                languageLevel: value
            });
            languageControl.get('languageLevel')?.setErrors(validationResult);
        } else {
            // If validation passes, update the value
            languageControl.patchValue({
                languageLevel: value
            });
            languageControl.get('languageLevel')?.setErrors(null);
        }
    }

    private addFormControl(controls: { [key: string]: FormControl | FormArray }, { fieldName, required, disabled }: ProfileFieldConfig, userInfo: IGetProfileInfoResponse) {
        let value = this.getInitialValue(fieldName);
        const validators = this.getValidators(fieldName, required);

        if (fieldName === IBasicProfileInfoDtoParamsMap.dateOfBirth && value) value = this.formatDate(value);
        if (fieldName === 'emailAddress') {
            const primaryEmail = userInfo.emails?.find(e => e.isPrimary)?.email || '';
            controls['emailAddress'] = new FormControl({ value: primaryEmail, disabled: !!disabled }, [Validators.required, Validators.email, Validators.minLength(1)]);
            const nonPrimaryEmails = userInfo.emails?.filter(e => !e.isPrimary) || [];
            controls['emailAddresses'] = this.fb.array(nonPrimaryEmails.map(email => this.fb.control(email, [])));
            controls['NewEmailAddresses'] = this.fb.array([Validators.required, Validators.email, Validators.minLength(1)]);
        } else {
            controls[fieldName] = new FormControl({ value, disabled: !!disabled }, validators);
        }
        return controls;
    }

    private getInitialValue(fieldName: string): any {
        const { basicProfileInfoDto: basic, userAddress: address, emails, mobileNumberDto: mobile } = this.userInfo();
        const timezone = { timezoneValue: basic?.timeZoneIana || '', timeZoneDisplayName: basic?.timeZoneDisplayName || '', timezoneFilterName: basic?.timeZoneId || '' };

        return {
            firstName: basic?.firstName, lastName: basic?.lastName, gender: basic?.gender, dateOfBirth: basic?.dateOfBirth,
            zoom: basic?.zoom,
            msTeamsEmailAddress: basic?.msTeamsEmailAddress,
            timeZoneIana: timezone, emailAddress: emails?.filter(e => e.isPrimary).map(e => e.email)[0] || '',
            emailAddresses: emails?.filter(e => !e.isPrimary) || [], country: address?.country, addressLine1: address?.addressLine1,
            addressLine2: address?.addressLine2, city: address?.city, postCode: address?.postCode, state: address?.state,
            dialCode: mobile?.dialCodeData, mobileNumber: mobile?.mobileNumber,
        }[fieldName] ?? null;
    }

    private getValidators(fieldName: string, required?: boolean): ValidatorFn[] {
        const validators = required ? [Validators.required] : [];
        return {
            firstName: [...validators, this.nameValidator(IBasicProfileInfoDtoParamsMap.firstName)],
            lastName: [...validators, this.nameValidator(IBasicProfileInfoDtoParamsMap.lastName)],
            emailAddress: [...validators, Validators.email, Validators.minLength(1)],
            mobileNumber: [...validators, this.phoneNumberValidator()],
        }[fieldName] || validators;
    }

    private nameValidator(fieldName: string) {
        return (control: AbstractControl) => CustomValidators.nameValidator(control, fieldName);
    }

    private phoneNumberValidator() {
        return (control: AbstractControl) => CustomValidators.phoneNumberValidator(this.userForm?.get('dialCode')?.value + control.value, 'Mobile Number', this.countryCode());
    }

    private buildPayload(formValues: any): Partial<IPatchProfileInfoRequest> {
        const roleConfig = this.profileConfig[this.finalRole()] || [];
        const payload: Partial<IPatchProfileInfoRequest> = {
            userId: this.userInfo().basicProfileInfoDto.userId,
            emailAddressesDto: [],
            userAddressDto: {},
            mobileNumberDto: {},
            speakingLanguageDtos: this.userInfo().speakingLanguages.map((lang: ISpeakingLanguageDto): ISpeakingLanguageDto => ({
                language: lang.language,
                isNative: lang.isNative,
                languageLevel: lang.languageLevel,
            }))
        };

        roleConfig.forEach(({ fieldName }) => this.mapFieldToPayload(payload, fieldName, formValues[fieldName]));

        if (this.finalRole() === IUserRole.TEACHER) {
            // Add speaking languages to payload
            console.log('Form Values for Payload:', formValues); // Debug log
            payload.speakingLanguageDtos = formValues.speakingLanguages.map((lang: ISpeakingLanguageDto): ISpeakingLanguageDto => ({
                language: lang.language,
                isNative: lang.isNative,
                languageLevel: lang.languageLevel,
            }));
        }

        if (this.finalRole() === IUserRole.PARENT || this.finalRole() === IUserRole.STUDENT) {
            this.adjustPayloadForStudentRole(payload);
        }

        if (this.finalRole() === IUserRole.TEACHER) {
            payload.speakingLanguageDtos = this.speakingLanguages.controls
                .map(control => {
                    const values = control.getRawValue();
                    return {
                        language: values.language,
                        isNative: values.isNative,
                        languageLevel: values.languageLevel
                    } as ISpeakingLanguageDto;
                });
        }
        return payload;
    }

    private mapFieldToPayload(payload: Partial<IPatchProfileInfoRequest>, fieldName: string, value: any) {
        if (!value) return;


        const basicFields = [
            IBasicProfileInfoDtoParamsMap.firstName,
            IBasicProfileInfoDtoParamsMap.lastName,
            IBasicProfileInfoDtoParamsMap.gender,
            IBasicProfileInfoDtoParamsMap.zoom,
            IBasicProfileInfoDtoParamsMap.msTeamsEmailAddress
        ];

        const addressFields = [
            IAddressDtoParamsMap.country,
            IAddressDtoParamsMap.addressLine1,
            IAddressDtoParamsMap.addressLine2,
            IAddressDtoParamsMap.city,
            IAddressDtoParamsMap.postCode,
            IAddressDtoParamsMap.state
        ];
        const mobileFields = ['dialCode', 'mobileNumber'];

        if (basicFields.includes(fieldName as typeof basicFields[number])) {
            (payload as any)[fieldName] = value;
        }
        else if (fieldName === IBasicProfileInfoDtoParamsMap.dateOfBirth && moment(value).isValid()) {
            const formattedDate = this.formatDate(value);
            payload[fieldName] = new Date(formattedDate);
        }
        else if (fieldName === 'timeZoneIana') payload[fieldName] = value?.timezoneValue || value;
        else if (fieldName === 'emailAddress') this.mapEmailFields(payload, value, this.userForm.getRawValue());
        else if (addressFields.includes(fieldName as typeof addressFields[number])) {
            payload.userAddressDto = {
                ...payload.userAddressDto,
                [fieldName as keyof IAddressDto]: value?.name || value
            };
        }
        else if (mobileFields.includes(fieldName)) payload.mobileNumberDto = { ...payload.mobileNumberDto, [fieldName === 'dialCode' ? 'dialCodeData' : 'mobileNumber']: value };
    }

    private mapEmailFields(payload: Partial<IPatchProfileInfoRequest>, primaryEmail: string,
        formValues: Record<string, IEmailAddressDto[] | string[]>) {
        const nonPrimaryEmails = formValues['emailAddresses'] as IEmailAddressDto[] || [];
        const newEmails = formValues['NewEmailAddresses'] as string[] || [];
        payload.emailAddressesDto = [
            { email: primaryEmail, isConfirmed: true, isPrimary: true },
            ...nonPrimaryEmails.map((email: IEmailAddressDto) => ({ email: email.email, isConfirmed: false, isPrimary: false })),
            ...newEmails.map((email: string) => ({ email, isConfirmed: false, isPrimary: false })),
        ];
    }

    private adjustPayloadForStudentRole(payload: any) {
        if (this.finalRole() === IUserRole.STUDENT) {
            payload.emailAddressesDto = null;
            payload.mobileNumberDto = null;
        }
    }

    private formatDate(date: string | Date): string {
        const formatted = moment(date).format(this.dateFormatToSubmit);
        console.log('Input to formatDate:', date, 'Formatted Date:', formatted);
        return formatted;
    }

    private submitForm(payload: any): void {

        this.generalService.showDivLoading('.userform_info__container');
        this.apiResponseService.getApiData<IPatchProfileInfoRequest>({ url: IProfileInfo.patchProfileInfo, method: 'PATCH' },
            payload, true)
            .subscribe({
                next: (response: IPatchProfileInfoResponse) => this.handleSuccessResponse(response),
            });
    }

    private handleSuccessResponse(response: IPatchProfileInfoResponse): void {
        this.generalService.hideDivLoading();
        this.emitEvents();
        this.toastService.show(ToastMessages.UserSettingsSaved.success);
        setTimeout(() => {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }, 10);
        console.log(response);

        if (this.removedEmails().length > 0) {
            const message = `The following confirmed email addresses have been removed: ${this.removedEmails().map((e: IEmailAddressDto) => e.email).join(', ')}`;
            this.toastService.show({ severity: Severity.Info, summary: 'Confirmed Emails Removed', detail: message });
        }

        this.displayMode.set(false);
        this.formSubmitted.emit(true);
    }

    private handleErrorResponse(err: unknown): void {
        this.generalService.hideDivLoading();
        console.error('Error updating profile:', err);
    }

    private emitEvents(): void {
        const userId = this.userInfo().basicProfileInfoDto.userId;
        if (userId) this.eventBusService.emit(new EmitEvent(Events.StateLoadProfileInfo, { userId }));
    }

    trackEmailChanges(): void {
        if (!this.emailAddresses) {
            return;
        }
        this.emailAddresses.valueChanges.pipe(this.untilDestroyed()).subscribe((currentEmails: IEmailAddressDto[]) => {
            const currentEmailStrings = currentEmails.map(e => e.email);
            const removedConfirmed = this.initialConfirmedEmails.filter(
                (emailDto: IEmailAddressDto) => !currentEmailStrings.includes(emailDto.email)
            );

            this.removedEmails.set(removedConfirmed);
            console.log("🚀 ~ Removed Emails:", this.removedEmails());
        });
    }

    private promptEmailRemovalConfirmation(removedEmails: IEmailAddressDto[]): void {
        const message = `The following email(s) will be removed: <br>
         <b>${removedEmails.map(e => e.email).join(' <br>')}</b> 
        <br>
        Are you sure you want to proceed ?`;

        this.confirmationService.confirm({
            message: message,
            header: 'Confirm Email Removal',
            icon: 'pi pi-exclamation-triangle',
            acceptButtonStyleClass: 'p-button-primary',
            acceptLabel: 'Yes, Remove',
            rejectLabel: 'Not yet',
            accept: () => {
                // If user confirms, proceed with submission and update initialConfirmedEmails
                this.initialConfirmedEmails = (this.emailAddresses)
                    .value
                    .filter((e: IEmailAddressDto) => e.isConfirmed)
                    .map((e: IEmailAddressDto) => ({ ...e }));
                // Clear removedEmails after acceptance
                this.removedEmails.set([]);
                this.submitForm(this.buildPayload(this.userForm.getRawValue()));
            },
            reject: () => {
                // If user rejects, restore the removed emails and clear removedEmails
                const emailAddresses = this.userForm.get('emailAddresses') as FormArray;
                removedEmails.forEach(email => {
                    const exists = emailAddresses.value.some((e: IEmailAddressDto) => e.email === email.email);
                    if (!exists) {
                        emailAddresses.push(this.fb.control(email));
                    }
                });
                this.removedEmails.set([]); // Clear removedEmails to prevent re-triggering
                console.log('Email removal cancelled, restored emails:', emailAddresses.value);
                this.userForm.updateValueAndValidity();
            }
        });
    }
}