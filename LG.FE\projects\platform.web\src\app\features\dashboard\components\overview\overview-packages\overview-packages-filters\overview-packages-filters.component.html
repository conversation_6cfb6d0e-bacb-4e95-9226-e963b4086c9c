<div class="filters-container">
  <!-- Teaching Language Filter -->
  <div class="filter-section">
    <div class="filter-header">
      <h4 class="filter-title">
        <i class="pi pi-globe"></i>
        Teaching Language
      </h4>
    </div>
    <div class="filter-content">
      <p-dropdown
        [options]="filterState.teachingLanguages"
        [ngModel]="currentFilters().teachingLanguageId"
        (ngModelChange)="onTeachingLanguageChange($event)"
        placeholder="Select teaching language"
        optionLabel="name"
        optionValue="id"
        [showClear]="true"
        styleClass="w-full modern-dropdown"
        [filter]="true"
        filterBy="name"
        [resetFilterOnHide]="true">
        
        <ng-template pTemplate="selectedItem" let-selectedOption>
          <div class="flex align-items-center gap-2" *ngIf="selectedOption">
            <i class="pi pi-globe text-primary"></i>
            <span>{{ selectedOption.name }}</span>
          </div>
        </ng-template>
        
        <ng-template pTemplate="item" let-option>
          <div class="flex align-items-center gap-2">
            <i class="pi pi-globe text-primary"></i>
            <span>{{ option.name }}</span>
          </div>
        </ng-template>
        
        <ng-template pTemplate="emptyfilter">
          <div class="text-center p-3">
            <i class="pi pi-search text-muted"></i>
            <p class="text-muted mt-2 mb-0">No languages found</p>
          </div>
        </ng-template>
      </p-dropdown>
    </div>
  </div>

  <!-- Purchase Date Range Filter -->
  <div class="filter-section">
    <div class="filter-header">
      <h4 class="filter-title">
        <i class="pi pi-shopping-cart"></i>
        Purchase Date Range
      </h4>
    </div>
    <div class="filter-content">
      <div class="flex flex-column gap-3">
        <div>
          <label class="text-sm text-600 mb-2 block">From Date</label>
          <p-calendar
            [ngModel]="currentFilters().purchasedFrom"
            (ngModelChange)="onPurchasedFromChange($event)"
            placeholder="Select start date"
            [showIcon]="true"
            [showClear]="true"
            styleClass="w-full"
            dateFormat="dd/mm/yy">
          </p-calendar>
        </div>
        <div>
          <label class="text-sm text-600 mb-2 block">To Date</label>
          <p-calendar
            [ngModel]="currentFilters().purchasedTo"
            (ngModelChange)="onPurchasedToChange($event)"
            placeholder="Select end date"
            [showIcon]="true"
            [showClear]="true"
            styleClass="w-full"
            dateFormat="dd/mm/yy">
          </p-calendar>
        </div>
      </div>
    </div>
  </div>

  <!-- Expiry Date Range Filter -->
  <div class="filter-section">
    <div class="filter-header">
      <h4 class="filter-title">
        <i class="pi pi-calendar"></i>
        Expiry Date Range
      </h4>
    </div>
    <div class="filter-content">
      <div class="flex flex-column gap-3">
        <div>
          <label class="text-sm text-600 mb-2 block">From Date</label>
          <p-calendar
            [ngModel]="currentFilters().expiresFrom"
            (ngModelChange)="onExpiresFromChange($event)"
            placeholder="Select start date"
            [showIcon]="true"
            [showClear]="true"
            styleClass="w-full"
            dateFormat="dd/mm/yy">
          </p-calendar>
        </div>
        <div>
          <label class="text-sm text-600 mb-2 block">To Date</label>
          <p-calendar
            [ngModel]="currentFilters().expiresTo"
            (ngModelChange)="onExpiresToChange($event)"
            placeholder="Select end date"
            [showIcon]="true"
            [showClear]="true"
            styleClass="w-full"
            dateFormat="dd/mm/yy">
          </p-calendar>
        </div>
      </div>
    </div>
  </div>

  <!-- Add-On Extension Filter -->
  <div class="filter-section">
    <div class="filter-header">
      <h4 class="filter-title">
        <i class="pi pi-plus-circle"></i>
        Package Extensions
      </h4>
    </div>
    <div class="filter-content">
      <div class="flex align-items-center gap-2">
        <p-checkbox
          [ngModel]="currentFilters().hasAddOnExtension"
          (ngModelChange)="onHasAddOnExtensionChange($event)"
          [binary]="true"
          inputId="hasAddOnExtension">
        </p-checkbox>
        <label for="hasAddOnExtension" class="text-sm text-600">
          Show only packages with add-on extensions
        </label>
      </div>
    </div>
  </div>

  <!-- Filter Actions (Hidden - handled by drawer) -->
  <div class="filter-actions" style="display: none;">
    <button 
      type="button" 
      pButton 
      label="Apply Filters" 
      icon="pi pi-check" 
      class="p-button-primary"
      (click)="emitSearchAction()">
    </button>
    <button 
      type="button" 
      pButton 
      label="Reset" 
      icon="pi pi-refresh" 
      class="p-button-outlined"
      (click)="emitResetAction()">
    </button>
  </div>
</div>
