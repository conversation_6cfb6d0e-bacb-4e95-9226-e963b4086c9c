

<app-card-split-layout [maxWidth]="'700px'" containerClass="p-0" [showRightSide]="false"
  [rightBgImage]="'/assets/images/graphic/formify-bg-21.svg'">
  <div left>
    <div class="flex flex-column gap-4 align-items-center justify-content-center h-full overflow-hidden relative z-3 
              ">


      <div
        class="w-full flex flex-column align-items-center justify-content-center z-3 p-3 registerTrialSteps__container"
        [appLoader]="generalService.divLoading()">


        <div class="sm:px-3 w-full" style="border-radius:53px">


<div class="change-teacher-request-container p-3 md:p-4">
  <!-- Progress Steps -->
  <div class="progress-section mb-2 md:mb-4">
    <app-dynamic-progress-steps
      #progressSteps
      [stepDefinitions]="stepDefinitions"
      [flowTitle]="'Change Teacher Request'"
      [baseRoutePattern]="'change-teacher-request'"
      [navigationCallback]="handleStepNavigation"
      (stepChanged)="onStepChanged($event)"
      (navigationAttempted)="onNavigationAttempted($event)">
    </app-dynamic-progress-steps>
  </div>

  <!-- Main Content -->
  <div class="content-section">
    <!-- Step 1: Teacher Change Selection -->
    @if (currentStep() === ChangeTeacherStep.SELECTION) {
      <div class="step-content selection-step">
        <div class="step-header mb-4 md:mb-3">
          <h2 class="text-xl md:text-xl font-semibold text-900 mb-2">Choose Teacher Change Option</h2>
          <p class="text-600 line-height-3 text-sm md:text-sm">
            Please select the type of teacher change you would like to request.
          </p>
        </div>

        <div class="selection-options max-w-full md:max-w-30rem mx-auto">
          <!-- Complete Teacher Change Option -->
          <div class="selection-card mb-3 cursor-pointer"
                  [class.selected]="selectedChangeType() === TeacherChangeType.COMPLETE_CHANGE"
                  (click)="onChangeTypeSelect(TeacherChangeType.COMPLETE_CHANGE)">
            <div class="flex align-items-start gap-3 p-2 md:p-3">
              <p-radioButton 
                name="changeType" 
                [value]="TeacherChangeType.COMPLETE_CHANGE"
                [(ngModel)]="selectedChangeType"
                [inputId]="TeacherChangeType.COMPLETE_CHANGE">
              </p-radioButton>
              
              <div class="flex-1">
                <label [for]="TeacherChangeType.COMPLETE_CHANGE" class="cursor-pointer">
                  <div class="selection-content">
                    <div class="flex align-items-center gap-2 mb-2">
                      <i class="pi pi-user-edit text-primary text-lg md:text-xl"></i>
                      <h3 class="text-base md:text-lg font-semibold text-900 m-0">Change teacher completely</h3>
                    </div>
                    <p class="text-600 text-xs md:text-sm line-height-3 m-0">
                      Request a complete change of your current teacher. This will affect all your lessons and sessions.
                    </p>
                  </div>
                </label>
              </div>
            </div>
          </div>

          <!-- Student/Group Specific Change Option -->
          <div class="selection-card cursor-pointer"
                  [class.selected]="selectedChangeType() === TeacherChangeType.STUDENT_GROUP_CHANGE"
                  (click)="onChangeTypeSelect(TeacherChangeType.STUDENT_GROUP_CHANGE)">
            <div class="flex align-items-start gap-3 p-2 md:p-3">
              <p-radioButton
                name="changeType"
                [value]="TeacherChangeType.STUDENT_GROUP_CHANGE"
                [(ngModel)]="selectedChangeType"
                [inputId]="TeacherChangeType.STUDENT_GROUP_CHANGE">
              </p-radioButton>

              <div class="flex-1">
                <label [for]="TeacherChangeType.STUDENT_GROUP_CHANGE" class="cursor-pointer">
                  <div class="selection-content">
                    <div class="flex align-items-center gap-2 mb-2">
                      <i class="pi pi-users text-primary text-lg md:text-xl"></i>
                      <h3 class="text-base md:text-lg font-semibold text-900 m-0">Change teacher for a student/group</h3>
                    </div>
                    <p class="text-600 text-xs md:text-sm line-height-3 m-0">
                      Request a teacher change for a specific student or group while keeping your current teacher for other lessons.
                    </p>
                  </div>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    }

    <!-- Step 2: Student/Group Selection (only for STUDENT_GROUP_CHANGE) -->
    @if (currentStep() === ChangeTeacherStep.STUDENT_GROUP_SELECTION && shouldShowStudentGroupStep()) {
      <div class="step-content student-group-selection-step">
        <app-student-group-selection-step
          [initialData]="studentGroupSelectionData()"
          (dataChanged)="onStudentGroupSelectionDataChanged($event)"
          (validationChanged)="onStudentGroupSelectionValidationChanged($event)">
        </app-student-group-selection-step>
      </div>
    }

    <!-- Step 3: Details -->
    @if (currentStep() === ChangeTeacherStep.DETAILS) {
      <div class="step-content details-step">

        <!-- Complete Teacher Change Step -->
        @if (selectedChangeType() === TeacherChangeType.COMPLETE_CHANGE) {
          <app-complete-teacher-change-step
            [initialData]="getInitialStepData()"
            [changeType]="selectedChangeType()!"
            (dataChanged)="onCompleteChangeStepDataChanged($event)"
            (validationChanged)="onCompleteChangeStepValidationChanged($event)">
          </app-complete-teacher-change-step>
        }

        <!-- Student/Group Specific Change Step -->
        @if (selectedChangeType() === TeacherChangeType.STUDENT_GROUP_CHANGE) {
          <app-complete-teacher-change-step
            [initialData]="getInitialStepData()"
            [changeType]="selectedChangeType()!"
            [studentGroupData]="studentGroupSelectionData()"
            (dataChanged)="onCompleteChangeStepDataChanged($event)"
            (validationChanged)="onCompleteChangeStepValidationChanged($event)">
          </app-complete-teacher-change-step>
        }

      </div>
    }

    <!-- Step 4: Review & Confirm -->
    @if (currentStep() === ChangeTeacherStep.REVIEW_CONFIRM) {
      <div class="step-content review-confirm-step">

        <!-- Success State -->
        @if (submissionSuccess()) {
          <div class="confirmation-content text-center p-6">
            <i class="pi pi-check-circle text-6xl text-green-500 mb-4"></i>
            <h2 class="text-2xl font-semibold text-900 mb-3">Request Submitted Successfully!</h2>
            <p class="text-600 mb-4 max-w-30rem mx-auto line-height-3">
              Your teacher change request has been submitted and will be reviewed by our team.
              You will be notified once the request is processed.
            </p>

            <div class="flex flex-column sm:flex-row justify-content-center gap-3">
              <p-button
                label="Start Again"
                icon="pi pi-refresh"
                (onClick)="startAgain()"
                styleClass="p-button-outlined p-button-secondary">
              </p-button>
              <p-button
                label="Return to Dashboard"
                icon="pi pi-home"
                routerLink="/dashboard"
                styleClass="p-button-primary">
              </p-button>
            </div>
          </div>
        } @else {
          <!-- Review & Confirm Content -->
          @if (teacherChangeRequest()) {
            <app-teacher-change-review-step
              [teacherChangeData]="getReviewStepData()"
              [changeType]="selectedChangeType()!"
              [studentGroupData]="studentGroupSelectionData()"
              (confirmRequest)="onReviewStepConfirm($event)">
            </app-teacher-change-review-step>

            <!-- Error Message -->
            @if (submissionError()) {
              <div class="error-message mt-4 p-3 border-1 border-red-300 border-round bg-red-50">
                <p class="text-red-700 text-sm m-0">
                  <i class="pi pi-exclamation-triangle mr-2"></i>
                  {{ submissionError() }}
                </p>
              </div>
            }
          } @else {
            <div class="step-header mb-4">
              <h2 class="text-2xl font-semibold text-900 mb-2">Review & Confirm Changes</h2>
              <p class="text-600 line-height-3">
                Review the impact of your teacher change request and confirm submission.
              </p>
            </div>

            <div class="placeholder-content">
              <p class="text-center text-600 py-6">
                No data available for review. Please go back and complete the previous steps.
              </p>
            </div>
          }
        }
      </div>
    }


  </div>

  <!-- Navigation Buttons -->
  @if (!submissionSuccess()) {
    <div class="navigation-section mt-4 md:mt-6">
      <div class="flex flex-row md:flex-row justify-content-between align-items-center gap-3 md:gap-0">
        <p-button
          [label]="getPreviousButtonLabel()"
          icon="pi pi-chevron-left"
          [outlined]="true"
          [disabled]="isFirstStep() || isSubmitting()"
          (click)="previousStep()"
          class="p-button-secondary w-full md:w-auto">
        </p-button>

        <div class="flex gap-2 w-full md:w-auto justify-content-center">
          <p-button
            [label]="getNextButtonLabel()"
            [icon]="getNextButtonIcon()"
            iconPos="right"
            [disabled]="!canProceedToNextStep() || isSubmitting()"
            [loading]="isSubmitting()"
            (click)="handleNextAction()"
            [class]="getNextButtonClass()">
          </p-button>
        </div>
      </div>
    </div>
  }
</div>

        </div>
      </div>
    </div>

  </div>

</app-card-split-layout>


