@use 'projects/shared-modules.library/src/lib/styles/mixins.scss' as mixins;

.students-filters-container {
  height: 100%;
  overflow-y: auto;
  
  .filter-section {
    margin-bottom: 1.5rem;
    
    label {
      display: flex;
      align-items: center;
      margin-bottom: 0.5rem;
      font-weight: 600;
      color: var(--text-color);
      
      i {
        color: var(--primary-color);
        margin-right: 0.5rem;
      }
    }
    
    // Input styling
    :host ::ng-deep {
      .p-inputtext,
      .p-select,
      .p-datepicker,
      .p-slider {
        width: 100%;
      }
      
      .p-select {
        .p-select-label {
          padding: 0.75rem;
        }
      }
      
      .p-datepicker {
        .p-inputtext {
          padding: 0.75rem;
        }
      }
      
      .p-slider {
        .p-slider-handle {
          width: 1.2rem;
          height: 1.2rem;
          margin-top: -0.6rem;
          margin-left: -0.6rem;
        }
      }
    }
  }
  
  .filter-actions {
    position: sticky;
    bottom: 0;
    background: var(--surface-ground);
    padding: 1rem 0 0 0;
    margin-top: auto;
    
    .p-button {
      @include mixins.breakpoint(mobile) {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
      }
    }
  }
}

// Responsive adjustments
@include mixins.breakpoint(mobile) {
  .students-filters-container {
    padding: 1rem;
    
    .filter-section {
      margin-bottom: 1rem;
      
      label {
        font-size: 0.875rem;
        margin-bottom: 0.25rem;
      }
    }
    
    .grid {
      .col-12 {
        padding: 0.25rem;
      }
    }
  }
}

@include mixins.breakpoint(tablet) {
  .students-filters-container {
    // padding: 1.5rem;
  }
}

// Custom styling for specific components
:host ::ng-deep {
  .p-checkbox {
    .p-checkbox-box {
      width: 1.2rem;
      height: 1.2rem;
      
      .p-checkbox-icon {
        font-size: 0.75rem;
      }
    }
  }
  
  .p-slider {
    .p-slider-range {
      background: var(--primary-color);
    }
    
    .p-slider-handle {
      border-color: var(--primary-color);
      background: var(--primary-color);
      
      &:focus {
        box-shadow: 0 0 0 0.2rem var(--primary-color-text);
      }
    }
  }
  
  .p-datepicker {
    .p-datepicker-trigger {
      background: transparent;
      border: none;
      color: var(--text-color-secondary);
      
      &:hover {
        background: var(--surface-hover);
      }
    }
  }
}

// Dark theme adjustments
:host-context(.dark-theme) {
  .students-filters-container {
    .filter-actions {
      background: var(--surface-ground);
      border-color: var(--surface-border);
    }
  }
}
