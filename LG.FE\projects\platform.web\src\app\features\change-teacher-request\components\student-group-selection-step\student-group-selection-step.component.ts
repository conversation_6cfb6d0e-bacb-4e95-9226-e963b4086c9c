import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, signal, computed, OnInit, Input, Output, EventEmitter, DestroyRef, Injector } from '@angular/core';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';

// PrimeNG Imports
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { DividerModule } from 'primeng/divider';
import { RadioButtonModule } from 'primeng/radiobutton';
import { TagModule } from 'primeng/tag';
import { DropdownModule } from 'primeng/dropdown';
import { SelectModule } from 'primeng/select';

// Shared Library
import {
  ISearchStudentDto,
  ISearchStudentGroupsDto,
  ITeachingLanguageDto,
  IGetAllTeachingLanguagesResponse,
  TeachingLanguagesRoutes,
  PrimeStudentsSelectionComponent,
  PrimeStudentGroupSelectionComponent,
  HandleApiResponseService,
  IStudentTeachingLanguageDto
} from 'SharedModules.Library';

export enum StudentGroupSelectionType {
  STUDENT = 'student',
  GROUP = 'group'
}

export interface IStudentGroupSelectionStepData {
  selectionType?: StudentGroupSelectionType;
  studentId?: string;
  student?: ISearchStudentDto;
  groupId?: string;
  group?: ISearchStudentGroupsDto;
  teachingLanguageId?: string;
  teachingLanguage?: ITeachingLanguageDto;
}

@Component({
  selector: 'app-student-group-selection-step',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ButtonModule,
    CardModule,
    DividerModule,
    RadioButtonModule,
    TagModule,
    DropdownModule,
    SelectModule,
    PrimeStudentsSelectionComponent,
    PrimeStudentGroupSelectionComponent
  ],
  templateUrl: './student-group-selection-step.component.html',
  styleUrl: './student-group-selection-step.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class StudentGroupSelectionStepComponent implements OnInit {
  private readonly fb = inject(FormBuilder);
  private readonly destroy = inject(DestroyRef);
  private readonly apiService = inject(HandleApiResponseService);
  private readonly injector = inject(Injector);

  @Input() initialData?: IStudentGroupSelectionStepData;
  @Output() dataChanged = new EventEmitter<IStudentGroupSelectionStepData>();
  @Output() validationChanged = new EventEmitter<boolean>();

  readonly StudentGroupSelectionType = StudentGroupSelectionType;

  form!: FormGroup;
  selectionType = signal<StudentGroupSelectionType | null>(null);
  selectedStudent = signal<ISearchStudentDto | null>(null);
  selectedGroup = signal<ISearchStudentGroupsDto | null>(null);
  allTeachingLanguages = signal<ITeachingLanguageDto[]>([]);
  selectedTeachingLanguage = signal<ITeachingLanguageDto | null>(null);
  isLoadingTeachingLanguages = signal(false);
  private isLoadingInitialData = signal(false);

  teachingLanguages = computed(() => {
    const allLanguages = this.allTeachingLanguages();
    const selectionType = this.selectionType();
    const selectedStudent = this.selectedStudent();
    const selectedGroup = this.selectedGroup();

    if (!selectionType) return [];

    if (selectionType === StudentGroupSelectionType.STUDENT && selectedStudent) {
      const studentLanguageIds = selectedStudent.studentTeachingLanguageDto?.map((lang: any) => lang.teachingLanguageId) || [];
      return allLanguages.filter((lang: ITeachingLanguageDto) => studentLanguageIds.includes(lang.id!));
    }

    if (selectionType === StudentGroupSelectionType.GROUP && selectedGroup) {
      return allLanguages.filter((lang: ITeachingLanguageDto) => lang.id === selectedGroup.teachingLanguageId);
    }

    return [];
  });

  isTeachingLanguageDisabled = computed(() => {
    const selectionType = this.selectionType();
    const teachingLanguages = this.teachingLanguages();

    return selectionType === StudentGroupSelectionType.GROUP ||
           (selectionType === StudentGroupSelectionType.STUDENT && teachingLanguages.length === 1);
  });



  currentData = computed<IStudentGroupSelectionStepData>(() => {
    const type = this.selectionType();
    const student = this.selectedStudent();
    const group = this.selectedGroup();
    const teachingLanguage = this.selectedTeachingLanguage();

    return {
      selectionType: type || undefined,
      studentId: student?.userId || undefined,
      student: student || undefined,
      groupId: group?.id || undefined,
      group: group || undefined,
      teachingLanguageId: teachingLanguage?.id || undefined,
      teachingLanguage: teachingLanguage || undefined
    };
  });

  isValid = computed(() => {
    const { type, student, group, teachingLanguage } = this.getValidationData();

    const validationRules = [
      { condition: !teachingLanguage?.id, message: 'Teaching language is required' },
      { condition: !type, message: 'Selection type is required' },
      { condition: type === StudentGroupSelectionType.STUDENT && !student?.userId, message: 'Student selection is required' },
      { condition: type === StudentGroupSelectionType.GROUP && !group?.id, message: 'Group selection is required' }
    ];

    return !validationRules.some(rule => rule.condition);
  });

  private getValidationData() {
    return {
      type: this.selectionType(),
      student: this.selectedStudent(),
      group: this.selectedGroup(),
      teachingLanguage: this.selectedTeachingLanguage()
    };
  }

  ngOnInit(): void {
    this.initializeForm();
    this.setupFormSubscriptions();
    this.loadTeachingLanguages();
    this.loadInitialData();
  }

  private initializeForm(): void {
    this.form = this.fb.group({
      selectionType: ['', [Validators.required]]
    });
  }

  private setupFormSubscriptions(): void {
    this.form.get('selectionType')?.valueChanges
      .pipe(takeUntilDestroyed(this.destroy))
      .subscribe(value => this.handleSelectionTypeChange(value));
  }

  private handleSelectionTypeChange(value: StudentGroupSelectionType | null): void {
    this.selectionType.set(value || null);
    if (!this.isLoadingInitialData()) this.clearPreviousSelections();
    this.emitDataChange();
  }

  private clearPreviousSelections(): void {
    this.selectedStudent.set(null);
    this.selectedGroup.set(null);
  }

  private loadInitialData(): void {
    if (!this.initialData) {
      this.emitDataChange();
      return;
    }

    this.isLoadingInitialData.set(true);

    if (this.initialData.selectionType) {
      this.selectionType.set(this.initialData.selectionType);
      this.form.get('selectionType')?.setValue(this.initialData.selectionType);
    }

    if (this.initialData.student) this.selectedStudent.set(this.initialData.student);
    if (this.initialData.group) this.selectedGroup.set(this.initialData.group);

    if (this.initialData.teachingLanguage) {
      this.setInitialTeachingLanguage(this.initialData.teachingLanguage);
    } else if (this.initialData.group) {
      this.setInitialTeachingLanguageForGroup(this.initialData.group);
    } else {
      this.isLoadingInitialData.set(false);
      this.emitDataChange();
    }
  }

  private setInitialTeachingLanguage(teachingLanguage: ITeachingLanguageDto): void {
    if (this.canSetInitialTeachingLanguage()) {
      this.finalizeTeachingLanguageSetup(teachingLanguage);
    } else {
      this.waitForInitialDataReadiness(teachingLanguage);
    }
  }

  private canSetInitialTeachingLanguage(): boolean {
    const hasTeachingLanguages = this.allTeachingLanguages().length > 0;
    const selectionType = this.selectionType();
    const hasValidSelection = selectionType === StudentGroupSelectionType.STUDENT
      ? this.selectedStudent() !== null
      : selectionType === StudentGroupSelectionType.GROUP
        ? this.selectedGroup() !== null
        : false;

    return hasTeachingLanguages && hasValidSelection;
  }

  private waitForInitialDataReadiness(teachingLanguage: ITeachingLanguageDto): void {
    const checkAndSetLanguage = () => {
      if (this.canSetInitialTeachingLanguage()) {
        this.finalizeTeachingLanguageSetup(teachingLanguage);
      }
    };

    toObservable(this.isLoadingTeachingLanguages, { injector: this.injector })
      .pipe(takeUntilDestroyed(this.destroy))
      .subscribe(checkAndSetLanguage);

    toObservable(this.selectionType, { injector: this.injector })
      .pipe(takeUntilDestroyed(this.destroy))
      .subscribe(checkAndSetLanguage);

    toObservable(this.selectedStudent, { injector: this.injector })
      .pipe(takeUntilDestroyed(this.destroy))
      .subscribe(checkAndSetLanguage);

    toObservable(this.selectedGroup, { injector: this.injector })
      .pipe(takeUntilDestroyed(this.destroy))
      .subscribe(checkAndSetLanguage);
  }

  private finalizeTeachingLanguageSetup(teachingLanguage: ITeachingLanguageDto): void {
    this.setTeachingLanguageFromLoadedArray(teachingLanguage);
    this.isLoadingInitialData.set(false);
    this.emitDataChange();
  }

  private setInitialTeachingLanguageForGroup(group: ISearchStudentGroupsDto): void {
    if (this.allTeachingLanguages().length > 0) {
      this.autoSelectTeachingLanguageForGroup(group);
      this.isLoadingInitialData.set(false);
      this.emitDataChange();
    } else {
      toObservable(this.isLoadingTeachingLanguages, { injector: this.injector })
        .pipe(takeUntilDestroyed(this.destroy))
        .subscribe((isLoading: boolean) => {
          if (!isLoading && this.allTeachingLanguages().length > 0) {
            this.autoSelectTeachingLanguageForGroup(group);
            this.isLoadingInitialData.set(false);
            this.emitDataChange();
          }
        });
    }
  }

  private setTeachingLanguageFromLoadedArray(teachingLanguage: ITeachingLanguageDto): void {
    const selectionType = this.selectionType();
    const selectedGroup = this.selectedGroup();

    if (selectionType === StudentGroupSelectionType.GROUP && selectedGroup) {
      const groupTeachingLanguage = this.allTeachingLanguages().find((lang: ITeachingLanguageDto) => lang.id === selectedGroup.teachingLanguageId);
      if (groupTeachingLanguage && groupTeachingLanguage.id === teachingLanguage.id) {
        this.selectedTeachingLanguage.set(groupTeachingLanguage);
        return;
      }
    }

    const availableLanguages = this.teachingLanguages();
    const matchingAvailableLanguage = availableLanguages.find((lang: ITeachingLanguageDto) => lang.id === teachingLanguage.id);

    if (matchingAvailableLanguage) {
      this.selectedTeachingLanguage.set(matchingAvailableLanguage);
    } else {
      const matchingLanguage = this.allTeachingLanguages().find((lang: ITeachingLanguageDto) => lang.id === teachingLanguage.id);
      this.selectedTeachingLanguage.set(matchingLanguage || null);
    }
  }

  onSelectionTypeChange(type: StudentGroupSelectionType): void {
    this.selectionType.set(type);
    this.form.get('selectionType')?.setValue(type);

    if (!this.isLoadingInitialData()) {
      this.clearPreviousSelections();
      this.selectedTeachingLanguage.set(null);
    }
    this.emitDataChange();
  }

  onStudentSelected(student: ISearchStudentDto | ISearchStudentDto[]): void {
    const selectedStudent = Array.isArray(student) ? student[0] : student;
    this.selectedStudent.set(selectedStudent);

    if (!this.isLoadingInitialData()) {
      this.autoSelectTeachingLanguageForStudent(selectedStudent);
    }
    this.emitDataChange();
  }

  onStudentCleared(): void {
    this.selectedStudent.set(null);
    this.emitDataChange();
  }

  onGroupSelected(group: ISearchStudentGroupsDto): void {
    this.selectedGroup.set(group);
    setTimeout(() => {
      this.autoSelectTeachingLanguageForGroup(group);
      this.emitDataChange();
    }, 0);
  }

  onGroupCleared(): void {
    this.selectedGroup.set(null);
    this.emitDataChange();
  }

  private autoSelectTeachingLanguageForStudent(student: ISearchStudentDto): void {
    const studentLanguageIds = student.studentTeachingLanguageDto?.map((lang: IStudentTeachingLanguageDto) => lang.teachingLanguageId) || [];
    const availableLanguages = this.allTeachingLanguages().filter((lang: ITeachingLanguageDto) => studentLanguageIds.includes(lang.id!));

    this.selectedTeachingLanguage.set(availableLanguages.length === 1 ? availableLanguages[0] : null);
  }

  private autoSelectTeachingLanguageForGroup(group: ISearchStudentGroupsDto): void {
    const groupTeachingLanguage = this.allTeachingLanguages().find((lang: ITeachingLanguageDto) => lang.id === group.teachingLanguageId);
    this.selectedTeachingLanguage.set(groupTeachingLanguage || null);
  }

  onTeachingLanguageSelected(languageId: string): void {
    const language = this.findTeachingLanguageById(languageId);
    this.selectedTeachingLanguage.set(language);
    this.emitDataChange();
  }

  onTeachingLanguageCleared(): void {
    this.selectedTeachingLanguage.set(null);
    this.emitDataChange();
  }

  private findTeachingLanguageById(languageId: string): ITeachingLanguageDto | null {
    return this.allTeachingLanguages().find((lang: ITeachingLanguageDto) => lang.id === languageId) || null;
  }

  private emitDataChange(): void {
    this.dataChanged.emit(this.currentData());
    this.validationChanged.emit(this.isValid());
  }

  getData(): IStudentGroupSelectionStepData {
    return this.currentData();
  }

  reset(): void {
    this.form.reset();
    this.selectionType.set(null);
    this.selectedStudent.set(null);
    this.selectedGroup.set(null);
    this.selectedTeachingLanguage.set(null);
    this.emitDataChange();
  }

  getSelectionTypeLabel(type: StudentGroupSelectionType): string {
    const labels = {
      [StudentGroupSelectionType.STUDENT]: 'Individual Student',
      [StudentGroupSelectionType.GROUP]: 'Student Group'
    };
    return labels[type] || '';
  }

  getSelectionTypeDescription(type: StudentGroupSelectionType): string {
    const descriptions = {
      [StudentGroupSelectionType.STUDENT]: 'Request teacher change for a specific student only',
      [StudentGroupSelectionType.GROUP]: 'Request teacher change for an entire student group'
    };
    return descriptions[type] || '';
  }

  private loadTeachingLanguages(): void {
    this.isLoadingTeachingLanguages.set(true);

    this.apiService.getApiData<IGetAllTeachingLanguagesResponse>({
      url: TeachingLanguagesRoutes.getAllTeachingLanguages,
      method: 'GET'
    })
      .pipe(takeUntilDestroyed(this.destroy))
      .subscribe({
        next: (response: IGetAllTeachingLanguagesResponse) => {
          this.allTeachingLanguages.set(response.teachingLanguages || []);
          this.isLoadingTeachingLanguages.set(false);
        },
        error: () => {
          this.isLoadingTeachingLanguages.set(false);
        }
      });
  }


}
