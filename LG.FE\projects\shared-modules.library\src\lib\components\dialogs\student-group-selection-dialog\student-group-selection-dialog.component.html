<app-custom-dialog-popup [header]="'Title'" [maximizable]="false" [styleClass]="dialogStyleClass()"
  [style]="dialogStyle$()" [visible]="dialogVisible()">

  <ng-container dialogHeader>
    <div
      class=" relative w-full inline-flex align-items-center text-white font-semibold justify-content-center gap-2 fluid-title">
      <div *ngIf="canGoBack$() && this.editGroupState() !== this.EditGroupState.AfterEditSuccess" class="">
        <p-button styleClass="text-white w-2rem h-2rem back-btn-popup" (click)="backToViewGroup()"
          icon="pi pi-chevron-left" [rounded]="true" [outlined]="true" />
      </div>

      <span>
        {{ dialogHeader$() }}
      </span>
    </div>
  </ng-container>
  <ng-container dialogFooter>

    <ng-container dialogFooter>
      <ng-container [ngSwitch]="editGroupState()">
        <!-- Edit Group or Create Group states -->
        <ng-container *ngSwitchCase="EditGroupState.EditGroup">
          <div class="flex flex-column align-items-center justify-content-center w-full text-center gap-3 py-2">
            <!-- {{this.formComponent?.editGroupForm?.valid}} -->
            <p-button (click)="onAvailabilityStepSubmitted()" class="w-full flex align-items-center"
              [disabled]="!this.formComponent?.canSaveGroup()"
              [loading]="services.apiLoadingStateService.getIsLoading()" [icon]="'pi pi-check-circle'" [rounded]="true"
              label="Save Group Changes" styleClass="submit-btn w-full mx-2"></p-button>
          </div>
        </ng-container>
        <ng-container *ngSwitchCase="EditGroupState.CreateGroupSuggestionStep">
          <div class="flex flex-column align-items-center justify-content-center w-full text-center gap-3 py-2">
            <p-button (click)="onGroupStateChanged(EditGroupState.CreateGroup)" class="w-full flex align-items-center"
              [icon]="'pi pi-arrow-right'" [iconPos]="'right'" [rounded]="true" label="Proceed"
              styleClass="submit-btn w-full mx-2"></p-button>
          </div>
        </ng-container>

        <ng-container *ngSwitchCase="EditGroupState.CreateGroup">
          <div class="flex flex-column align-items-center justify-content-center w-full text-center gap-3 py-2">
            <p-button (click)="onAvailabilityStepSubmitted()" class="w-full flex align-items-center"
              [disabled]="!this.formComponent?.canSaveGroup()" [icon]="'pi pi-check'" [rounded]="true"
              label="Create Group" styleClass="submit-btn w-full mx-2"></p-button>
          </div>
        </ng-container>
      </ng-container>
    </ng-container>
  </ng-container>

  <!-- {{students$() | json}} -->
  <!-- {{studentGroupItem() | json}} -->



  <!-- ViewGroup State - Compact Modern UI -->
  <ng-container *ngIf="editGroupState() === EditGroupState.ViewGroup">
    <div class="view-group-container" *ngIf="studentGroupItem()">
      <!-- Compact Header -->
      <div class="group-header-compact">
        <div class="group-icon-minimal">
          <i class="pi pi-users"></i>
        </div>
        <!-- {{studentGroupItem() | json}} -->
        <div class="group-info-minimal">
          <h3 class="group-name-compact">{{ getGroupDetails(false) }}</h3>
          <div class="group-meta-compact">
            <span class="language-pill">{{ studentGroupItem().teachingLanguageName || 'Language' }}</span>
            <span class="level-pill">{{ services.general.getILanguageLevelsEnumText(studentGroupItem().groupLevel,
              false) }}</span>
            <span class="members-count">{{ studentGroupItem().studentInfo?.length || 0 }} members</span>
          </div>
        </div>
      </div>

      <!-- Compact Info Grid -->
      <div class="info-grid-compact">
        <!-- Members Section -->
        <div class="info-card members-compact">
          <div class="card-header-minimal">
            <i class="pi pi-users"></i>
            <span>Members</span>
          </div>

          <lib-students-display *ngIf="studentGroupItem().studentInfo?.length; else noMembers"
            [students]="studentGroupItem().studentInfo!" [config]="{
            layout: 'horizontal',
            size: 'normal',
            showImages: true,
            maxVisible: 5,
            showMoreButton: true,
            styleClass: 'w-full'
          }">
          </lib-students-display>

          <ng-template #noMembers>
            <div class="empty-state-mini">
              <i class="pi pi-user-plus"></i>
              <span>No members</span>
            </div>
          </ng-template>
        </div>

      </div>

      <!-- Action Buttons -->
      <div class="actions-compact">
        <div class="primary-actions flex gap-2">


          <p-button label="Edit Group" icon="pi pi-pencil" size="small" styleClass="btn-standard btn-soft-info"
            (click)="onEditGroupClicked()">
          </p-button>

          <p-button label="Delete Group" icon="pi pi-trash" size="small" severity="danger"
            styleClass="btn-standard btn-soft-danger" (click)="onDeleteGroupClicked()">
          </p-button>
        </div>

      </div>
    </div>
  </ng-container>

  <!-- <ng-container *ngIf="editGroupState() === EditGroupState.CreateGroupSuggestionStep">
    <app-student-group-selection-suggestion-text-step
      (buttonClicked)="onSuggestionTextButtonSelected($event)"></app-student-group-selection-suggestion-text-step>
  </ng-container> -->

  <ng-container *ngIf="editGroupState() === EditGroupState.None
    || editGroupState() === EditGroupState.CreateGroup
    || editGroupState() === EditGroupState.DeleteGroup
    || editGroupState() === EditGroupState.EditGroup
    || editGroupState() === EditGroupState.AfterCreateSuccess
     || editGroupState() === EditGroupState.CreateGroupSuggestionStep">

    <div class="">

      <ng-container *ngIf="editGroupState() === EditGroupState.AfterCreateSuccess">
        <div class="group-success-container">
          <!-- Success Header -->
          <div class="success-header">
            <div class="success-icon">
              <div class="icon-background">
                <i class="pi pi-check"></i>
              </div>
              <div class="success-glow"></div>
            </div>

            <div class="success-content">
              <h2 class="success-title">Student Group Created Successfully!</h2>
              <p class="success-subtitle">Your learning constellation is ready for you to start learning!</p>
            </div>
          </div>

          <!-- Group Showcase Card -->
          <div class="group-showcase-card">
            <div class="group-header">
              <div class="group-icon">
                <i class="pi pi-users"></i>
                <div class="orbit-ring"></div>
              </div>

              <div class="group-details">
                <h3 class="group-name">{{ getGroupDetails(false) }}</h3>

                <div class="group-meta" *ngIf="studentGroupItem()">
                  <div class="meta-item">
                    <i class="pi pi-globe"></i>
                    <span>{{ studentGroupItem().teachingLanguageName || studentGroupItem().groupName }}</span>
                  </div>
                  <div class="meta-item">
                    <i class="pi pi-star-fill"></i>
                    <span class="level-badge">{{
                      services.general.getILanguageLevelsEnumText(studentGroupItem().groupLevel, false) }}</span>
                  </div>
                  <div class="meta-item">
                    <i class="pi pi-users"></i>
                    <span>{{ studentGroupItem().studentInfo?.length || 0 }} members</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Group Members Preview -->
            @if (studentGroupItem().studentInfo?.length) {
            <div class="members-preview" *ngIf="studentGroupItem().studentInfo?.length">
              <div class="members-header">
                <span class="members-title">Students</span>
              </div>

              <lib-students-display *ngIf="studentGroupItem().studentInfo?.length"
                [students]="studentGroupItem().studentInfo!" [config]="{
            layout: 'horizontal',
            size: 'normal',
            showImages: true,
            maxVisible: 5,
            showMoreButton: true,
            styleClass: 'w-full'
          }">
              </lib-students-display>

            </div>
            }
          </div>

          <!-- Action Buttons -->
          <div class="success-actions">
            <p-button label="Explore Groups" icon="pi pi-arrow-right" iconPos="right" styleClass="primary-action-btn"
              (click)="onGoBackToViewGroups()">
            </p-button>

            <p-button label="Create Another Group" icon="pi pi-plus" iconPos="left" styleClass="secondary-action-btn"
              (click)="resetToCreateGroupSuggestion()">
            </p-button>
          </div>
        </div>
      </ng-container>

      <ng-container *ngIf="editGroupState() === EditGroupState.EditGroup
        || editGroupState() === EditGroupState.CreateGroup
        || editGroupState() === EditGroupState.CreateGroupSuggestionStep">

        <!-- {{ editGroupState() }} -->
        <!--  (onGroupCreated)="onGroupCreated($event)" -->
        <!-- {{studentGroupItem() | json}} -->
        <app-parent-overview-group-create-form
        [parentId]="this.parentId"
        [isEditMode]="editMode()" [isDialogPopup]="true"
          [groupId]="studentGroupItem() && studentGroupItem().id ? studentGroupItem().id : null"
          [editGroupState]="editGroupState()" (onGroupCreated)="onGroupCreated($event)"
          (onGroupStateChanged)="onGroupStateChanged($event)"
          (onGroupFormValidityChanged)="onGroupFormValidityChanged($event)"></app-parent-overview-group-create-form>

        <!-- <app-student-group-selection-members-step [editGroupState]="editGroupState()" [editMode]="editMode()"
          (groupStateChanged)="onGroupStateChanged($event)" (groupItemChanged)="onGroupItemChanged($event)"
          (onMembersStepSubmitted)="onMembersStepSubmitted($event)"></app-student-group-selection-members-step> -->

      </ng-container>

      <!-- General Error Message -->
      <!-- <div *ngIf="this.services.general.errorDataSignal" class="p-1 my-2">
        <app-form-field-validation-message [severity]="Severity.Danger" styleClass="w-full justify-content-start"
          [text]="this.services.general.errorDataSignal">
        </app-form-field-validation-message>
      </div> -->

      <ng-container *ngIf="deleteGroupContent() && editMode()">
        <div class="px-3 py-3">
          <div class="p-1 mt-2">
            <h3 class="primary-purple-color m-0 text-center">Are you sure you want to delete the group?</h3>
          </div>
        </div>
        <ng-container dialogFooter>
          <div class="flex flex-column align-items-center justify-content-center w-full text-center gap-2">
            <p-button (click)="deleteGroupStepActionSelected('yes')" styleClass="w-16rem" label="Yes" [rounded]="true"
              [outlined]="true" severity="success" />
            <p-button (click)="backToViewGroup()" styleClass="w-16rem" label="No" [rounded]="true" [outlined]="true"
              severity="danger" />
          </div>
        </ng-container>
      </ng-container>

      <div
        *ngIf="canGoBack$() && !isEditModeOrAfterEdit && editGroupState() !== EditGroupState.CreateGroupSuggestionStep"
        class="flex flex-column align-items-center justify-content-center w-full text-center gap-3 mt-2 mb-4">
        <p-button label="Back" styleClass="w-16rem" (click)="backToViewGroup()" icon="pi pi-chevron-left"
          [rounded]="true" severity="help" [outlined]="true" />
      </div>

    </div>

    <!-- General Error Message -->
    <div *ngIf="this.services.general.errorDataSignal" class="p-1 my-2">

      <app-actionable-alert iconUrl="/assets/images/graphic/alarm_icon.svg" title=""
        [message]="this.services.general.errorDataSignal" alertClass="bg-red-50 border-1 border-red-500 p-3"
        imageClass="w-3rem" alertTextHeaderClass="text-xl font-bold text-red-700 mb-1"
        alertTextSubHeaderClass="text-sm text-600">
      </app-actionable-alert>

      <!-- <app-form-field-validation-message [severity]="Severity.Danger" styleClass="w-full justify-content-start"
            [text]="this.services.general.errorDataSignal">
          </app-form-field-validation-message> -->
    </div>

  </ng-container>


</app-custom-dialog-popup>