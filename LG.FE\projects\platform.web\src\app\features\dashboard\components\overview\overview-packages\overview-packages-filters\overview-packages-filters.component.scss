@use 'mixins';

.filters-container {
  
  .filter-section {
    margin-bottom: 1.5rem;
    
    .filter-header {
      margin-bottom: 0.75rem;
      
      .filter-title {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--text-color);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        
        i {
          color: var(--primary-color);
          font-size: 0.875rem;
        }
      }
    }
    
    .filter-content {
      .modern-dropdown {
        width: 100%;
        
        ::ng-deep .p-dropdown {
          width: 100%;
          border-radius: 6px;
          border: 1px solid var(--surface-border);
          
          &:not(.p-disabled):hover {
            border-color: var(--primary-color);
          }
          
          &.p-focus {
            outline: 0 none;
            outline-offset: 0;
            box-shadow: 0 0 0 0.2rem var(--primary-color-text);
            border-color: var(--primary-color);
          }
        }
      }
      
      // Calendar styling
   
      
      // Checkbox styling
      ::ng-deep .p-checkbox {
        .p-checkbox-box {
          border-radius: 4px;
          border: 1px solid var(--surface-border);
          
          &:not(.p-disabled):hover {
            border-color: var(--primary-color);
          }
          
          &.p-focus {
            outline: 0 none;
            outline-offset: 0;
            box-shadow: 0 0 0 0.2rem var(--primary-color-text);
            border-color: var(--primary-color);
          }
          
          &.p-highlight {
            border-color: var(--primary-color);
            background: var(--primary-color);
          }
        }
      }
    }
  }
  
  .filter-actions {
    display: flex;
    gap: 0.75rem;
    padding-top: 1rem;
    border-top: 1px solid var(--surface-border);
    margin-top: 1.5rem;
    
    button {
      flex: 1;
    }
  }
}

// Responsive adjustments
@include mixins.breakpoint(mobile) {
  .filters-container {
    .filter-section {
      margin-bottom: 1.25rem;
      
      .filter-header {
        margin-bottom: 0.5rem;
        
        .filter-title {
          font-size: 0.8125rem;
        }
      }
    }
    
    .filter-actions {
      flex-direction: column;
      gap: 0.5rem;
    }
  }
}
