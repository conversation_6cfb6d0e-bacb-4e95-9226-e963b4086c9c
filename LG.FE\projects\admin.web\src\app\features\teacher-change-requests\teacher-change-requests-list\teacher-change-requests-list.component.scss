// ============================================================================
// TEACHER CHANGE REQUESTS LIST COMPONENT STYLES
// ============================================================================

@use 'mixins';

.teacher-change-requests-list-container {
  height: 100%;
  
  // Ensure proper scrolling behavior
  :host ::ng-deep {
    .p-datatable-wrapper {
      overflow: auto;
      max-height: calc(100vh - 300px);
    }
    
    .p-datatable {
      .p-datatable-thead > tr > th {
        position: sticky;
        top: 0;
        z-index: 1;
        background: var(--surface-ground);
        border-bottom: 1px solid var(--surface-border);
      }
    }
    
    // Column specific styling
    .p-datatable-tbody > tr > td {
      vertical-align: top;
      padding: 0.75rem 0.5rem;
      
      // Request ID styling
      &:has(.text-primary) {
        font-family: 'Courier New', monospace;
      }
    }
    
    // Responsive column widths
    .p-column-title {
      font-weight: 600;
    }
    
    // Status tag styling
    .p-tag {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
    }
    
    // Tooltip styling for long text
    .cursor-pointer {
      cursor: help;
    }
  }
}

// Responsive design using mixins


// Loading state styling
:host ::ng-deep {
  .p-datatable-loading-overlay {
    background-color: rgba(255, 255, 255, 0.8);
    
    .p-datatable-loading-icon {
      font-size: 2rem;
      color: var(--primary-color);
    }
  }
}

// Empty state styling
:host ::ng-deep {
  .p-datatable-emptymessage > td {
    border: none !important;
    background: var(--surface-ground) !important;
  }
}

