@if (selectionMode === 'single') {
<!-- Debug info (remove in production) -->
<!-- {{ selectedTeacherDebug() | json}} -->
<p-select #selectTeachersList [appendTo]="'body'" [options]="displayItems()"
[(ngModel)]="selectedTeacher"
    [filter]="!enablePagination" [placeholder]="dropdownPlaceholder"
     class="w-full md:w-56" [styleClass]="styleClass()"
    (onChange)="onSelectionChange($event)"
    (onShow)="onDropdownShow()"
    (onHide)="onDropdownHide()" [showClear]="true"
    (onClear)="onClearSelection()"
    [loading]="isLoading()"
    [loadingIcon]="'pi pi-spinner pi-spin'"
    [autoOptionFocus]="true" [selectOnFocus]="false">

    <!-- Custom Header Template with Search -->
    @if (enableSearch && enablePagination) {
    <ng-template #header>
        <div class="p-3 border-bottom-1 surface-border" (click)="$event.stopPropagation()">
            <!-- Search Input with Loading State -->
            <div class="p-inputgroup" [class.search-loading]="isSearching()" [class.typing]="isTyping()">
                <p-iconfield>
                    <p-inputicon styleClass="pi pi-search" />
                    <input #searchInput pInputText type="text"
                    [placeholder]="getSearchPlaceholder()"
                        [(ngModel)]="searchInputValue" (input)="onSearchInputChange($event)"
                        (keydown.enter)="$event.preventDefault()" (click)="$event.stopPropagation()"
                        (focus)="$event.stopPropagation()" [disabled]="isSearching()" class="w-full" autocomplete="off">
                    @if (searchInputValue.trim() && !isSearching() && !isTyping()) {
                    <p-inputicon styleClass="pi pi-times text-primary cursor-pointer"  (click)="clearSearchInput()" [attr.aria-label]="'Clear search'" />
                    }
                    @if (isSearching()) {
                    <p-inputicon styleClass="pi pi-spin pi-spinner text-primary" />
                    }
                </p-iconfield>
            </div>

            <!-- Search Progress Indicator -->
            @if (isSearching()) {
            <div class="mt-2 p-2 bg-primary-50 border-round text-sm">
                <div class="flex align-items-center gap-2">
                    <i class="pi pi-spin pi-spinner text-primary text-xs"></i>
                    <span class="text-primary font-medium">
                        Searching for "{{ searchTerm() }}"...
                    </span>
                </div>
            </div>
            } @else if (isTyping() && searchInputValue.trim()) {
            <div class="mt-2 p-2 bg-orange-50 border-round text-sm">
                <div class="flex align-items-center gap-2">
                    <i class="pi pi-clock text-orange-500 text-xs"></i>
                    <span class="text-orange-600 font-medium">
                        Will search for "{{ searchInputValue }}"...
                    </span>
                </div>
            </div>
            }
        </div>
    </ng-template>
    }

    <ng-template #empty>
        @if (isSearchMode() && searchTerm() && !isLoading()) {
        <div class="text-center py-4">
            <i class="pi pi-search text-400 text-2xl mb-2"></i>
            <div class="text-sm text-500">No teachers found for "{{ searchTerm() }}"</div>
        </div>
        } @else {
        <div class="font-medium p-3">Available Teachers</div>
        }
    </ng-template>

    <!-- Selected item -->
    <ng-template #selectedItem let-selectedOption>
        <ng-container *ngTemplateOutlet="commonTemplate; context: { data: selectedOption }"></ng-container>
    </ng-template>

    <!-- Dropdown item with loading state -->
    <ng-template let-teacher #item>
        <div class="dropdown-item-wrapper" [class.loading-overlay]="isLoading() && displayItems().length === 0">
            <ng-container *ngTemplateOutlet="commonTemplate; context: { data: teacher }"></ng-container>
        </div>
    </ng-template>

    <!-- Loading overlay template for dropdown items -->
    @if (isLoading() && displayItems().length > 0) {
    <ng-template #loader>
        <div class="dropdown-loading-overlay">
            <div class="flex align-items-center justify-content-center gap-2 py-3">
                <i class="pi pi-spin pi-spinner text-primary"></i>
                <span class="text-sm text-600">
                    @if (isSearching()) {
                    Searching for more results...
                    } @else {
                    Loading more teachers...
                    }
                </span>
            </div>
        </div>
    </ng-template>
    }

    <!-- Load More Footer (only shown when pagination is enabled) -->
    <ng-template #footer>
        @if (enablePagination) {
        <div class="load-more-container p-2 border-top-1 surface-border" (click)="$event.stopPropagation()">
            <!-- Search mode indicator and clear button -->
            @if (isSearchMode() && searchTerm()) {
            <!-- <div
                class="search-indicator flex align-items-center justify-content-between gap-2 mb-2 p-2 bg-primary-50 border-round">
                <div class="flex align-items-center gap-2">
                    @if (isSearching()) {
                    <i class="pi pi-spin pi-spinner text-primary text-sm"></i>
                    <span class="text-sm text-primary font-medium">
                        Searching for "{{ searchTerm() }}"...
                    </span>
                    } @else {
                    }
                </div>
                <p-button icon="pi pi-times" size="small" styleClass="p-button-text p-button-sm clear-search-btn"
                    [pTooltip]="'Clear search'" (click)="clearSearchAndClose($event)">
                </p-button>
            </div> -->
            }

            <!-- Load More / Loading / Completion states -->
            @if (hasMoreData() && !isLoading()) {
            <p-button [label]="isSearchMode() ? 'Load More Results' : 'Load More Teachers'" icon="pi pi-chevron-down"
                styleClass="w-full p-button-text p-button-sm load-more-btn"
                (click)="onLoadMoreClicked();">
            </p-button>
            } @else if (isLoading()) {
            <div class="flex align-items-center justify-content-center gap-2 py-2">
                <i class="pi pi-spin pi-spinner text-primary"></i>
                <span class="text-sm text-600">
                    @if (isSearching()) {
                    Searching for more results...
                    } @else {
                    Loading more teachers...
                    }
                </span>
            </div>
            } @else if (!hasMoreData() && displayItems().length > 0) {
            <div class="text-center py-2">
                @if (isSearchMode()) {
                <span class="text-sm text-500">
                    All search results loaded ({{ displayItems().length }} of {{ totalRecords() }})
                </span>
                } @else {
                <span class="text-sm text-500">
                    All teachers loaded ({{ displayItems().length }} of {{ totalRecords() }})
                </span>
                }
            </div>
            }
        </div>
        }
    </ng-template>

    <!-- Common reusable template -->
    <ng-template #commonTemplate let-data="data">
        <div class="flex align-items-center gap-2" *ngIf="data">
            @let teacherProfile = data;
            <lib-prime-profile-photo-single [userId]="teacherProfile.id"
                customClass="w-2rem filter-brightness" [width]="32" [height]="32">
            </lib-prime-profile-photo-single>
            <div class="flex-1 min-w-0">
                <div class="text-sm font-medium text-900 truncate">
                    {{ getTeacherFullName(teacherProfile) }}
                </div>

                <!-- Compact info row -->
                <div class="flex align-items-center gap-2 text-xs text-600 mt-1">
                    <!-- Location (compact) -->
                    @if (teacherProfile.city || teacherProfile.country) {
                    <span class="flex align-items-center gap-1">
                        <i class="pi pi-map-marker text-400"></i>
                        {{ getTeacherLocationText(teacherProfile) }}
                    </span>
                    }

                    <!-- Languages (minimal) -->
                    @if (data.teacherTeachingLanguages?.length) {
                    <span class="flex align-items-center gap-1">
                        <i class="pi pi-globe text-400"></i>
                        {{ data.teacherTeachingLanguages.length }} language{{ data.teacherTeachingLanguages.length !== 1 ? 's' : '' }}
                    </span>
                    }

                    <!-- Students count -->
                    @if (teacherProfile.activeStudentsCount !== undefined) {
                    <span class="flex align-items-center gap-1">
                        <i class="pi pi-users text-400"></i>
                        {{ teacherProfile.activeStudentsCount }}
                    </span>
                    }
                </div>
            </div>
        </div>
    </ng-template>

</p-select>
}

@if (selectionMode === 'multiple') {

<p-multiselect #selectTeachersListMultiple class="w-full md:w-56" [appendTo]="'body'" [styleClass]="styleClass()" [(ngModel)]="selectedTeachers"
    (onChange)="onSelectMultipleChange($event)"
    (onClear)="onClearSelection()"
    [showClear]="true"
    [filterBy]="'firstName,lastName'" [options]="items()" [filter]="true"
    placeholder="Select a Teacher from the list">

    <ng-template let-data pTemplate="item">
        <div class="flex align-items-center gap-2">
            @let teacherProfile = data;

            <lib-prime-profile-photo-single [userId]="teacherProfile.id"
                customClass="w-2rem filter-brightness" [width]="32" [height]="32">
            </lib-prime-profile-photo-single>

            <div class="flex-1 min-w-0">
                <div class="text-sm font-medium text-900 truncate">
                    {{ getTeacherFullName(teacherProfile) }}
                </div>

                <!-- Compact info row -->
                <div class="flex align-items-center gap-2 text-xs text-600 mt-1">
                    <!-- Location (compact) -->
                    @if (teacherProfile.city || teacherProfile.country) {
                    <span class="flex align-items-center gap-1">
                        <i class="pi pi-map-marker text-400"></i>
                        {{ getTeacherLocationText(teacherProfile) }}
                    </span>
                    }

                    <!-- Languages (minimal) -->
                    @if (data.teacherTeachingLanguages?.length) {
                    <span class="flex align-items-center gap-1">
                        <i class="pi pi-globe text-400"></i>
                        {{ data.teacherTeachingLanguages.length }} language{{ data.teacherTeachingLanguages.length !== 1 ? 's' : '' }}
                    </span>
                    }

                    <!-- Students count -->
                    @if (teacherProfile.activeStudentsCount !== undefined) {
                    <span class="flex align-items-center gap-1">
                        <i class="pi pi-users text-400"></i>
                        {{ teacherProfile.activeStudentsCount }}
                    </span>
                    }
                </div>
            </div>
        </div>
    </ng-template>

    <ng-template let-selectedTeacherSa pTemplate="selectedItems">
        <div class="flex flex-wrap">
            <ng-container *ngFor="let teacher of selectedTeacherSa; let last = last">
                <span class="outline-1 border-gray-200 border-round pl-1">
                    {{ getTeacherFullName(teacher) }}</span>
                <span *ngIf="!last">,</span>
            </ng-container>
        </div>
    </ng-template>


    <ng-template #empty let-emptyMessage>
        <div class="font-medium">
            <div class="mt-1 flex flex-column md:flex-row align-items-center justify-content-between
            gap-3 px-3 py-2 border-round-md border-1 mb-3 bg-cyan-50 border-cyan-300 backdrop-blur-sm shadow-1">
               <div class="flex align-items-center gap-2">
                   <i class="pi pi-info-circle text-cyan-700 text-xl"></i>
                   <p class="text-cyan-700 text-sm m-0 line-height-3">
                       {{emptyMessageText}}
                   </p>
               </div>
           </div>
        </div>
    </ng-template>
    <ng-template #emptyMessage>
        <div class="font-medium px-3 py-2">Test</div>
    </ng-template>

    <ng-template #filtericon>
        <i class="pi pi-search"></i>
    </ng-template>
    <ng-template #header>
        <div class="font-medium px-3 py-2">Available Teachers</div>
    </ng-template>
    <ng-template #footer>
        <div class="p-3 flex justify-content-between align-items-center gap-2">
            <div class="flex gap-2">
                <p-button label="Remove All"
                         severity="danger"
                         text
                         size="small"
                         icon="pi pi-times"
                         (onClick)="clearAllTeachers()"/>
                <p-button label="Select All"
                         severity="secondary"
                         text
                         size="small"
                         icon="pi pi-check-circle"
                         (onClick)="selectAllTeachers()"/>
            </div>
            <div class="flex gap-2">
                <p-button label="Done"
                         severity="primary"
                         size="small"
                         icon="pi pi-check"
                         (onClick)="selectTeachersListMultiple.close($event)" />
            </div>
        </div>
    </ng-template>
</p-multiselect>
}
