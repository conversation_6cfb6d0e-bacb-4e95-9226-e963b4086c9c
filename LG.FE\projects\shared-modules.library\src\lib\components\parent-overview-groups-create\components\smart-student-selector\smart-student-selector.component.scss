@use "mixins";

:host {
  display: block;
  width: 100%;
}

// Professional Student Selector - Enterprise Grade Design
.professional-student-selector {
  width: 100%;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  // Compact Professional Selection Summary
  .compact-selection-summary {
    margin-bottom: 1.25rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    @include mixins.breakpoint(mobile) {
      padding: 0.875rem;
      margin-bottom: 1rem;
    }

    // Main Selection Info Row
    .selection-info-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // margin-bottom: 0.75rem;

      @include mixins.breakpoint(mobile) {
        margin-bottom: 0.625rem;
      }

      .language-section {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .pi-globe {
          color: #6366f1;
          font-size: 1rem;

          @include mixins.breakpoint(mobile) {
            font-size: 0.875rem;
          }
        }

        .language-name {
          font-size: 0.875rem;
          font-weight: 600;
          color: #374151;

          @include mixins.breakpoint(mobile) {
            font-size: 0.8125rem;
          }
        }
      }

      .students-section {
        display: flex;
        align-items: center;
        gap: 0.375rem;

        .student-count {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          min-width: 1.375rem;
          height: 1.375rem;
          padding: 0 0.25rem;
          background: #6366f1;
          color: white;
          font-size: 0.75rem;
          font-weight: 700;
          border-radius: 6px;

          @include mixins.breakpoint(mobile) {
            min-width: 1.25rem;
            height: 1.25rem;
            font-size: 0.6875rem;
          }
        }

        .student-label {
          font-size: 0.75rem;
          color: #6b7280;
          font-weight: 500;

          @include mixins.breakpoint(mobile) {
            font-size: 0.6875rem;
          }
        }
      }
    }

    // Group Level Row (Compact)
    .group-level-row {
      border-top: 1px solid #f1f5f9;
      // padding-top: 0.75rem;
      animation: slideInUp 0.3s ease-out;

      @include mixins.breakpoint(mobile) {
        padding-top: 0.625rem;
      }

      .group-level-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        flex-wrap: wrap;

        .pi-users {
          color: #6366f1;
          font-size: 0.8125rem;

          @include mixins.breakpoint(mobile) {
            font-size: 0.75rem;
          }
        }

        .group-label {
          font-size: 0.75rem;
          font-weight: 600;
          color: #374151;
          margin-right: 0.25rem;

          @include mixins.breakpoint(mobile) {
            font-size: 0.6875rem;
          }
        }

        .level-badges {
          display: flex;
          gap: 0.375rem;
          align-items: center;
          flex-wrap: wrap;

          .level-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.25rem 0.5rem;
            font-size: 0.6875rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.3px;
            color: white;
            border-radius: 8px;
            text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
            min-width: 1.75rem;

            @include mixins.breakpoint(mobile) {
              padding: 0.2rem 0.375rem;
              font-size: 0.625rem;
              border-radius: 6px;
              min-width: 1.5rem;
            }

            // Level-specific compact styling
            &.level-a1 {
              background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            }

            &.level-a2 {
              background: linear-gradient(135deg, #ea580c 0%, #c2410c 100%);
            }

            &.level-b1 {
              background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            }

            &.level-b2 {
              background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            }

            &.level-c1 {
              background: linear-gradient(135deg, #a855f7 0%, #9333ea 100%);
            }

            &.level-c2 {
              background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            }

            &.level-none {
              background: #546a90;
            }

            // Subtle hover effect
            &:hover {
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
            }
          }
        }
      }
    }
  }



  // Professional Search & Controls Section
  .search-controls-section {
    margin-bottom: 1.25rem;

    .search-toolbar {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.75rem 1rem;
      background: #ffffff;
      border: 1px solid #e5e7eb;
      border-radius: 10px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

      @include mixins.breakpoint(mobile) {
        flex-direction: column;
        gap: 0.5rem;
        padding: 0.625rem 0.75rem;
      }
    }

    .search-input-wrapper {
      display: flex;
      align-items: center;
      flex: 1;
      gap: 0.5rem;

      .search-input-container {
        flex: 1;

        .search-input {
          width: 100%;
          padding: 0.625rem 0.75rem 0.625rem 2.5rem;
          border: 1px solid #d1d5db;
          border-radius: 8px;
          font-size: 0.875rem;
          font-weight: 400;
          transition: all 0.2s ease;
          background: #ffffff;

          &:focus {
            outline: none;
            border-color: #6366f1;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
          }

          &::placeholder {
            color: #9ca3af;
            font-weight: 400;
          }
        }

        .pi-search {
          color: #9ca3af;
          font-size: 0.875rem;
        }
      }

      .clear-search-btn {
        width: 2rem;
        height: 2rem;
        min-width: 2rem;
        padding: 0;

        .p-button-icon {
          font-size: 0.75rem;
        }
      }
    }

    .view-controls {
      .view-toggle-btn {
        width: 2.5rem;
        height: 2.5rem;
        min-width: 2.5rem;
        padding: 0;

        .p-button-icon {
          font-size: 0.875rem;
        }

        @include mixins.breakpoint(mobile) {
          width: 2.25rem;
          height: 2.25rem;
          min-width: 2.25rem;
        }
      }
    }

    .no-search-results {
      margin-top: 0.75rem;
      padding: 1rem;
      background: #f9fafb;
      border: 1px solid #e5e7eb;
      border-radius: 8px;

      .no-results-content {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;

        .pi-search {
          color: #9ca3af;
          font-size: 1rem;
        }

        .no-results-text {
          color: #6b7280;
          font-size: 0.875rem;
          font-weight: 500;
        }
      }
    }
  }

  // Loading State - Professional
  .loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    .loading-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.75rem;

      .pi-spin {
        color: #6366f1;
        font-size: 1.5rem;
      }

      span {
        color: #6b7280;
        font-size: 0.875rem;
        font-weight: 500;
      }
    }
  }

  // Students Container - Professional
  .students-container {
    width: 100%;
    max-height: 315px;
    overflow-y: auto;

    &.empty {
      min-height: 250px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    // Empty State - Professional
    .empty-state {
      text-align: center;
      padding: 2.5rem 1.5rem;

      .pi-users {
        color: #9ca3af;
        margin-bottom: 1rem;
      }

      h4 {
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
        font-size: 1.125rem;
      }

      p {
        color: #6b7280;
        font-size: 0.875rem;
        line-height: 1.5;
      }
    }

    // Professional Student List - Enterprise Design
    .professional-student-list {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
      max-height: 600px;
      overflow-y: auto;
      padding: 0.5rem;
      background: #f8fafc;
      border-radius: 12px;
      border: 1px solid #e5e7eb;

      @include mixins.breakpoint(tablet) {
        max-height: 500px;
        gap: 0.625rem;
        padding: 0.375rem;
      }

      @include mixins.breakpoint(mobile) {
        max-height: 450px;
        gap: 0.5rem;
        padding: 0.25rem;
      }

      // Compact View - Enhanced List Style
      &.compact-view {
        gap: 0.5rem;
        max-height: 650px;

        .professional-student-card {
          min-height: 60px;
          padding: 0.75rem 1rem;

          @include mixins.breakpoint(mobile) {
            min-height: 56px;
            padding: 0.625rem 0.75rem;
          }

          .student-profile-section {
            .avatar-container {
              .student-avatar {
                width: 42px;
                height: 42px;

                @include mixins.breakpoint(mobile) {
                  width: 32px;
                  height: 32px;
                }
              }
            }

            .student-information {
              .student-name {
                font-size: 0.8rem;

                @include mixins.breakpoint(mobile) {
                  font-size: 0.75rem;
                }
              }

              .student-meta {
                .language-count {
                  font-size: 0.7rem;

                  @include mixins.breakpoint(mobile) {
                    font-size: 0.65rem;
                  }
                }
              }
            }
          }

          .language-proficiency-section {
            .professional-language-pill {
              padding: 0.25rem 0.5rem;

              .language-name {
                font-size: 0.65rem;
                max-width: 3.5rem;
              }

              .level-indicator {
                font-size: 0.6rem;
                min-width: 1.25rem;
                height: 0.875rem;
              }
            }
          }
        }
      }

      // Custom Scrollbar - Professional
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 3px;

        &:hover {
          background: #94a3b8;
        }
      }
    }

    // Professional Student Card - Enterprise Design
    .professional-student-card {
      position: relative;
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1rem 1.25rem;
      background: #ffffff;
      border: 1px solid #e5e7eb;
      border-radius: 12px;
      transition: all 0.2s ease;
      cursor: pointer;
      min-height: 80px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

      @include mixins.breakpoint(mobile) {
        padding: 0.875rem 1rem;
        gap: 0.75rem;
        min-height: 72px;
      }

      &.selectable:hover {
        border-color: var(--primary-100);
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
      }

      &.selected {
        border-color: var(--green-300);
        background: var(--green-50);
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
      }

      &.disabled {
        opacity: 0.6;
        cursor: not-allowed;
        background: #f8fafc;

        &:hover {
          transform: none;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
          border-color: #e5e7eb;
        }
      }

      // Student Profile Section
      .student-profile-section {
        display: flex;
        align-items: center;
        gap: 0.875rem;
        flex: 1;
        min-width: 0;

        @include mixins.breakpoint(mobile) {
          gap: 0.75rem;
        }

        // Avatar Container with PrimeNG Avatar
        .avatar-container {
          position: relative;
          flex-shrink: 0;
          display: flex;
          align-items: center;
          justify-content: center;

          .student-avatar {
            width: 48px;
            height: 48px;

            @include mixins.breakpoint(mobile) {
              width: 42px;
              height: 42px;
            }
          }

          // Selection Indicator - Professional
          .selection-indicator {
            position: absolute;
            top: -3px;
            right: -3px;
            width: 18px;
            height: 18px;
            background: #10b981;
            border: 2px solid white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

            @include mixins.breakpoint(mobile) {
              width: 16px;
              height: 16px;
              top: -2px;
              right: -2px;
            }

            &.visible {
              opacity: 1;
              transform: scale(1);
            }

            i {
              color: white;
              font-size: 0.5rem;
              font-weight: 700;

              @include mixins.breakpoint(mobile) {
                font-size: 0.45rem;
              }
            }
          }
        }

        // Student Information
        .student-information {
          flex: 1;
          min-width: 0;

          .student-name-section {
            .student-name {
              margin: 0 0 0.25rem 0;
              font-size: 0.9375rem;
              font-weight: 600;
              color: #1f2937;
              line-height: 1.2;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;

              @include mixins.breakpoint(mobile) {
                font-size: 0.875rem;
                margin: 0 0 0.2rem 0;
              }
            }

            .student-meta {
              .language-count {
                font-size: 0.75rem;
                color: #6b7280;
                font-weight: 500;

                @include mixins.breakpoint(mobile) {
                  font-size: 0.7rem;
                }
              }
            }
          }
        }
      }

      // Language Proficiency Section - Professional
      .language-proficiency-section {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        align-items: center;
        max-width: 100%;

        @include mixins.breakpoint(mobile) {
          gap: 0.375rem;
        }

        // Language Summary Container for multiple languages
        .language-summary-container {
          display: flex;
          align-items: center;
          gap: 0.375rem;
          max-width: 100%;
        }

        // Mixed Language Display (3 pills + compact indicator)
        .mixed-language-display {
          display: flex;
          align-items: center;
          gap: 0.375rem;
          flex-wrap: wrap;
          max-width: 100%;

          @include mixins.breakpoint(mobile) {
            gap: 0.3125rem;
          }
        }

        // Additional Languages Indicator
        .additional-languages-indicator {
          display: inline-flex;
          align-items: center;
          background: rgba(99, 102, 241, 0.1);
          border: 1px solid rgba(99, 102, 241, 0.2);
          border-radius: 12px;
          padding: 0.25rem 0.5rem;
          cursor: pointer;
          transition: all 0.2s ease;
          font-size: 0.6875rem;
          font-weight: 500;
          color: #6366f1;
          gap: 0.25rem;
          min-width: fit-content;
          flex-shrink: 0;

          @include mixins.breakpoint(mobile) {
            padding: 0.1875rem 0.375rem;
            border-radius: 10px;
            font-size: 0.625rem;
          }

          .plus-indicator {
            font-weight: 600;
            line-height: 1;
          }

          .expand-icon {
            font-size: 0.625rem;
            transition: transform 0.2s ease;
          }

          .info-icon {
            font-size: 0.625rem;
            opacity: 0.7;
            transition: opacity 0.2s ease;
          }

          &:hover {
            background: rgba(99, 102, 241, 0.15);
            border-color: rgba(99, 102, 241, 0.3);

            .expand-icon {
              transform: rotate(180deg);
            }

            .info-icon {
              opacity: 1;
            }
          }
        }

        // Professional Language Pills
        .professional-language-pill {
          display: inline-flex;
          align-items: center;
          background: rgba(255, 255, 255, 0.9);
          border: 1px solid #e5e7eb;
          border-radius: 6px;
          padding: 0.375rem 0.75rem;
          transition: all 0.2s ease;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
          flex-shrink: 0;

          @include mixins.breakpoint(mobile) {
            padding: 0.3125rem 0.625rem;
            border-radius: 14px;
          }

          // Compact variant for two languages
          &.compact {
            padding: 0.3125rem 0.625rem;

            @include mixins.breakpoint(mobile) {
              padding: 0.25rem 0.5rem;
              border-radius: 12px;
            }

            .language-name {
              font-size: 0.6875rem;
              max-width: 3.5rem;

              @include mixins.breakpoint(mobile) {
                font-size: 0.625rem;
                max-width: 3rem;
              }
            }

            .level-indicator {
              font-size: 0.5625rem;
              min-width: 1.25rem;
              height: 1rem;

              @include mixins.breakpoint(mobile) {
                font-size: 0.5rem;
                min-width: 1.125rem;
                height: 0.875rem;
              }
            }
          }

          // Primary variant for multiple languages (first language)
          &.primary {
            border-width: 2px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
          }

          .language-name {
            font-size: 0.75rem;
            font-weight: 500;
            color: #374151;
            margin-right: 0.375rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 5rem;

            @include mixins.breakpoint(mobile) {
              font-size: 0.6875rem;
              margin-right: 0.3125rem;
              max-width: 4rem;
            }
          }

          .level-indicator {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 1.5rem;
            height: 1.125rem;
            padding: 0 0.25rem;
            border-radius: 8px;
            font-size: 0.625rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            line-height: 1;
            color: white;
            text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);

            @include mixins.breakpoint(mobile) {
              font-size: 0.5625rem;
              min-width: 1.375rem;
              height: 1rem;
              border-radius: 6px;
            }
          }

          // Level-specific professional styling
          &.level-a1 {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-color: #f59e0b;

            .level-indicator {
              background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            }
          }

          &.level-a2 {
            background: linear-gradient(135deg, #fed7aa 0%, #fdba74 100%);
            border-color: #ea580c;

            .level-indicator {
              background: linear-gradient(135deg, #ea580c 0%, #c2410c 100%);
            }
          }

          &.level-b1 {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border-color: #3b82f6;

            .level-indicator {
              background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            }
          }

          &.level-b2 {
            background: linear-gradient(135deg, #bfdbfe 0%, #93c5fd 100%);
            border-color: #2563eb;

            .level-indicator {
              background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            }
          }

          &.level-c1 {
            background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
            border-color: #a855f7;

            .level-indicator {
              background: linear-gradient(135deg, #a855f7 0%, #9333ea 100%);
            }
          }

          &.level-c2 {
            background: linear-gradient(135deg, #ede9fe 0%, #ddd6fe 100%);
            border-color: #8b5cf6;

            .level-indicator {
              background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            }
          }

          &.level-none,
          &.level-default,
          &.no-languages {
            background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
            border-color: #d1d5db;

            .level-indicator {
              background: #546a90;
            }
          }

          // Hover effects for professional feel
          &:hover {
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);

            .level-indicator {}
          }
        }
      }

      // Disabled Overlay - Professional
      .disabled-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(248, 250, 252, 0.8);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(2px);

        .disabled-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 0.25rem;

          .pi-ban {
            color: #9ca3af;
            font-size: 1.125rem;
          }

          .disabled-text {
            color: #6b7280;
            font-size: 0.75rem;
            font-weight: 500;
          }
        }
      }
    }
  }
}

// Professional Animations
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(8px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Custom tooltip styling for language details
:host ::ng-deep .language-tooltip {
  .p-tooltip-text {
    background: rgba(30, 41, 59, 0.95) !important;
    color: white !important;
    border-radius: 8px !important;
    padding: 0.75rem 1rem !important;
    font-size: 0.75rem !important;
    line-height: 1.4 !important;
    white-space: pre-line !important;
    max-width: 200px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    backdrop-filter: blur(8px) !important;
  }

  .p-tooltip-arrow {
    border-top-color: rgba(30, 41, 59, 0.95) !important;
  }
}