// ============================================================================
// STUDENTS LIST COMPONENT - Displays and manages student data in a data grid
// ============================================================================

import { CommonModule } from '@angular/common';
import {
  Component,
  OnInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  inject,
  OnDestroy,
  signal,
  computed,
  ViewChild,
  Injector
} from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { takeUntil, combineLatest } from 'rxjs';
import { toObservable } from '@angular/core/rxjs-interop';

// === PRIMENG IMPORTS ===
import { TableModule } from 'primeng/table';
import { MultiSelectModule } from 'primeng/multiselect';
import { ButtonModule } from "primeng/button";
import { InputTextModule } from 'primeng/inputtext';
import { TooltipModule } from 'primeng/tooltip';
import { RippleModule } from 'primeng/ripple';
import { TagModule } from 'primeng/tag';
import { FormsModule } from '@angular/forms';

// === SHARED LIBRARY IMPORTS ===
import {
  IGetStudentsRequest,
  IGetStudentsResponse,
  ITeachingLanguageDto,
  nameOf,
  IGetAllTeachingLanguagesResponse,
  TeachingLanguagesRoutes,
  IStudents,
  IDataGridFields,
  IBasedDataGridRequest,
  FiltersDrawerSidebarComponent,
  IFiltersDrawerConfig,
  AppliedFiltersTagsComponent,
  IAppliedFilterTag,
  IFilterTagRemoveEvent,
  ILanguageLevelsEnum,
  IEnumDropdownOptions,
  EnumDropdownOptionsService,
  ISearchStudentDto,
  PrimeProfilePhotoSingleComponent
} from 'SharedModules.Library';

// === COMPONENT IMPORTS ===
import { DataGridHeaderFooterComponent } from '../../../shared/components/data-grid-header-footer/data-grid-header-footer.component';

// === SERVICE IMPORTS ===
import { StudentListComponentHelperService } from '../../../shared/services/student-list-component-helper.service';

// === BASE COMPONENT ===
import { BaseDataGridComponent, IBaseDataGridConfig } from 'SharedModules.Library';
import { IStudentsFilterActionEvent, IStudentsFilterChangeEvent, StudentsListFiltersComponent } from './students-list-filters/students-list-filters.component';

// ============================================================================
// COMPONENT DEFINITION
// ============================================================================

/**
 * Students List Component
 *
 * Displays students in a data grid with:
 * - Pagination and sorting
 * - Advanced filtering (ages, languages, account status, etc.)
 * - Applied filters display
 * - Search functionality
 * - Export capabilities
 *
 * Extends BaseDataGridComponent for common data grid functionality
 */
@Component({
  selector: 'app-students-list',
  standalone: true,
  imports: [
    CommonModule,
    TableModule,
    MultiSelectModule,
    ButtonModule,
    FormsModule,
    InputTextModule,
    TooltipModule,
    RippleModule,
    TagModule,
    DataGridHeaderFooterComponent,
    AppliedFiltersTagsComponent,
    StudentsListFiltersComponent,
    FiltersDrawerSidebarComponent,
    PrimeProfilePhotoSingleComponent,
  ],
  templateUrl: './students-list.component.html',
  styleUrls: ['./students-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class StudentsListComponent
  extends BaseDataGridComponent<IGetStudentsRequest, IGetStudentsResponse>
  implements OnInit, OnDestroy {

  // ============================================================================
  // DEPENDENCY INJECTION
  // ============================================================================

  private readonly studentHelperService = inject(StudentListComponentHelperService);
  private readonly enumDropdownOptionsService = inject(EnumDropdownOptionsService);
  private readonly injector = inject(Injector);
  protected override readonly cdr = inject(ChangeDetectorRef);

  // ============================================================================
  // COMPONENT REFERENCES
  // ============================================================================

  @ViewChild('studentsFiltersComponent') studentsFiltersComponent?: StudentsListFiltersComponent;

  // ============================================================================
  // CONFIGURATION
  // ============================================================================

  /** Configuration for the base data grid component */
  override getConfig(): IBaseDataGridConfig<IGetStudentsRequest> {
    return {
      apiEndpoint: IStudents.getStudents,
      defaultRequest: this.studentHelperService.createDefaultRequest(),
      mapUrlParamsToRequest: (params) => this.studentHelperService.mapQueryParamsToStudentsRequest(params),
      errorPrefix: 'Failed to load students',
      appliedFiltersConfig: {
        convertToFilterTags: (request, urlParams) => this.convertToFilterTags(request, urlParams),
        getFiltersCount: (filters) => filters.length
      }
    };
  }

  /** Field names for the request object */
  readonly getStudentsRequestFieldNames = nameOf<IGetStudentsRequest>();

  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================

  /** Teaching languages for filtering */
  teachingLanguages = signal<ITeachingLanguageDto[]>([]);

  /** Component initialization flag */
  private componentInitializedFlag = false;
  
  // Expansion state management for PrimeNG table
  expandedRows = signal<{ [key: string]: boolean }>({});

  // ============================================================================
  // COMPUTED PROPERTIES
  // ============================================================================



  /** Data for the table display */
  studentsForTable = computed(() => {
    return this.dataResponse()?.pageData || [];
  });

  /** Visible columns (excludes hidden columns) */
  visibleColumns = computed(() => this.cols.filter((col) => !col.hide));

  // ============================================================================
  // TABLE CONFIGURATION
  // ============================================================================

  /** Column definitions for the students table */
  override cols: IDataGridFields[] = this.studentHelperService.initializeTableColumns();

  // ============================================================================
  // FILTERS CONFIGURATION
  // ============================================================================

  /** Configuration for the filters drawer */
  filtersConfig: IFiltersDrawerConfig = {
    headerText: 'Filter Students',
    headerIcon: 'pi pi-filter',
    position: 'right',
    width: '400px',
    showApplyButton: true,
    showResetButton: true,
    showCloseButton: true,
    applyButtonLabel: 'Apply Filters',
    resetButtonLabel: 'Reset All',
    closeButtonLabel: 'Close',
    applyButtonIcon: 'pi pi-check',
    resetButtonIcon: 'pi pi-refresh',
    closeButtonIcon: 'pi pi-times'
  };

  // ============================================================================
  // LIFECYCLE METHODS
  // ============================================================================

  override ngOnInit(): void {
    // Filter out hidden columns for initial selection
    const visibleColumns = this.cols.filter(col => !col.hide);
    this.selectedColumns.set(visibleColumns);

    super.ngOnInit();
    this.loadTeachingLanguages();
    this.setupLanguageSubscription();
    this.componentInitializedFlag = true;
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
  }

  // ============================================================================
  // DATA LOADING
  // ============================================================================

  /**
   * Load teaching languages for filtering
   */
  private loadTeachingLanguages(): void {
    this.handleApiService.getApiData<IGetAllTeachingLanguagesResponse>({
      url: TeachingLanguagesRoutes.getAllTeachingLanguages,
      method: 'GET'
    }).pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.teachingLanguages.set(response?.teachingLanguages || []);
        },
        error: (error) => {
          console.error('Failed to load teaching languages:', error);
        }
      });
  }

  /**
   * Set up language subscription to update applied filters when languages are loaded
   */
  private setupLanguageSubscription(): void {
    combineLatest([toObservable(this.teachingLanguages, { injector: this.injector })])
      .pipe(takeUntil(this.destroy$))
      .subscribe(([teachingLanguages]) => {
        if (
          teachingLanguages.length > 0 &&
          Object.keys(this.currentUrlParams()).length > 0 &&
          this.componentInitializedFlag
        ) {
          this.updateAppliedFilters();
        }
      });
  }

  // Override the base class method to handle date formatting for API calls
  protected override cleanRequestForApi<T extends IBasedDataGridRequest>(request: T): Partial<T> {
    return this.studentHelperService.cleanRequestForApi(request as unknown as IGetStudentsRequest) as Partial<T>;
  }

  // ============================================================================
  // FILTER HANDLING
  // ============================================================================



  // ============================================================================
  // UTILITY METHODS
  // ============================================================================



  // ============================================================================
  // DATA GRID OVERRIDES
  // ============================================================================

  override removeFilter(filterName: string, event?: MouseEvent): void {
    if (event) event.stopPropagation();

    const fieldNames = nameOf<IGetStudentsRequest>();

    // Handle special filter removals
    if (filterName === fieldNames.teachingLanguage!) {
      // When removing teaching language, also clear teaching language level
      this.queryParams.update(current => {
        const updated = { ...current };
        delete updated[fieldNames.teachingLanguage!];
        delete updated[fieldNames.teachingLanguageLevel!];
        return updated;
      });
    } else if (filterName === fieldNames.sortColumn!) {
      // When removing sort filter, reset to default sort
      this.queryParams.update(current => ({
        ...current,
        sortColumn: StudentListComponentHelperService.DEFAULT_SORT_COLUMN,
        sortDirection: StudentListComponentHelperService.DEFAULT_SORT_DIRECTION,
        pageNumber: 1
      }));
    } else {
      // Standard filter removal
      this.queryParams.update(current => {
        const updated = { ...current };
        delete updated[filterName as keyof IGetStudentsRequest];
        return updated;
      });
    }

    // Update URL and reload data
    this.generalService.updateQueryParams(this.queryParams(), {
      replaceUrl: true,
    });
  }

  override resetFilters(): void {
    this.saveScrollPosition();

    // Reset to default values using helper service
    this.queryParams.set(this.studentHelperService.createDefaultRequest());

    // No need to manually reset UI state - computed signals will automatically update when queryParams change

    this.generalService.updateQueryParams(this.queryParams(), {
      preserveFragment: true,
      queryParamsHandling: '',
      replaceUrl: true
    });

    // BaseDataGrid will automatically fetch data when URL changes
    this.restoreScrollPosition();
  }

  // ============================================================================
  // APPLIED FILTERS
  // ============================================================================


  hasFilterInUrl(paramName: string): boolean {
    const value = (this.currentUrlParams as any)[paramName];
    return !!value && value !== '0' && value !== 'false';
  }

  /**
   * Check if sort filter is different from default
   */
  private hasSortFilter(request: IGetStudentsRequest): boolean {
    const defaultColumn = StudentListComponentHelperService.DEFAULT_SORT_COLUMN;
    const defaultDirection = StudentListComponentHelperService.DEFAULT_SORT_DIRECTION;

    return !!(request.sortColumn &&
      (request.sortColumn !== defaultColumn || request.sortDirection !== defaultDirection));
  }

  /**
   * Get display name for current sort column
   */
  getSortColumnDisplayName(): string {
    const sortColumn = this.queryParams().sortColumn;
    const column = this.cols.find(col => col.field === sortColumn);
    return column ? column.header : sortColumn || 'Unknown';
  }

  /**
   * Get display name for sort column from URL
   */
  getSortColumnDisplayNameFromUrl(): string {
    const sortColumn = this.currentUrlParams()[this.getStudentsRequestFieldNames.sortColumn!];
    const column = this.cols.find(col => col.field === sortColumn);
    return column ? column.header : sortColumn || 'Unknown';
  }

  /**
   * Check if sort filter exists in URL
   */
  hasSortFilterInUrl(): boolean {
    const sortColumn = this.currentUrlParams()[this.getStudentsRequestFieldNames.sortColumn!];
    return !!(sortColumn && sortColumn !== StudentListComponentHelperService.DEFAULT_SORT_COLUMN);
  }

  /**
   * Convert request and URL params to filter tags for display
   */
  private convertToFilterTags(request: IGetStudentsRequest, _urlParams: any): IAppliedFilterTag[] {
    const filters: IAppliedFilterTag[] = [];
    const fieldNames = nameOf<IGetStudentsRequest>();

    // Sort Column Filter - check if different from default
    if (this.hasSortFilter(request)) {
      const displayName = this.getSortColumnDisplayName();
      filters.push({
        id: fieldNames.sortColumn!,
        label: `Sort: ${displayName} (${request.sortDirection === 'asc' ? 'Ascending' : 'Descending'})`,
        icon: 'pi pi-sort-alt',
        type: 'sort',
        removeData: { filterName: fieldNames.sortColumn! }
      });
    }

    // Search Term Filter
    if (request.searchTerm) {
      filters.push({
        id: fieldNames.searchTerm!,
        label: `Search: "${request.searchTerm}"`,
        icon: 'pi pi-search',
        type: 'search',
        removeData: { filterName: fieldNames.searchTerm! }
      });
    }

    // Teaching Language Filter
    if (request.teachingLanguage) {
      const language = this.teachingLanguages().find(lang => lang.id === request.teachingLanguage);
      filters.push({
        id: fieldNames.teachingLanguage!,
        label: language ? `Learning Language: ${language.name}` : `Learning Language: ${request.teachingLanguage}`,
        icon: 'pi pi-globe',
        type: 'select',
        removeData: { filterName: fieldNames.teachingLanguage! }
      });
    }

    // Teaching Language Level Filter
    if (request.teachingLanguageLevel) {
      const levelDisplay = this.formatLanguageLevel(request.teachingLanguageLevel);
      filters.push({
        id: fieldNames.teachingLanguageLevel!,
        label: `Level: ${levelDisplay}`,
        icon: 'pi pi-star',
        type: 'select',
        removeData: { filterName: fieldNames.teachingLanguageLevel! }
      });
    }

    // Account Status Filter
    if (request.accountStatus !== null && request.accountStatus !== undefined) {
      filters.push({
        id: fieldNames.accountStatus!,
        label: `Status: ${this.getAccountStatusText(request.accountStatus)}`,
        icon: 'pi pi-user',
        type: 'select',
        removeData: { filterName: fieldNames.accountStatus! }
      });
    }

    // Include Blocked Filter
    if (request.includeBlocked === true) {
      filters.push({
        id: fieldNames.includeBlocked!,
        label: 'Include Blocked: Yes',
        icon: 'pi pi-ban',
        type: 'boolean',
        removeData: { filterName: fieldNames.includeBlocked! }
      });
    }

    return filters;
  }

  // ============================================================================
  // MISSING METHODS FROM TEMPLATE
  // ============================================================================

  formatLanguageLevel(level: ILanguageLevelsEnum): string {
    const levelOptions = this.enumDropdownOptionsService.languageLevelsOptions;
    const option = levelOptions.find(
      (opt: IEnumDropdownOptions) => opt.value === level
    );
    return option?.label || level?.toString() || '';
  }

  /**
   * Handle applied filter removal
   */
  override onAppliedFilterRemove(event: IFilterTagRemoveEvent): void {
    this.removeFilter(event.filter.removeData.filterName, event.event);
  }

  /**
   * Handle clear all applied filters
   */
  override onAppliedFiltersClearAll(_event: MouseEvent): void {
    this.resetFilters();
  }

  /**
   * Handle search input changes
   */
  override onSearchChange(value: string): void {
    this.queryParams.update(current => ({
      ...current,
      searchTerm: value || null,
      pageNumber: 1 // Reset to first page when searching
    }));

    this.generalService.updateQueryParams(this.queryParams(), {
      replaceUrl: true,
    });
  }

  /**
   * Handle filter changes from the filters component
   */
  override onFilterChange(event: IStudentsFilterChangeEvent): void {
    // With temporary state management, filter changes are stored in the filters component
    // and only applied when the user clicks "Apply" or "Reset"
    // This method is kept for backward compatibility but no longer immediately applies changes
    console.debug('Filter change detected:', event.filterName, event.value);
  }

  /**
   * Handle filter actions (apply/reset) from the filters component
   */
  override onFilterAction(event: IStudentsFilterActionEvent): void {
    if (event.action === 'search') {
      this.saveScrollPosition();
      this.queryParams.set(event.filters);
      this.generalService.updateQueryParams(this.queryParams(), { replaceUrl: true });
      this.restoreScrollPosition();
    } else if (event.action === 'reset') {
      this.resetFilters();
    }
  }

  /**
   * Handle filters drawer action events
   */
  override onFiltersDrawerAction(event: any): void {
    switch (event.action) {
      case 'apply':
        // Apply temporary filters from the filters component
        if (this.studentsFiltersComponent) {
          const filters = this.studentsFiltersComponent.getCurrentFilters();
          this.onFilterAction({ action: 'search', filters });
        }
        this.closeFiltersDrawer();
        break;

      case 'reset':
        // Reset only clears temporary filter state, doesn't apply the reset
        // The reset will only be applied when user clicks "Apply Filters"
        if (this.studentsFiltersComponent) {
          this.studentsFiltersComponent.resetFiltersToDefault();
        }
        // Do NOT close the drawer - user should be able to see the reset state
        // and decide whether to apply it or make further changes
        break;

      case 'close':
        // Check if there are pending changes and handle accordingly
        if (this.studentsFiltersComponent?.hasPendingChanges()) {
          // Discard temporary changes when closing without applying
          this.studentsFiltersComponent.discardTempChanges();
        }
        this.closeFiltersDrawer();
        break;
    }
  }

  /**
   * Toggle filters drawer visibility
   */
  override toggleFiltersDrawer(): void {
    this.isFiltersDrawerVisible.update(current => !current);
  }

  /**
   * Handle filters drawer visibility change
   */
  onFiltersDrawerVisibilityChange(visible: boolean): void {
    this.isFiltersDrawerVisible.set(visible);
  }

  /**
   * Export table data to Excel
   */
  exportTable(): void {
    const response = this.dataResponse();
    if (response) {
      this.studentHelperService.exportStudentTable(this.table, this.selectedColumns(), response);
    }
  }

  /**
   * Make resetAllFilters method public for template access
   */
  public resetFiltersComponent(): void {
    if (this.studentsFiltersComponent) {
      // Access the protected method through a public wrapper
      (this.studentsFiltersComponent as any).resetAllFilters();
    }
  }

  // ============================================================================
  // DISPLAY HELPER METHODS
  // ============================================================================

  /**
   * Get display text for gender enum
   */
  getGenderDisplayText(gender?: number): string {
    return this.studentHelperService.getGenderDisplayText(gender || 0);
  }

  /**
   * Get display text for account status
   */
  getAccountStatusText(status: number): string {
    return this.studentHelperService.getAccountStatusDisplayText(status);
  }

  /**
   * Get display text for blocked status
   */
  getBlockedStatusText(isBlocked: boolean): string {
    return isBlocked ? 'Blocked' : 'Active';
  }

  /**
   * Format date for display
   */
  formatDate(date?: Date | string): string {
    if (!date) return '';
    return this.studentHelperService.formatUtcDateToAdminLocalized(date);
  }

  
    // ============================================================================
    // ROW EXPANSION METHODS
    // ============================================================================
  
    /**
     * Handle row expand event
     */
    onRowExpand(event: any): void {
      console.log('🔽 Teachers List - onRowExpand called:', event);
      const data: ISearchStudentDto = event.data as ISearchStudentDto;
      const teacherId = data.userId;
      console.log('🔽 Expanding teacher ID:', teacherId);
      this.expandedRows.update((rows) => {
        const newRows = {
          ...rows,
          [teacherId]: true,
        };
        console.log('🔽 Updated expandedRows:', newRows);
        return newRows;
      });
    }
  
    /**
     * Handle row collapse event
     */
    onRowCollapse(event: any): void {
      console.log('🔼 Teachers List - onRowCollapse called:', event);
      const teacherId = event.data.id;
      console.log('🔼 Collapsing teacher ID:', teacherId);
      this.expandedRows.update((rows) => {
        const newRows = { ...rows };
        delete newRows[teacherId];
        console.log('🔼 Updated expandedRows:', newRows);
        return newRows;
      });
    }
}
