@use "mixins";

:host {
  display: block;
}

.left-login {

  width: 100%;

  @include mixins.breakpoint(large) {
    width: calc(100% - 415px);
  }
  @include mixins.breakpoint(xxlarge) {
    // padding-left: 8rem !important;
    // padding-right: 8rem !important;
    
  &.no-padding {
    padding: 0 !important;
  }
  }

  &.no-padding {
    padding: 0 !important;
  }
}

.only-left-side {
  width: 100%;
}

.bg-graphic {
  background-image: url(/assets/images/graphic/formify-bg-21.svg);
  background-size: cover;
  background-repeat: no-repeat;
  /* height: 100%; */
  background-position: 100% 90%;

  width: 100%;

  @include mixins.breakpoint(large) {
    width: 415px;
  }
}