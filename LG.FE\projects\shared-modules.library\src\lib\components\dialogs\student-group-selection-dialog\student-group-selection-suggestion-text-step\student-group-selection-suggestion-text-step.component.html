<div class="benefits-container">
  <!-- Hero Section with Planet Graphic -->
  <div class="hero-section">
    <div class="hero-content">
      <div class="hero-visual">
        <div class="solar-container">
          <div class="solar-system">
            <div class="central-planet">
              <div class="planet-core"></div>
              <div class="planet-glow"></div>
            </div>
            <div class="orbit-ring orbit-1">
              <div class="orbiting-planet planet-1"></div>
            </div>
            <div class="orbit-ring orbit-2">
              <div class="orbiting-planet planet-2"></div>
            </div>
            <div class="orbit-ring orbit-3">
              <div class="orbiting-planet planet-3"></div>
            </div>
          </div>
        </div>
      </div>

      <div class="hero-text">
        <h1 class="hero-title">Create Your Learning Universe</h1>
        <p class="hero-subtitle">Connect students in collaborative group classes and unlock better learning outcomes</p>
      </div>
    </div>
  </div>

  <!-- Benefits Section -->
  <div class="benefits-section">
    <h2 class="benefits-title">Why Choose Group Classes?</h2>

    <div class="benefits-grid">
      <div class="benefit-card">
        <div class="benefit-icon">
          <i class="pi pi-percentage"></i>
        </div>
        <div class="benefit-content">
          <h3 class="benefit-title">Reduced Pricing</h3>
          <p class="benefit-desc">Save up to 30% on lesson costs when students share the experience</p>
        </div>
      </div>

      <div class="benefit-card">
        <div class="benefit-icon">
          <i class="pi pi-users"></i>
        </div>
        <div class="benefit-content">
          <h3 class="benefit-title">Collaborative Learning</h3>
          <p class="benefit-desc">Students learn from each other, building confidence and social skills</p>
        </div>
      </div>

      <div class="benefit-card">
        <div class="benefit-icon">
          <i class="pi pi-chart-line"></i>
        </div>
        <div class="benefit-content">
          <h3 class="benefit-title">Enhanced Engagement</h3>
          <p class="benefit-desc">Interactive group dynamics keep students motivated and focused</p>
        </div>
      </div>

      <div class="benefit-card">
        <div class="benefit-icon">
          <i class="pi pi-clock"></i>
        </div>
        <div class="benefit-content">
          <h3 class="benefit-title">Flexible Scheduling</h3>
          <p class="benefit-desc">Coordinate schedules that work for multiple families</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Tips Section -->
  <!-- <div class="tips-section">
    <h2 class="tips-title">Tips for Successful Group Classes</h2>

    <div class="tips-list">
      <div class="tip-item">
        <div class="tip-icon">
          <i class="pi pi-graduation-cap"></i>
        </div>
        <div class="tip-content">
          <p class="tip-title">Match Language Levels</p>
          <p class="tip-desc">Ensure all students are at similar proficiency levels for optimal learning</p>
        </div>
      </div>

      <div class="tip-item">
        <div class="tip-icon">
          <i class="pi pi-calendar"></i>
        </div>
        <div class="tip-content">
          <p class="tip-title">Consider Age Groups</p>
          <p class="tip-desc">Group students within 2-3 years of age for better social dynamics</p>
        </div>
      </div>

      <div class="tip-item">
        <div class="tip-icon">
          <i class="pi pi-check-circle"></i>
        </div>
        <div class="tip-content">
          <p class="tip-title">Align Availability</p>
          <p class="tip-desc">Choose students with overlapping schedules for consistent attendance</p>
        </div>
      </div>
    </div>
  </div> -->

  @if (!isDialogPopup()) {

  <!-- Selection Prompt -->
  <div class="selection-section">
    <div class="selection-content">
      <div class="selection-header">
        <h3 class="selection-title">Ready to Create Your Group?</h3>
        <p class="selection-subtitle">Let's find compatible students who match your preferences and schedule</p>
      </div>

      <div class="selection-action">
        <p-button (click)="onItemClicked()" label="Proceed to Group Creation" icon="pi pi-users" iconPos="left"
          styleClass="selection-button">
        </p-button>

        <div class="selection-note">
          <i class="pi pi-info-circle"></i>
          <span>We'll show you the best matches based on level, age, and availability</span>
        </div>
      </div>
    </div>
  </div>
  }

</div>