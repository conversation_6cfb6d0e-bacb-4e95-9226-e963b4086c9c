import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  Output,
  EventEmitter,
  computed,
  ChangeDetectionStrategy,
  OnInit,
  OnChanges,
  signal,
  inject
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { DropdownModule } from 'primeng/dropdown';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { CheckboxModule } from 'primeng/checkbox';
import {
  IGetPackagesRequest,
  ITeachingLanguageDto,
  BaseDataGridFiltersComponent,
  IFilterChangeEvent,
  IFilterActionEvent,
  IBaseFilterState,
  nameOf,
  DateRangeFilterService,
  TimezoneService,
  AuthStateService,
  IBasicProfileInfoDto
} from 'SharedModules.Library';

export type IOverviewPackagesFilterChangeEvent = IFilterChangeEvent<IGetPackagesRequest>;

export type IOverviewPackagesFilterActionEvent = IFilterActionEvent<IGetPackagesRequest>;

/**
 * Interface for filter state data passed from parent
 */
export interface IOverviewPackagesFilterState extends IBaseFilterState<IGetPackagesRequest> {
  queryParams: IGetPackagesRequest;
  teachingLanguages: ITeachingLanguageDto[];
  isFilterOpen: boolean;
}

/**
 * Interface for filter configuration
 */
export interface IOverviewPackagesFilterConfig {
  showToggleButton?: boolean;
  defaultOpen?: boolean;
  enableAutoSearch?: boolean;
  searchDebounceMs?: number;
}

@Component({
  selector: 'app-overview-packages-filters',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    DropdownModule,
    ButtonModule,
    CalendarModule,
    CheckboxModule
  ],
  templateUrl: './overview-packages-filters.component.html',
  styleUrl: './overview-packages-filters.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class OverviewPackagesFiltersComponent extends BaseDataGridFiltersComponent<
  IGetPackagesRequest,
  IOverviewPackagesFilterState,
  IOverviewPackagesFilterConfig
> implements OnInit, OnChanges {

  // ============================================================================
  // SERVICES
  // ============================================================================

  readonly dateRangeFilterService = inject(DateRangeFilterService);
  readonly timezoneService = inject(TimezoneService);
  readonly authService = inject(AuthStateService);

  // ============================================================================
  // INPUTS & OUTPUTS
  // ============================================================================

  /**
   * Filter state data from parent component
   */
  @Input() override filterState: IOverviewPackagesFilterState = {
    queryParams: {} as IGetPackagesRequest,
    teachingLanguages: [],
    isFilterOpen: true
  };

  /**
   * Filter configuration
   */
  @Input() override config: IOverviewPackagesFilterConfig = {
    showToggleButton: true,
    defaultOpen: true,
    enableAutoSearch: false,
    searchDebounceMs: 400
  };

  @Output() override filterChanged = new EventEmitter<IOverviewPackagesFilterChangeEvent>();
  @Output() override filterAction = new EventEmitter<IOverviewPackagesFilterActionEvent>();

  // ============================================================================
  // SIGNALS & STATE
  // ============================================================================

  // Reset signals for child components
  readonly resetSelectionSignal = signal(false);
  readonly isResetting = signal(false);

  // Computed properties that merge temp + current state
  override readonly currentFilters = computed(() => ({
    ...this.filterState.queryParams,
    ...this._tempFilters()
  }));

  // ============================================================================
  // PRIVATE PROPERTIES
  // ============================================================================

  // Field names for type safety
  private readonly fieldNames = nameOf<IGetPackagesRequest>();

  // User basic info for timezone handling
  private readonly userBasicInfo = this.authService.getUserBasicInfo() as IBasicProfileInfoDto;

  // ============================================================================
  // LIFECYCLE METHODS
  // ============================================================================

  override ngOnInit(): void {
    console.debug('Overview Packages Filters Component initialized');
    super.ngOnInit();
    this.timezoneService.setTimezone(this.userBasicInfo.timeZoneIana!);
  }

  override ngOnChanges(): void {
    super.ngOnChanges();
  }

  // ============================================================================
  // FILTER CHANGE HANDLERS
  // ============================================================================

  onTeachingLanguageChange(value: string | null): void {
    this._tempFilters.update(current => ({
      ...current,
      [this.fieldNames.teachingLanguageId!]: value
    }));
  }

  onPurchasedFromChange(value: Date | string | null): void {
    const convertedDate = this.dateRangeFilterService.convertDateFromToUtc(value);
    this._tempFilters.update(current => ({
      ...current,
      [this.fieldNames.purchasedFrom!]: convertedDate ? new Date(convertedDate) : null
    }));
  }

  onPurchasedToChange(value: Date | string | null): void {
    const convertedDate = this.dateRangeFilterService.convertDateToToUtc(value);
    this._tempFilters.update(current => ({
      ...current,
      [this.fieldNames.purchasedTo!]: convertedDate ? new Date(convertedDate) : null
    }));
  }

  onExpiresFromChange(value: Date | string | null): void {
    const convertedDate = this.dateRangeFilterService.convertDateToToUtc(value);
    this._tempFilters.update(current => ({
      ...current,
      [this.fieldNames.expiresFrom!]: convertedDate ? new Date(convertedDate) : null
    }));
  }

  onExpiresToChange(value: Date | string | null): void {
    const convertedDate = this.dateRangeFilterService.convertDateToToUtc(value);
    this._tempFilters.update(current => ({
      ...current,
      [this.fieldNames.expiresTo!]: convertedDate ? new Date(convertedDate) : null
    }));
  }

  onHasAddOnExtensionChange(value: boolean | null): void {
    this._tempFilters.update(current => ({
      ...current,
      [this.fieldNames.hasAddOnExtension!]: value
    }));
  }

  // ============================================================================
  // DATE RANGE HANDLERS
  // ============================================================================

  /**
   * Handles purchased date range changes (both from and to) with timezone conversion
   */
  onPurchasedDateRangeChange(dateFrom: Date | string | null, dateTo: Date | string | null): void {
    const convertedRange = this.dateRangeFilterService.convertDateRangeToUtc({
      dateFrom,
      dateTo
    });

    this._tempFilters.update(current => ({
      ...current,
      [this.fieldNames.purchasedFrom!]: convertedRange.dateFrom ? new Date(convertedRange.dateFrom) : null,
      [this.fieldNames.purchasedTo!]: convertedRange.dateTo ? new Date(convertedRange.dateTo) : null
    }));
  }

  /**
   * Handles expires date range changes (both from and to) with timezone conversion
   */
  onExpiresDateRangeChange(dateFrom: Date | string | null, dateTo: Date | string | null): void {
    const convertedRange = this.dateRangeFilterService.convertDateRangeToUtc({
      dateFrom,
      dateTo
    });

    this._tempFilters.update(current => ({
      ...current,
      [this.fieldNames.expiresFrom!]: convertedRange.dateFrom ? new Date(convertedRange.dateFrom) : null,
      [this.fieldNames.expiresTo!]: convertedRange.dateTo ? new Date(convertedRange.dateTo) : null
    }));
  }

  // ============================================================================
  // FILTER ACTIONS
  // ============================================================================

  override emitSearchAction(): void {
    const filters = this.currentFilters();
    this.filterAction.emit({
      action: 'search',
      filters
    });
  }

  // ============================================================================
  // RESET FUNCTIONALITY
  // ============================================================================

  /**
   * Public method to reset filters to default state (for drawer reset button)
   * This only resets the temporary filter state, not the applied filters
   */
  resetFiltersToDefault(): void {
    this.resetAllFilters();
  }

  /**
   * Reset all filters to default values
   * Implementation of abstract method from BaseDataGridFiltersComponent
   */
  protected resetAllFilters(): void {
    // Set reset flag to prevent event emissions during reset
    this.isResetting.set(true);

    // Reset all filter values to their defaults
    this._tempFilters.set({
      [this.fieldNames.teachingLanguageId!]: null,
      [this.fieldNames.purchasedFrom!]: null,
      [this.fieldNames.purchasedTo!]: null,
      [this.fieldNames.expiresFrom!]: null,
      [this.fieldNames.expiresTo!]: null,
      [this.fieldNames.hasAddOnExtension!]: null
    });

    // Trigger reset signal for any child components
    this.resetSelectionSignal.set(true);

    // Reset the signal after a brief delay
    setTimeout(() => {
      this.resetSelectionSignal.set(false);
      this.isResetting.set(false);
    }, 100);
  }
}
