:host {
  display: block;
}

.flag-size {
  width: 0.875rem;
  height: 0.875rem;
}

:host ::ng-deep {
  .p-select-label {
    text-align: left;

  }
}

:host ::ng-deep {
  .teacher-select {
    .p-select {
      width: auto;
    }

    .p-select-panel {
      .p-select-items {
        padding: 0.5rem;
      }
    }

    .teacher-item {
      border-radius: 8px;
      transition: all 0.2s;

      &:hover {
        background-color: var(--surface-100);
      }
    }

    .profile-photo {
      border-radius: 50%;
      border: 2px solid var(--surface-200);
    }

    .language-badge {
      background-color: var(--surface-50);
      border: 1px solid var(--surface-200);
      transition: all 0.2s;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
      }
    }

    .search-loading {
      .p-inputtext {
        background-color: var(--surface-50);
        color: var(--text-color-secondary);
      }
    }

    .typing {
      .p-inputtext {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem var(--primary-color-alpha);
      }
    }

    // Footer styling
    .p-select-footer {
      background-color: var(--surface-0);
      border-top: 1px solid var(--surface-200);
    }

    // Loading states
    .loading-indicator {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 1rem;
      color: var(--text-color-secondary);
    }

    // Search progress indicators
    .search-progress {
      background: linear-gradient(90deg, var(--primary-50) 0%, var(--primary-100) 100%);
      border: 1px solid var(--primary-200);
    }

    .typing-progress {
      background: linear-gradient(90deg, var(--orange-50) 0%, var(--orange-100) 100%);
      border: 1px solid var(--orange-200);
    }

    // Empty state styling
    .empty-state {
      padding: 2rem 1rem;
      text-align: center;
      color: var(--text-color-secondary);
    }

    // Responsive adjustments
    @media (max-width: 768px) {
      .teacher-item {
        padding: 0.75rem;
      }

      .language-badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
      }
    }
  }

  // Multi-select specific styles
  .p-multiselect {
    .p-multiselect-panel {
      .p-multiselect-items {
        padding: 0.5rem;
      }
    }

    .p-multiselect-item {
      border-radius: 8px;
      transition: all 0.2s;

      &:hover {
        background-color: var(--surface-100);
      }
    }

    .p-multiselect-header {
      background-color: var(--surface-0);
      border-bottom: 1px solid var(--surface-200);
    }

    .p-multiselect-footer {
      background-color: var(--surface-0);
      border-top: 1px solid var(--surface-200);
    }
  }

  // Selected item compact design styles
  .compact-avatar {
    width: 24px !important;
    height: 24px !important;
    font-size: 10px !important;
    font-weight: 600 !important;
    margin-left: -4px !important;
    border: 2px solid var(--surface-0) !important;

    &:first-child {
      margin-left: 0 !important;
    }
  }

  // Selected item container
  .p-select-label {
    .flex {
      // min-height: 32px;

    }
  }

  // Availability status styling
  .text-green-600 {
    color: var(--green-600) !important;
  }

  .text-orange-600 {
    color: var(--orange-600) !important;
  }

  // Enhanced hover effects for teacher items
  .teacher-item {
    cursor: pointer;
    
    &:hover {
      .language-badge {
        background-color: var(--surface-100);
        border-color: var(--surface-300);
      }
      
      .profile-photo {
        border-color: var(--primary-200);
      }
    }
  }

  // Status indicators
  .pi-check-circle {
    color: var(--green-500);
  }

  .pi-clock {
    color: var(--orange-500);
  }

  // Responsive design for mobile
  @media (max-width: 576px) {
    .teacher-select {
      .teacher-item {
        .profile-photo {
          display: none;
        }
      }
    }

    .p-multiselect {
      .compact-avatar {
        width: 20px !important;
        height: 20px !important;
        font-size: 8px !important;
      }
    }
  }

  // Focus states
  .p-select:focus-within,
  .p-multiselect:focus-within {
    .p-select-label,
    .p-multiselect-label {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 0.2rem var(--primary-color-alpha);
    }
  }

  // Loading spinner animation
  .pi-spinner {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}
