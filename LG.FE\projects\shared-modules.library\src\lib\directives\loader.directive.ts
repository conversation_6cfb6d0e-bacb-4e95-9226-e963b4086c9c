import { Directive, ElementRef, Renderer2, inject, Injector, model, Input } from '@angular/core';
import { toObservable } from '@angular/core/rxjs-interop';

@Directive({
  standalone: true,
  selector: '[appLoader]',
})
export class LoaderDirective {
  appLoader = model<{
    isLoading: boolean;
    targetSelector: string | null;
    loadingText?: string;
  }>({
    isLoading: false,
    targetSelector: null,
    loadingText: '',
  });

  @Input('appLoaderText') customLoadingText?: string;

  private loaderElement: HTMLElement | null = null;
  private targetElement: HTMLElement | null = null;
  private injector = inject(Injector);

  constructor(private renderer: Renderer2, private el: ElementRef) {
    toObservable(this.appLoader, { injector: this.injector }).subscribe({
      next: ({ isLoading, targetSelector, loadingText }) => {
        const textToDisplay = this.customLoadingText || loadingText || '';
        if (isLoading) {
          this.showLoader(targetSelector, textToDisplay);
        } else {
          this.removeLoader();
        }
      },
    });
  }

  private showLoader(targetSelector: string | null, loadingText: string): void {
    if (!this.loaderElement) {
      const targetSelectorFound = targetSelector && this.el.nativeElement.querySelector(targetSelector);
      this.targetElement = targetSelector
        ? this.el.nativeElement.querySelector(targetSelector)
        : this.el.nativeElement;

      if (this.targetElement) {
        this.loaderElement = this.renderer.createElement('div');
        this.renderer.addClass(this.loaderElement, 'indiv_preloader');

        // PrimeFlex for layout
        this.renderer.addClass(this.loaderElement, 'flex');
        this.renderer.addClass(this.loaderElement, 'flex-column');
        this.renderer.addClass(this.loaderElement, 'justify-content-center');
        this.renderer.addClass(this.loaderElement, 'align-items-center');
        this.renderer.addClass(this.loaderElement, 'w-full');
        this.renderer.addClass(this.loaderElement, 'h-full');

        // Positioning and overlay
        this.renderer.setStyle(this.loaderElement, 'position', targetSelectorFound ? 'absolute' : 'fixed');
        this.renderer.setStyle(this.loaderElement, 'top', '0');
        this.renderer.setStyle(this.loaderElement, 'left', '0');
        this.renderer.setStyle(this.loaderElement, 'background', 'rgba(255, 255, 255, 0.8)');
        this.renderer.setStyle(this.loaderElement, 'z-index', '9999999');

        this.renderer.setStyle(this.targetElement, 'position', 'relative');
        this.renderer.setStyle(this.targetElement, 'overflow', 'hidden');

        // Conditionally include loader-text only if textToDisplay is non-empty
        const textHtml = loadingText ? `<span class="loader-text">${this.escapeHtml(loadingText)}</span>` : '';

        this.loaderElement!.innerHTML = `
          <div class="preloader">
            <div class="preloader-spin"></div>
          </div>
          ${textHtml}
        `;

        this.renderer.appendChild(this.targetElement, this.loaderElement);
      } else {
        console.warn('Target element not found for loader directive');
      }
    }
  }

  private removeLoader(): void {
    if (this.loaderElement && this.targetElement) {
      this.renderer.removeChild(this.targetElement, this.loaderElement);
      this.renderer.removeStyle(this.targetElement, 'overflow');
      this.loaderElement = null;
      this.targetElement = null;
    }
  }

  private escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  ngOnDestroy(): void {
    this.removeLoader();
  }
}