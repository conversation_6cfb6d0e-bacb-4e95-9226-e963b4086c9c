import { Directive, ViewChild, ChangeDetectorRef, inject, signal, computed, OnInit, OnDestroy } from '@angular/core';
import { Table, TablePageEvent } from 'primeng/table';
import { ActivatedRoute, Router, Params } from '@angular/router';
import { Observable, Subject, takeUntil } from "rxjs";
import { HandleApiResponseService } from '../../services/handle-api-response.service';
import { GeneralService } from '../../services/general.service';
import { IBasedDataGridRequest, IDataGridResponse } from '../../GeneratedTsFiles';
import { IDataGridFields } from '../../models/datagrid-fields.model';
import { IAppliedFilterTag } from '../applied-filters-tags/applied-filters-tags.component';
import {
  IBaseDataGridConfig,
  SortDirection,
  ISortEvent,
  IDataGridFilterChangeEvent,
  IDataGridFilterActionEvent,
  IFilterDrawerActionEvent,
  IApiError,
  RowsPerPageOption,
  IColumnSelectionEvent
} from './BaseDataGridInterfaces';

// ============================================================================
// BASE DATA GRID COMPONENT - Reusable data grid with common functionality
// ============================================================================

/**
 * Abstract base class for data grid components
 *
 * Provides common functionality for:
 * - URL parameter management
 * - Data fetching and pagination
 * - Sorting and filtering
 * - Applied filters display
 * - Search functionality
 *
 * @template TRequest - The request object type for API calls
 * @template TResponse - The response object type from API calls
 */
@Directive()
export abstract class BaseDataGridComponent<
  TRequest extends IBasedDataGridRequest = IBasedDataGridRequest,
  TResponse extends IDataGridResponse<any> = IDataGridResponse<any>
> implements OnInit, OnDestroy {
  // ============================================================================
  // COMPONENT SETUP & DEPENDENCIES
  // ============================================================================

  @ViewChild('dt') table!: Table;

  constructor(
    protected route: ActivatedRoute,
    protected cdr: ChangeDetectorRef,
  ) { }

  // === INJECTED SERVICES ===
  protected handleApiService = inject(HandleApiResponseService);
  protected generalService = inject(GeneralService);
  protected router = inject(Router);

  // === ABSTRACT METHODS (Must be implemented by child classes) ===
  abstract getConfig(): IBaseDataGridConfig<TRequest>;

  // ============================================================================
  // STATE MANAGEMENT - Reactive signals for component state
  // ============================================================================

  // === CORE UI STATE ===
  isLoading = signal<boolean>(false);
  cols: IDataGridFields[] = [];
  rowsPerPageOptions = signal<RowsPerPageOption[]>([10, 25, 50]);

  // === DATA & PARAMETERS ===
  protected currentUrlParams = signal<Params>({});
  protected selectedColumns = signal<IDataGridFields[]>([]);
  protected queryParams = signal<TRequest>({} as TRequest);
  protected dataResponse = signal<TResponse | null>(null);

  // === FILTER STATE ===
  protected appliedFilters = signal<IAppliedFilterTag[]>([]);
  protected isFiltersDrawerVisible = signal<boolean>(false);

  // === COMPUTED PROPERTIES ===
  readonly tableData = computed(() => this.dataResponse()?.pageData || []);
  readonly totalRecords = computed(() => this.dataResponse()?.totalRecords || 0);
  readonly currentPage = computed(() => this.dataResponse()?.currentPage || 1);
  readonly pageSize = computed(() => this.queryParams().pageSize || 10);
  readonly appliedFiltersCount = computed(() => {
    const config = this.getConfig();
    const filters = this.appliedFilters();
    return config.appliedFiltersConfig?.getFiltersCount
      ? config.appliedFiltersConfig.getFiltersCount(filters)
      : filters.length;
  });
  readonly hasAppliedFilters = computed(() => this.appliedFiltersCount() > 0);

  // === LIFECYCLE & UTILITY ===
  protected destroy$ = new Subject<void>();
  protected scrollPosition = 0;
  protected searchTimeout: any = null;
  /** Flag to track if component has been initialized to prevent duplicate API calls */
  private isInitialized = signal<boolean>(false);

  // ============================================================================
  // LIFECYCLE HOOKS
  // ============================================================================

  ngOnInit(): void {
    let isFirstEmission = true;
    
    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(params => {
      this.currentUrlParams.set({ ...params });
      const request = this.createRequestFromUrlParams(params);
      this.queryParams.set(request);
      
      // Only update URL params if this is not the first emission and params are empty
      // This prevents the initial URL update from triggering another subscription
      if (Object.keys(params).length === 0 && !isFirstEmission) {
        this.updateUrlParams();
      }
      
      this.fetchData();
      this.updateAppliedFilters();
      
      // Mark first emission as complete
      if (isFirstEmission) {
        isFirstEmission = false;
        this.isInitialized.set(true);
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    if (this.searchTimeout) clearTimeout(this.searchTimeout);
  }

  /**
   * Check if the component has been initialized
   * Child components can use this to avoid duplicate initialization
   */
  protected get componentInitialized(): boolean {
    return this.isInitialized();
  }

  /**
   * Generic method to clean up invalid filter parameters from URL and state
   * @param paramName - The parameter name to remove (e.g., 'studentId', 'teacherId')
   * @param logMessage - Optional custom log message
   */
  protected cleanupInvalidFilterParam(paramName: keyof TRequest, logMessage?: string): void {
    console.log(logMessage || `🧹 Cleaning up invalid ${String(paramName)}`);
    
    // Remove the parameter from query params
    const currentParams = { ...this.queryParams() };
    delete currentParams[paramName];
    
    // Update the query params signal and URL
    this.queryParams.set(currentParams);
    this.updateUrlParams();
    
    // Update applied filters to reflect the removal
    this.updateAppliedFilters();
    
    console.log(`✅ Invalid ${String(paramName)} cleaned up successfully`);
  }

  // ============================================================================
  // URL PARAMETER MANAGEMENT - Handles URL synchronization
  // ============================================================================

  /**
   * Create request object from URL parameters
   */
  protected createRequestFromUrlParams(params: Params): TRequest {
    const config = this.getConfig();
    return config.mapUrlParamsToRequest ? config.mapUrlParamsToRequest(params) : config.defaultRequest;
  }

  /**
   * Update URL with current query parameters
   */
  protected updateUrlParams(): void {
    this.generalService.updateQueryParams(this.queryParams(), {
      queryParamsHandling: '',
      replaceUrl: true
    });
  }

  // ============================================================================
  // APPLIED FILTERS MANAGEMENT - Handles filter display and updates
  // ============================================================================

  /**
   * Update the applied filters display based on current request state
   */
  protected updateAppliedFilters(): void {
    const config = this.getConfig();
    if (config.appliedFiltersConfig?.convertToFilterTags) {
      const filterTags = config.appliedFiltersConfig.convertToFilterTags(
        this.queryParams(),
        this.currentUrlParams()
      );
      this.appliedFilters.set(filterTags);
    }
  }

  // ============================================================================
  // FILTER DRAWER MANAGEMENT - Handles filter sidebar visibility
  // ============================================================================

  openFiltersDrawer(): void {
    this.isFiltersDrawerVisible.set(true);
  }

  closeFiltersDrawer(): void {
    this.isFiltersDrawerVisible.set(false);
  }

  toggleFiltersDrawer(): void {
    this.isFiltersDrawerVisible.update(visible => !visible);
  }

  // ============================================================================
  // DATA FETCHING - Handles API calls and data management
  // ============================================================================

  /**
   * Fetch data from the API using current query parameters
   */
  protected fetchData(): void {
    const config = this.getConfig();

    this.getDataTableData<TRequest, TResponse>(
      this.queryParams(),
      config.apiEndpoint,
      config.errorPrefix || 'Failed to load data'
    ).pipe(takeUntil(this.destroy$)).subscribe({
      next: (response: TResponse) => {
        this.dataResponse.set(response);
        this.updateAppliedFilters();
      },
      error: (error: IApiError) => {
        console.error('Error fetching data:', error);
        this.dataResponse.set(null);
      }
    });
  }

  /**
   * Refresh data by re-fetching from API
   */
  refreshData(): void {
    this.fetchData();
  }

  // ============================================================================
  // EVENT HANDLERS - Handles user interactions
  // ============================================================================

  /**
   * Handle page changes from the data table
   */
  onPageChange(event: TablePageEvent): void {
    const newPageNumber = Math.ceil((event.first || 0) / (event.rows || 10)) + 1;
    const newPageSize = event.rows;

    if (this.queryParams().pageNumber === newPageNumber && this.queryParams().pageSize === newPageSize) return;

    this.saveScrollPosition();
    this.queryParams.update(params => ({ ...params, pageNumber: newPageNumber, pageSize: newPageSize }));
    this.updateUrlParams();
    this.restoreScrollPosition();
  }

  /**
   * Handle sort changes from the data table
   */
  onSortChange(event: ISortEvent): void {
    const sortColumn = event.field;
    const sortDirection: SortDirection = event.order === 1 ? 'asc' : 'desc';

    if (this.queryParams().sortColumn === sortColumn && this.queryParams().sortDirection === sortDirection) return;

    this.saveScrollPosition();
    this.queryParams.update(params => ({ ...params, sortColumn, sortDirection, pageNumber: 1 }));
    this.updateUrlParams();
    this.restoreScrollPosition();
  }

  /**
   * Handle search input changes
   */
  onSearchChange(searchTerm: string): void {
    if (this.searchTimeout) clearTimeout(this.searchTimeout);
    
    this.searchTimeout = setTimeout(() => {
      this.saveScrollPosition();
      this.queryParams.update(params => ({ ...params, searchTerm, pageNumber: 1 }));
      this.updateUrlParams();
      this.restoreScrollPosition();
    }, 300);
  }

  /**
   * Generic filter handlers
   */
  onFilterChange(event: IDataGridFilterChangeEvent<TRequest>): void {
    this.queryParams.update(current => ({ ...current, [event.filterName]: event.value, ...(event.resetPage ? { pageNumber: 1 } : {}) }));
  }

  onFilterAction(event: IDataGridFilterActionEvent<TRequest>): void {
    if (event.action === 'search') {
      this.saveScrollPosition();
      this.queryParams.set(event.filters);
      this.updateUrlParams();
      this.restoreScrollPosition();
    } else if (event.action === 'reset') {
      this.resetFilters();
    }
  }

  /**
   * Generic filter drawer action handler (should be overridden by child classes for 'apply' logic)
   */
  onFiltersDrawerAction(event: IFilterDrawerActionEvent): void {
    switch (event.action) {
      case 'apply':
        // Child classes should override this method to implement specific filter application logic
        console.warn('onFiltersDrawerAction apply case should be overridden by child class for specific filter logic');
        this.closeFiltersDrawer();
        break;
      case 'reset':
        this.resetFilters();
        this.closeFiltersDrawer();
        break;
      case 'close':
        this.closeFiltersDrawer();
        break;
    }
  }

  /**
   * Generic handlers (to be overridden by child classes)
   */
  onColumnsChange(event: IDataGridFields[] | IColumnSelectionEvent): void {
    // Handle both direct array from p-multiSelect and wrapped event object
    const selectedColumns = Array.isArray(event) ? event : event.columns;

    // Filter columns based on selection, preserving order from original cols array
    const filteredColumns = this.cols.filter(col =>
      selectedColumns.some(valCol => valCol.field === col.field)
    );

    console.debug('Column selection changed:', {
      originalCols: this.cols.length,
      selectedFromMultiSelect: selectedColumns.length,
      filteredResult: filteredColumns.length,
      selectedFields: selectedColumns.map(c => c.field),
      filteredFields: filteredColumns.map(c => c.field)
    });

    this.selectedColumns.set(filteredColumns);
  }

  removeFilter(_filterName: string, event?: MouseEvent): void {
    if (event) event.stopPropagation();
    console.warn('removeFilter method should be overridden by child class');
  }

  resetFilters(): void {
    console.warn('resetFilters method should be overridden by child class');
  }

  /**
   * Applied filters handlers with strong typing
   */
  onAppliedFilterRemove(event: { filter: IAppliedFilterTag; event?: MouseEvent }): void {
    const filterName = event.filter.removeData?.filterName;
    if (filterName) this.removeFilter(filterName, event.event);
  }

  onAppliedFiltersClearAll(_event: MouseEvent): void {
    this.resetFilters();
  }

  /**
   * Search functionality
   */
  updateSearchTerm(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.onSearchChange(target.value);
  }

  clearSearchTerm(): void {
    this.onSearchChange('');
  }

  // ============================================================================
  // UTILITY METHODS - Helper functions for common operations
  // ============================================================================

  /**
   * Save current scroll position
   */
  protected saveScrollPosition(): void {
    if (this.table?.el?.nativeElement) {
      const scrollableView = this.table.el.nativeElement.querySelector('.p-datatable-scrollable-body');
      this.scrollPosition = scrollableView ? scrollableView.scrollTop : 0;
    }
  }

  /**
   * Restore saved scroll position
   */
  protected restoreScrollPosition(): void {
    setTimeout(() => {
      if (this.table?.el?.nativeElement && this.scrollPosition > 0) {
        const scrollableView = this.table.el.nativeElement.querySelector('.p-datatable-scrollable-body');
        if (scrollableView) scrollableView.scrollTop = this.scrollPosition;
      }
    }, 100);
  }

  /**
   * Update rows per page options based on total records
   */
  protected updateRowsPerPageOptionsSignal(totalRecords: number, currentPageSize?: number): void {
    const baseOptions = [10, 25, 50];
    let options = [...baseOptions];

    if (currentPageSize && !baseOptions.includes(currentPageSize)) {
      options.push(currentPageSize);
      options.sort((a, b) => a - b);
    }

    if (totalRecords > 0 && totalRecords < Math.max(...options)) {
      options = options.filter(option => option <= totalRecords);
      if (!options.includes(totalRecords)) options.push(totalRecords);
      options.sort((a, b) => a - b);
    }

    this.rowsPerPageOptions.set(options);
  }

  // Helper function to clean the request object before sending to API
  protected cleanRequestForApi<T extends IBasedDataGridRequest>(request: T): Partial<T> {
    return this.generalService.cleanRequestForApi(request);
  }

  protected createEmptyDataGridResponse<T>(): IDataGridResponse<T> {
    return { currentPage: 0, pageSize: 0, totalRecords: 0, totalPages: 0, sortColumn: '', sortDirection: '', pageData: [] };
  }

  /**
   * Generic method to fetch data table data from API and return an Observable
   */
  protected getDataTableData<TRequest extends IBasedDataGridRequest, TResponse extends IDataGridResponse<any>>(
    request: TRequest, endpoint: string, errorPrefix: string = 'Failed to load data'
  ): Observable<TResponse> {
    const cleanRequest = this.cleanRequestForApi(request);
    const responseSubject = new Subject<TResponse>();

    this.isLoading.set(true);

    this.handleApiService.getApiData<TResponse>({ url: endpoint, method: 'GET' }, cleanRequest).subscribe({
      next: (response: TResponse) => {
        const finalResponse = response || this.createEmptyDataGridResponse() as unknown as TResponse;
        responseSubject.next(finalResponse);
        if (response) this.updateRowsPerPageOptionsSignal(response.totalRecords, request.pageSize);
        else console.warn('Empty response received from server');
        this.isLoading.set(false);
      },
      error: (error: any) => {
        console.error(`${errorPrefix}:`, error);
        responseSubject.error(error);
        this.isLoading.set(false);
      }
    });

    return responseSubject.asObservable();
  }
}
