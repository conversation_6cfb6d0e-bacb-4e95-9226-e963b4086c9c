@use "mixins";

:host {
  display: block;
  width: 100%;
}

.complete-teacher-change-step {
  .step-header {
    text-align: center;
    margin-bottom: 2rem;

    @include mixins.breakpoint(mobile) {
      margin-bottom: 1.5rem;
    }

    h3 {
      color: var(--text-color);
      margin-bottom: 0.5rem;
      font-weight: 600;

      @include mixins.breakpoint(mobile) {
        font-size: 1.125rem;
      }
    }

    p {
      color: var(--text-color-secondary);
      max-width: 600px;
      margin: 0 auto;
    }
  }

  .teacher-change-form {
    .form-section {
      .section-title {
        color: var(--text-color);
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 0.75rem;

        @include mixins.breakpoint(mobile) {
          font-size: 0.9375rem;
        }
      }

      // Teacher Selection Styles
      .teacher-selection-card {
        ::ng-deep .p-card-body {
          padding: 1rem;

          @include mixins.breakpoint(mobile) {
            padding: 0.75rem;
          }
        }

        .teacher-option {
          transition: all 0.15s ease-in-out;
          cursor: pointer;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
          }

          &.selected {
            background: var(--primary-50);
            border-color: var(--primary-color);
            box-shadow: 0 1px 4px rgba(var(--primary-color-rgb), 0.15);
          }

          .teacher-info {
            h6 {
              color: var(--text-color);
              font-weight: 600;
              line-height: 1.2;
              font-size: 0.875rem;
            }

            .teacher-details {
              .detail-item {
                display: flex;
                align-items: center;
                gap: 0.25rem;
                color: var(--text-color-secondary);
                font-size: 0.75rem;

                i {
                  color: var(--primary-color);
                  opacity: 0.7;
                  font-size: 0.625rem;
                }
              }
            }
          }

          // Responsive adjustments
          @include mixins.breakpoint(mobile) {
            .teacher-option {
              margin-bottom: 0.5rem;
              padding: 0.5rem;

              .teacher-info {
                h6 {
                  font-size: 0.8125rem;
                }
              }
            }
          }
        }

        // Loading state styles
        .loading-skeleton {
          .skeleton-teacher-item {
            padding: 0.75rem;
            border: 1px solid var(--surface-border);
            border-radius: 6px;
            margin-bottom: 0.75rem;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }

        // Empty state styles
        .empty-state {
          .empty-state-icon {
            background: var(--orange-50);
            color: var(--orange-500);
            width: 4rem;
            height: 4rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;

            i {
              font-size: 1.5rem;
            }
          }

          h5 {
            color: var(--text-color);
            font-weight: 600;
            margin-bottom: 1rem;
          }

          p {
            color: var(--text-color-secondary);
            line-height: 1.5;
            max-width: 20rem;
            margin: 0 auto 1.5rem;
          }
        }

        // Small tag styling
        ::ng-deep .p-tag-sm {
          font-size: 0.625rem;
          padding: 0.125rem 0.375rem;

          .p-tag-icon {
            font-size: 0.5rem;
            margin-right: 0.125rem;
          }
        }

        // Small button styling
        ::ng-deep .p-button-sm {
          font-size: 0.75rem;
          padding: 0.25rem 0.5rem;

          .p-button-icon {
            font-size: 0.625rem;
          }
        }

        // Reason card styling
        .reason-card {
          ::ng-deep .p-card-body {
            padding: 1rem;

            @include mixins.breakpoint(mobile) {
              padding: 0.75rem;
            }
          }

          .field {
            margin-bottom: 0;

            label {
              color: var(--text-color);
              font-weight: 500;
              margin-bottom: 0.5rem;
            }


            .field-error {
              margin-top: 0.25rem;

              .p-error {
                color: var(--red-500);
                font-size: 0.75rem;
                line-height: 1.3;
              }
            }
          }
        }
      }

      .selected-teacher-display {
        .teacher-info {
          h5 {
            color: var(--text-color);
            font-size: 0.9375rem;
          }

          p {
            color: var(--text-color-secondary);
            font-size: 0.8125rem;
          }
        }
      }

      .teacher-selection-wrapper {
        ::ng-deep {
          .p-dropdown {
            width: 100%;
          }
        }
      }

      // Reason Selection Styles
      .reason-selection {
        ::ng-deep {
          .p-dropdown {
            width: 100%;
            
            .p-dropdown-label {
              padding: 0.75rem;
            }
          }
        }
      }

      // Additional Details Styles
      .additional-details {
        textarea {
          width: 100%;
          padding: 0.75rem;
          border: 1px solid var(--surface-300);
          border-radius: 6px;
          font-family: inherit;
          font-size: 0.875rem;
          line-height: 1.5;
          resize: vertical;
          min-height: 100px;

          &:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);
          }

          &::placeholder {
            color: var(--text-color-secondary);
          }

          @include mixins.breakpoint(mobile) {
            font-size: 0.8125rem;
            padding: 0.625rem;
          }
        }
      }
    }
  }

  // Validation and Success Summary Styles
  .validation-summary,
  .success-summary {
    ::ng-deep {
      .p-card {
        .p-card-body {
          padding: 1rem;

          @include mixins.breakpoint(mobile) {
            padding: 0.75rem;
          }
        }
      }
    }

    h5 {
      font-size: 0.875rem;
      margin-bottom: 0.5rem;
    }

    ul {
      list-style-type: disc;
      
      li {
        margin-bottom: 0.25rem;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    p {
      font-size: 0.8125rem;
      line-height: 1.4;
    }
  }
}

// Responsive adjustments
@include mixins.breakpoint(mobile) {
  .complete-teacher-change-step {
    .teacher-change-form {
      .form-section {
        margin-bottom: 1.5rem;

        &:last-child {
          margin-bottom: 1rem;
        }
      }
    }
  }
}

// PrimeNG component overrides
::ng-deep {
  // Dropdown styling
  .p-dropdown {
    &.p-invalid {
      border-color: var(--red-500);
    }
  }

  // Button styling in teacher selection
  .p-button.p-button-outlined {
    &.p-button-secondary {
      border-color: var(--surface-400);
      color: var(--text-color-secondary);

      &:hover {
        background: var(--surface-100);
        border-color: var(--surface-500);
        color: var(--text-color);
      }
    }
  }

  // Card styling for summaries
  .p-card {
    &.border-yellow-200 {
      border: 1px solid #fde047;
    }

    &.bg-yellow-50 {
      background: #fefce8;
    }

    &.border-green-200 {
      border: 1px solid #bbf7d0;
    }

    &.bg-green-50 {
      background: #f0fdf4;
    }
  }

  // Divider styling
  .p-divider {
    margin: 1.5rem 0;

    @include mixins.breakpoint(mobile) {
      margin: 1rem 0;
    }

    &.p-divider-horizontal {
      &:before {
        border-top-color: var(--surface-200);
      }
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .complete-teacher-change-step {
    .teacher-change-form {
      .form-section {
        .additional-details {
          textarea {
            border-width: 2px;

            &:focus {
              border-width: 3px;
            }
          }
        }
      }
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .complete-teacher-change-step {
    .teacher-change-form {
      .form-section {
        .additional-details {
          textarea {
            background: var(--surface-800);
            border-color: var(--surface-600);
            color: var(--text-color);

            &:focus {
              border-color: var(--primary-color);
            }
          }
        }
      }
    }
  }
}
