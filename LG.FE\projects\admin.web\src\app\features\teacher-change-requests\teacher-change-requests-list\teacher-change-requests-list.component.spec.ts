import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { of } from 'rxjs';

import { TeacherChangeRequestsListComponent } from './teacher-change-requests-list.component';
import { TeacherChangeRequestsListComponentHelperService } from './teacher-change-requests-list-component-helper.service';
import { AppliedFiltersAdapterService } from '../../../shared/services/applied-filters-adapter.service';
import { 
  EnumDropdownOptionsService, 
  GeneralService,
  IGetTeacherChangeRequestRequest,
  IGetTeacherChangeRequestResponse,
  ISearchTeacherChangeRequestDto,
  ITeacherChangeRequestStatusEnum
} from 'SharedModules.Library';

describe('TeacherChangeRequestsListComponent', () => {
  let component: TeacherChangeRequestsListComponent;
  let fixture: ComponentFixture<TeacherChangeRequestsListComponent>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockHelperService: jasmine.SpyObj<TeacherChangeRequestsListComponentHelperService>;
  let mockAppliedFiltersAdapter: jasmine.SpyObj<AppliedFiltersAdapterService>;
  let mockEnumDropdownService: jasmine.SpyObj<EnumDropdownOptionsService>;
  let mockGeneralService: jasmine.SpyObj<GeneralService>;

  const mockTeacherChangeRequest: ISearchTeacherChangeRequestDto = {
    requestId: 'test-request-1',
    parentFirstName: 'John',
    parentLastName: 'Doe',
    parentId: 'parent-1',
    parentEmail: '<EMAIL>',
    teacherToChangeFirstName: 'Jane',
    teacherToChangeLastName: 'Smith',
    teacherToChangeId: 'teacher-1',
    teacherToChangeTeacherEmail: '<EMAIL>',
    newTeacherFirstName: 'Bob',
    newTeacherLastName: 'Johnson',
    studentFirstName: 'Alice',
    studentLastName: 'Doe',
    studentId: 'student-1',
    groupName: 'Test Group',
    groupId: 'group-1',
    teachingLanguageName: 'English',
    reason: 'Test reason',
    adminNotes: 'Test admin notes',
    dateCreated: new Date('2024-01-01'),
    lastModifiedDate: new Date('2024-01-02')
  };

  const mockResponse: IGetTeacherChangeRequestResponse = {
    pageData: [mockTeacherChangeRequest],
    currentPage: 1,
    pageSize: 10,
    totalRecords: 1,
    totalPages: 1,
    sortColumn: 'dateCreated',
    sortDirection: 'desc'
  };

  beforeEach(async () => {
    // Create spies
    mockRouter = jasmine.createSpyObj('Router', ['navigate']);
    mockHelperService = jasmine.createSpyObj('TeacherChangeRequestsListComponentHelperService', [
      'createDefaultTeacherChangeRequestsRequest',
      'mapQueryParamsToTeacherChangeRequestsRequest',
      'exportTeacherChangeRequestsTable',
      'getDefaultSortColumn',
      'getDefaultSortDirection'
    ]);
    mockAppliedFiltersAdapter = jasmine.createSpyObj('AppliedFiltersAdapterService', [
      'convertTeacherChangeRequestsRequestToFilterTags'
    ]);
    mockEnumDropdownService = jasmine.createSpyObj('EnumDropdownOptionsService', [], {
      teacherChangeRequestStatusEnumOptions: [
        { label: 'Pending', value: ITeacherChangeRequestStatusEnum.Pending },
        { label: 'Approved', value: ITeacherChangeRequestStatusEnum.Approved },
        { label: 'Rejected', value: ITeacherChangeRequestStatusEnum.Rejected },
        { label: 'Cancelled', value: ITeacherChangeRequestStatusEnum.Cancelled }
      ]
    });
    mockGeneralService = jasmine.createSpyObj('GeneralService', ['getApiData']);

    // Setup default return values
    mockHelperService.createDefaultTeacherChangeRequestsRequest.and.returnValue({
      pageNumber: 1,
      pageSize: 10,
      sortColumn: 'dateCreated',
      sortDirection: 'desc',
      searchTerm: null,
      status: null
    } as IGetTeacherChangeRequestRequest);

    mockHelperService.getDefaultSortColumn.and.returnValue('dateCreated');
    mockHelperService.getDefaultSortDirection.and.returnValue('desc');

    await TestBed.configureTestingModule({
      imports: [TeacherChangeRequestsListComponent],
      providers: [
        { provide: Router, useValue: mockRouter },
        { provide: TeacherChangeRequestsListComponentHelperService, useValue: mockHelperService },
        { provide: AppliedFiltersAdapterService, useValue: mockAppliedFiltersAdapter },
        { provide: EnumDropdownOptionsService, useValue: mockEnumDropdownService },
        { provide: GeneralService, useValue: mockGeneralService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(TeacherChangeRequestsListComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.statusOptions()).toEqual([]);
    expect(component.availableColumns().length).toBeGreaterThan(0);
    expect(component.defaultSelectedColumns().length).toBeGreaterThan(0);
  });

  it('should initialize status options on ngOnInit', () => {
    component.ngOnInit();
    
    expect(component.statusOptions().length).toBe(4);
    expect(component.statusOptions()[0].label).toBe('Pending');
  });

  it('should get correct status severity', () => {
    expect(component.getStatusSeverity(ITeacherChangeRequestStatusEnum.Approved)).toBe('success');
    expect(component.getStatusSeverity(ITeacherChangeRequestStatusEnum.Pending)).toBe('info');
    expect(component.getStatusSeverity(ITeacherChangeRequestStatusEnum.Rejected)).toBe('danger');
    expect(component.getStatusSeverity(ITeacherChangeRequestStatusEnum.Cancelled)).toBe('warn');
  });

  it('should get correct status label', () => {
    expect(component.getStatusLabel(ITeacherChangeRequestStatusEnum.Pending)).toBe('Pending');
    expect(component.getStatusLabel(ITeacherChangeRequestStatusEnum.Approved)).toBe('Approved');
  });

  it('should format full name correctly', () => {
    expect(component.getFullName('John', 'Doe')).toBe('John Doe');
    expect(component.getFullName('John', '')).toBe('John');
    expect(component.getFullName('', 'Doe')).toBe('Doe');
    expect(component.getFullName('', '')).toBe('');
  });

  it('should format date correctly', () => {
    const testDate = new Date('2024-01-01T10:30:00Z');
    const formatted = component.formatUtcDateToAdminLocalized(testDate);
    expect(formatted).toContain('01/01/2024');
  });

  it('should track by request ID', () => {
    const result = component.trackByRequestId(0, mockTeacherChangeRequest);
    expect(result).toBe('test-request-1');
  });

  it('should get column style', () => {
    const style = component.getColumnStyle('requestId');
    expect(style['min-width']).toBe('150px');
    expect(style['max-width']).toBe('150px');
  });
});
