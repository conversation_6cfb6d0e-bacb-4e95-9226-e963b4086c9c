import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

// PrimeNG Modules
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { RadioButtonModule } from 'primeng/radiobutton';

import { ChangeTeacherRequestComponent } from './change-teacher-request.component';
import { TeacherChangeType, ChangeTeacherStep } from './interfaces/change-teacher-request.interfaces';
import { DynamicProgressStepsComponent } from '../../shared/components/dynamic-progress-steps/dynamic-progress-steps.component';
import { CompleteTeacherChangeStepComponent } from './components/complete-teacher-change-step/complete-teacher-change-step.component';

describe('ChangeTeacherRequestComponent', () => {
  let component: ChangeTeacherRequestComponent;
  let fixture: ComponentFixture<ChangeTeacherRequestComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ChangeTeacherRequestComponent,
        FormsModule,
        ReactiveFormsModule,
        NoopAnimationsModule,
        ButtonModule,
        CardModule,
        RadioButtonModule,
        DynamicProgressStepsComponent,
        CompleteTeacherChangeStepComponent
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ChangeTeacherRequestComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with correct default values', () => {
    expect(component.currentStep()).toBe(ChangeTeacherStep.SELECTION);
    expect(component.selectedChangeType()).toBeNull();
    expect(component.isFirstStep()).toBe(true);
    expect(component.isLastStep()).toBe(false);
    expect(component.canProceedToNextStep()).toBe(false);
  });

  it('should have correct step definitions', () => {
    expect(component.stepDefinitions).toHaveLength(3);
    expect(component.stepDefinitions[0].id).toBe(1);
    expect(component.stepDefinitions[0].route).toBe('selection');
    expect(component.stepDefinitions[1].id).toBe(2);
    expect(component.stepDefinitions[1].route).toBe('details');
    expect(component.stepDefinitions[2].id).toBe(3);
    expect(component.stepDefinitions[2].route).toBe('confirmation');
  });

  it('should allow proceeding to next step when change type is selected', () => {
    // Initially cannot proceed
    expect(component.canProceedToNextStep()).toBe(false);

    // Select a change type
    component.onChangeTypeSelect(TeacherChangeType.COMPLETE_CHANGE);
    fixture.detectChanges();

    // Now should be able to proceed
    expect(component.canProceedToNextStep()).toBe(true);
    expect(component.selectedChangeType()).toBe(TeacherChangeType.COMPLETE_CHANGE);
  });

  it('should navigate to next step when nextStep is called', () => {
    // Select change type first
    component.onChangeTypeSelect(TeacherChangeType.STUDENT_GROUP_CHANGE);
    fixture.detectChanges();

    // Navigate to next step
    component.nextStep();
    fixture.detectChanges();

    expect(component.currentStep()).toBe(ChangeTeacherStep.DETAILS);
    expect(component.isFirstStep()).toBe(false);
  });

  it('should navigate to previous step when previousStep is called', () => {
    // Move to step 2 first
    component.onChangeTypeSelect(TeacherChangeType.COMPLETE_CHANGE);
    component.nextStep();
    fixture.detectChanges();

    expect(component.currentStep()).toBe(ChangeTeacherStep.DETAILS);

    // Navigate back
    component.previousStep();
    fixture.detectChanges();

    expect(component.currentStep()).toBe(ChangeTeacherStep.SELECTION);
    expect(component.isFirstStep()).toBe(true);
  });

  it('should handle step navigation via handleStepNavigation', () => {
    // Select change type to make other steps accessible
    component.onChangeTypeSelect(TeacherChangeType.COMPLETE_CHANGE);
    fixture.detectChanges();

    // Navigate to details step
    component.handleStepNavigation('details');
    fixture.detectChanges();

    expect(component.currentStep()).toBe(ChangeTeacherStep.DETAILS);
  });

  it('should not allow navigation to inaccessible steps', () => {
    // Without selecting change type, details step should not be accessible
    component.handleStepNavigation('details');
    fixture.detectChanges();

    // Should remain on selection step
    expect(component.currentStep()).toBe(ChangeTeacherStep.SELECTION);
  });

  it('should update step when onStepChanged is called', () => {
    const mockEvent = {
      currentStep: {
        id: 2,
        route: 'details',
        title: 'Change Details',
        shortTitle: 'Details',
        description: 'Provide details for your teacher change request',
        icon: 'pi pi-info-circle',
        isCompleted: false,
        isActive: true,
        isAccessible: true
      },
      stepIndex: 1
    };

    component.onStepChanged(mockEvent);
    fixture.detectChanges();

    expect(component.currentStep()).toBe(ChangeTeacherStep.DETAILS);
  });

  it('should handle both change type selections correctly', () => {
    // Test complete change selection
    component.onChangeTypeSelect(TeacherChangeType.COMPLETE_CHANGE);
    fixture.detectChanges();
    expect(component.selectedChangeType()).toBe(TeacherChangeType.COMPLETE_CHANGE);

    // Test student/group change selection
    component.onChangeTypeSelect(TeacherChangeType.STUDENT_GROUP_CHANGE);
    fixture.detectChanges();
    expect(component.selectedChangeType()).toBe(TeacherChangeType.STUDENT_GROUP_CHANGE);
  });
});
