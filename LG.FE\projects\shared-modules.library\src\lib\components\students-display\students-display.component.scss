@use 'mixins';

.students-display-container {
  &.compact {
    .student-item {
      .student-photo {
        width: 24px;
        height: 24px;
      }
    }
  }

  .student-item {
    transition: all 0.2s ease;
    
    &:hover {
    }

    .student-photo {
      border-radius: 50%;
      border: 2px solid var(--surface-border);
      transition: border-color 0.2s ease;
      
      &:hover {
        border-color: var(--primary-color);
      }
    }

    .student-info {
      .student-name {
        color: var(--text-color);
        line-height: 1.2;
      }

      .student-age {
        font-size: 0.75rem;
      }

      .student-languages {
        .language-tag {
          font-weight: 500;
          border: 1px solid transparent;
          
          &:hover {
            border-color: var(--primary-color);
          }
        }
      }
    }
  }

  .more-indicator {
    cursor: help;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    background: var(--surface-100);
    border: 1px solid var(--surface-border);
    transition: all 0.2s ease;

    &:hover {
      background: var(--surface-200);
      border-color: var(--primary-color);
    }
  }
}

.students-avatar-group {
  .p-avatar {
    border: 2px solid var(--surface-0);
    transition: all 0.2s ease;
    
    &:hover {
      z-index: 10;
      border-color: var(--primary-color);
    }
  }
}

.empty-state {
  padding: 1rem;
  text-align: center;
  font-style: italic;
}

// Responsive adjustments


// Dark mode support
:host-context(.dark) {
  .students-display-container {
    .student-item {
      .student-photo {
        border-color: var(--surface-border);
      }

      .student-info {
        .student-name {
          color: var(--text-color);
        }

        .student-languages {
          .language-tag {
            background: var(--surface-200);
            color: var(--text-color);
          }
        }
      }
    }

    .more-indicator {
      background: var(--surface-200);
      border-color: var(--surface-border);
      color: var(--text-color);
    }
  }
}
