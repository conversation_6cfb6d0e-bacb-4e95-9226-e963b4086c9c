import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { SelectModule } from 'primeng/select';
import { MultiSelectModule } from 'primeng/multiselect';
import { ButtonModule } from 'primeng/button';
import { TooltipModule } from 'primeng/tooltip';
import { InputTextModule } from 'primeng/inputtext';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';

import { PrimeTeachersSelectionComponent } from './prime-teachers-selection.component';
import { PrimeProfilePhotoSingleComponent } from '../prime-profile-photo-single/prime-profile-photo-single.component';
import { DataApiStateService } from '../../../services/data-api-state.service';
import { EventBusService } from '../../../services/event-bus.service';
import { AuthStateService } from '../../../services/auth-state.service';
import { GeneralService } from '../../../services/general.service';
import { ISearchTeacherDto, ITeacherTeachingLanguageDto } from '../../../GeneratedTsFiles';

describe('PrimeTeachersSelectionComponent', () => {
  let component: PrimeTeachersSelectionComponent;
  let fixture: ComponentFixture<PrimeTeachersSelectionComponent>;
  let mockDataStateService: jasmine.SpyObj<DataApiStateService<any>>;
  let mockEventBusService: jasmine.SpyObj<EventBusService>;
  let mockAuthStateService: jasmine.SpyObj<AuthStateService>;
  let mockGeneralService: jasmine.SpyObj<GeneralService>;

  beforeEach(async () => {
    // Create spy objects for services
    mockDataStateService = jasmine.createSpyObj('DataApiStateService', ['teachers']);
    mockEventBusService = jasmine.createSpyObj('EventBusService', ['emit', 'on']);
    mockAuthStateService = jasmine.createSpyObj('AuthStateService', ['getUserClaims']);
    mockGeneralService = jasmine.createSpyObj('GeneralService', ['someMethod']);

    // Mock the teachers state signal
    const mockTeachersState = jasmine.createSpy().and.returnValue({
      loading: false,
      data: null,
      error: null,
      hasError: false,
      initialized: false,
      errorStatusCode: null
    });
    Object.defineProperty(mockDataStateService, 'teachers', {
      value: {
        state: mockTeachersState,
        setState: jasmine.createSpy()
      },
      writable: true
    });

    await TestBed.configureTestingModule({
      imports: [
        PrimeTeachersSelectionComponent,
        FormsModule,
        NoopAnimationsModule,
        SelectModule,
        MultiSelectModule,
        ButtonModule,
        TooltipModule,
        InputTextModule,
        IconFieldModule,
        InputIconModule,
        PrimeProfilePhotoSingleComponent
      ],
      providers: [
        { provide: DataApiStateService, useValue: mockDataStateService },
        { provide: EventBusService, useValue: mockEventBusService },
        { provide: AuthStateService, useValue: mockAuthStateService },
        { provide: GeneralService, useValue: mockGeneralService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(PrimeTeachersSelectionComponent);
    component = fixture.componentInstance;
  });

  // Mock teacher data
  const mockTeachers: ISearchTeacherDto[] = [
    {
      id: '1',
      firstName: 'John',
      lastName: 'Doe',
      availabilityStatus: 1,
      gender: 'Male',
      country: 'US',
      city: 'New York',
      timezone: 'America/New_York',
      vacationDates: [],
      speakingLanguages: [],
      teacherTeachingLanguages: [
        {
          teacherTeachingLanguageId: '1',
          teachingLanguageId: '1',
          teachingLanguageName: 'English',
          languageLevelsEnum: 1
        }
      ],
      isBlocked: false,
      accountStatus: 1,
      primaryEmail: '<EMAIL>',
      phoneNumber: '+**********',
      activeStudentsCount: 10,
      activePackagesCount: 5
    },
    {
      id: '2',
      firstName: 'Jane',
      lastName: 'Smith',
      availabilityStatus: 0,
      gender: 'Female',
      country: 'UK',
      city: 'London',
      timezone: 'Europe/London',
      vacationDates: [],
      speakingLanguages: [],
      teacherTeachingLanguages: [
        {
          teacherTeachingLanguageId: '2',
          teachingLanguageId: '2',
          teachingLanguageName: 'Spanish',
          languageLevelsEnum: 2
        }
      ],
      isBlocked: false,
      accountStatus: 1,
      primaryEmail: '<EMAIL>',
      phoneNumber: '+***********',
      activeStudentsCount: 8,
      activePackagesCount: 3
    }
  ];

  beforeEach(() => {
    // Set up component with mock data using fixture componentRef
    fixture.componentRef.setInput('items', mockTeachers);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.selectionMode).toBe('single');
    expect(component.emptyMessageText).toBe('No teachers found.');
    expect(component.selectedTeacher()).toEqual({} as ISearchTeacherDto);
    expect(component.selectedTeachers()).toEqual([]);
  });

  it('should handle single selection', () => {
    spyOn(component.itemClicked, 'emit');
    
    const teacher = mockTeachers[0];
    component.onSelectionChange({ value: teacher });

    expect(component.selectedTeacher()).toEqual(teacher);
    expect(component.itemClicked.emit).toHaveBeenCalledWith(teacher);
  });

  it('should handle multiple selection', () => {
    component.selectionMode = 'multiple';
    spyOn(component.itemClicked, 'emit');
    
    const teachers = [mockTeachers[0], mockTeachers[1]];
    component.onSelectMultipleChange({ value: teachers });

    expect(component.selectedTeachers()).toEqual(teachers);
    expect(component.itemClicked.emit).toHaveBeenCalledWith(teachers);
  });

  it('should clear all teachers in multiple selection mode', () => {
    component.selectionMode = 'multiple';
    component.selectedTeachers.set(mockTeachers);
    spyOn(component.itemClicked, 'emit');

    component.clearAllTeachers();

    expect(component.selectedTeachers()).toEqual([]);
    expect(component.itemClicked.emit).toHaveBeenCalledWith([]);
  });

  it('should select all teachers in multiple selection mode', () => {
    component.selectionMode = 'multiple';
    spyOn(component.itemClicked, 'emit');

    component.selectAllTeachers();

    expect(component.selectedTeachers()).toEqual(mockTeachers);
    expect(component.itemClicked.emit).toHaveBeenCalledWith(mockTeachers);
  });

  it('should get teacher full name correctly', () => {
    const teacher = mockTeachers[0];
    const fullName = component.getTeacherFullName(teacher);
    expect(fullName).toBe('John Doe');
  });

  it('should get teacher location text correctly', () => {
    const teacher = mockTeachers[0];
    const location = component.getTeacherLocationText(teacher);
    expect(location).toBe('New York, US');
  });

  it('should get teacher languages text correctly', () => {
    const teacher = mockTeachers[0];
    const languages = component.getTeacherLanguagesText(teacher);
    expect(languages).toBe('English');
  });

  it('should handle teachers with no languages', () => {
    const teacherWithoutLanguages: ISearchTeacherDto = {
      ...mockTeachers[0],
      teacherTeachingLanguages: []
    };
    const languages = component.getTeacherLanguagesText(teacherWithoutLanguages);
    expect(languages).toBe('No languages');
  });

  it('should truncate languages when there are more than MAX_VISIBLE_LANGUAGES', () => {
    const teacherWithManyLanguages: ISearchTeacherDto = {
      ...mockTeachers[0],
      teacherTeachingLanguages: [
        { teacherTeachingLanguageId: '1', teachingLanguageId: '1', teachingLanguageName: 'English', languageLevelsEnum: 1 },
        { teacherTeachingLanguageId: '2', teachingLanguageId: '2', teachingLanguageName: 'Spanish', languageLevelsEnum: 2 },
        { teacherTeachingLanguageId: '3', teachingLanguageId: '3', teachingLanguageName: 'French', languageLevelsEnum: 1 }
      ]
    };
    
    const languages = component.getTeacherLanguagesText(teacherWithManyLanguages);
    expect(languages).toBe('English, Spanish +1 more');
  });

  it('should compute selected teacher names for single selection', () => {
    component.selectionMode = 'single';
    component.selectedTeacher.set(mockTeachers[0]);
    
    const names = component.selectedTeacherNames();
    expect(names).toBe('John Doe');
  });

  it('should compute selected teacher names for multiple selection', () => {
    component.selectionMode = 'multiple';
    component.selectedTeachers.set([mockTeachers[0], mockTeachers[1]]);
    
    const names = component.selectedTeacherNames();
    expect(names).toBe('John Doe, Jane Smith');
  });

  it('should clear selection when reset signal is triggered', () => {
    component.selectedTeacher.set(mockTeachers[0]);
    fixture.componentRef.setInput('resetSelectionSignal', true);
    fixture.detectChanges();

    // Trigger the reset subscription manually for testing
    component['clearSelection']();

    expect(component.selectedTeacher()).toEqual({} as ISearchTeacherDto);
  });
});
