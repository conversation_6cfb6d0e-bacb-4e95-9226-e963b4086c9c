<div class="teacher-change-review-step">
  <!-- Step Header -->
  <!-- <div class="step-header mb-4 md:mb-6">
    <h3 class="text-lg md:text-xl font-semibold text-900 mb-2">Review Teacher Change Request</h3>
    <p class="text-600 text-sm md:text-base line-height-3">
      Please review the details of your teacher change request before submitting.
    </p>
  </div> -->

  @if (isLoading()) {
    <!-- Loading State -->
    <div class="loading-content">
      <p-skeleton height="200px" class="mb-4"></p-skeleton>
      <p-skeleton height="150px" class="mb-4"></p-skeleton>
      <p-skeleton height="100px"></p-skeleton>
    </div>
  } @else if (reviewData()) {
    <!-- Review Content -->
    <div class="review-content">

      <!-- Selected Teacher Summary - Compact -->
      <div class="selected-teacher-summary mb-3 p-3 border-round-lg bg-primary-50 border-1 border-primary-200">
        <div class="flex align-items-center gap-3">
          <lib-prime-profile-photo-single
            [width]="40" [height]="40"
            [src]="selectedTeacher()?.profilePhotoUrl!">
          </lib-prime-profile-photo-single>
          <div class="teacher-info flex-1">
            <h5 class="text-900 font-semibold m-0 mb-1 text-sm">
              {{ selectedTeacher()?.firstName }} {{ selectedTeacher()?.lastName }}
            </h5>
            <p class="text-600 text-xs m-0">
              {{ selectedTeacher()?.timeZoneDisplayName }}
            </p>
          </div>
          <div class="text-right">
            <p class="text-primary-700 font-semibold text-xs m-0">Selected Teacher</p>
          </div>
        </div>
      </div>

      <!-- Impact Summary - Compact Stats -->
      <div class="impact-summary mb-3">
        <div class="grid">
          <div class="col-4">
            <div class="text-center p-2 border-round bg-surface-50">
              <div class="text-lg font-bold text-primary mb-1">{{ reviewData()!.affectedResources.length }}</div>
              <div class="text-600 text-xs">Packages</div>
            </div>
          </div>
          <div class="col-4">
            <div class="text-center p-2 border-round bg-surface-50">
              <div class="text-lg font-bold text-primary mb-1">{{ totalAffectedLessons() }}</div>
              <div class="text-600 text-xs">Lessons</div>
            </div>
          </div>
          <div class="col-4">
            <div class="text-center p-2 border-round bg-surface-50">
              <div class="text-lg font-bold text-primary mb-1">{{ totalAffectedStudents() }}</div>
              <div class="text-600 text-xs">Students</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Student/Group Selection Summary (for STUDENT_GROUP_CHANGE) - Compact -->
      @if (changeType === TeacherChangeType.STUDENT_GROUP_CHANGE && studentGroupData) {
        <div class="selection-summary mb-3 p-3 border-round-lg bg-surface-50 border-1 surface-border">
          <div class="flex flex-column gap-2">
            <!-- Teaching Language -->
            @if (studentGroupData.teachingLanguage) {
              <div class="flex align-items-center gap-2">
                <i class="pi pi-globe text-primary-600 text-sm"></i>
                <span class="text-xs text-600">Teaching Language:</span>
                <span class="text-xs font-medium text-900">{{ studentGroupData.teachingLanguage.name }}</span>
              </div>
            }

            <!-- Selection Type and Details -->
            @if (studentGroupData.selectionType === 'student') {
              <div class="flex align-items-center gap-2">
                <i class="pi pi-user text-primary-600 text-sm"></i>
                <span class="text-xs text-600">Student:</span>
                <span class="text-xs font-medium text-900">
                  {{ studentGroupData.student?.firstName }} {{ studentGroupData.student?.lastName }}
                </span>
              </div>
            } @else if (studentGroupData.selectionType === 'group') {
              <div class="flex align-items-center gap-2">
                <i class="pi pi-users text-primary-600 text-sm"></i>
                <span class="text-xs text-600">Group:</span>
                <span class="text-xs font-medium text-900">
                  {{ studentGroupData.group?.groupName || studentGroupData.group?.teachingLanguageName }}
                </span>
              </div>
            }

            <!-- Change Scope -->
            <div class="flex align-items-center gap-2 mt-1">
              <i class="pi pi-info-circle text-primary-600 text-xs"></i>
              <p class="text-primary-700 text-xs m-0">
                @if (studentGroupData.selectionType === 'student') {
                  This change applies only to the selected student.
                } @else if (studentGroupData.selectionType === 'group') {
                  This change applies to all students in the selected group.
                }
              </p>
            </div>
          </div>
        </div>
      }

      <!-- Reason for Change - Compact -->
      <div class="reason-summary mb-3 p-3 border-round-lg bg-surface-50 border-1 surface-border">
        <div class="flex flex-column gap-2">
          <div class="flex align-items-center gap-2">
            <i class="pi pi-comment text-primary-600 text-sm"></i>
            <span class="text-xs font-medium text-900">Reason for Change</span>
          </div>
          <p class="text-xs text-700 m-0 line-height-3 pl-4">{{ reason() }}</p>
        </div>
      </div>

      <!-- Accordion for Affected Resources -->
      <p-accordion [multiple]="true" styleClass="teacher-change-accordion">

        <!-- Affected Packages Panel -->
        <p-accordionTab>
          <ng-template pTemplate="header">
            <div class="flex align-items-center gap-2 w-full">
              <i class="pi pi-box text-primary-600"></i>
              <span class="font-medium">Affected Packages</span>
              <p-tag [value]="reviewData()!.affectedResources.length.toString()" severity="info" styleClass="ml-auto text-xs"></p-tag>
            </div>
          </ng-template>

          <div class="packages-content">
            @if (reviewData()!.affectedResources.length > 0) {
              <div class="packages-list max-h-20rem overflow-y-auto">
                @for (packageItem of reviewData()!.affectedResources; track $index) {
                  <div class="package-item p-3 border-bottom-1 surface-border">
                    <div class="flex align-items-start justify-content-between gap-3">
                      <div class="package-info flex-1">
                        <h6 class="text-900 font-medium m-0 mb-2 text-sm">
                          {{ getPackageLanguageName(packageItem) }}
                        </h6>
                        <div class="grid">
                          <div class="col-6">
                            <div class="text-xs text-600 mb-1">Remaining: <span class="text-900 font-medium">{{ getPackageRemainingLessons(packageItem) }}</span></div>
                            <div class="text-xs text-600 mb-1">Total: <span class="text-900 font-medium">{{ getPackageTotalLessons(packageItem) }}</span></div>
                            <div class="text-xs text-600">Duration: <span class="text-900 font-medium">{{ getPackageDuration(packageItem) }}min</span></div>
                          </div>
                          <div class="col-6">
                            <div class="text-xs text-600 mb-1">Students: <span class="text-900 font-medium">{{ getPackageStudentsCount(packageItem) }}</span></div>
                            <div class="text-xs text-600 mb-1">Original Expiry: <span class="text-900 font-medium">{{ formatPackageDate(getPackageOriginalExpirationDate(packageItem)) }}</span></div>
                            @if (getPackageHasBeenExtended(packageItem)) {
                              <div class="text-xs text-600">Current Expiry: <span class="text-900 font-medium">{{ formatPackageDate(getPackageExpiresOnDate(packageItem)) }}</span></div>
                            }
                          </div>
                        </div>
                      </div>
                      <div class="package-status">
                        <p-tag
                          [value]="getPackageStatusLabel(getPackageStatus(packageItem))"
                          [severity]="getPackageStatusSeverity(getPackageStatus(packageItem))"
                          styleClass="text-xs">
                        </p-tag>
                      </div>
                    </div>
                  </div>
                }
              </div>
            } @else {
              <div class="no-packages text-center p-3">
                <p class="text-600 text-sm m-0">No packages found</p>
              </div>
            }
          </div>
        </p-accordionTab>

        <!-- Affected Students Panel -->
        <p-accordionTab>
          <ng-template pTemplate="header">
            <div class="flex align-items-center gap-2 w-full">
              <i class="pi pi-users text-primary-600"></i>
              <span class="font-medium">Affected Students</span>
              <p-tag [value]="totalAffectedStudents().toString()" severity="info" styleClass="ml-auto text-xs"></p-tag>
            </div>
          </ng-template>

          <div class="students-content">
            @if (affectedStudents().length > 0) {
              <div class="students-list max-h-20rem overflow-y-auto">
                <div class="">
                  @for (student of affectedStudents(); track student.userId) {
                    <div class="col-12">
                      <div class="student-card p-2 border-1 surface-border border-round mb-2">
                        <div class="flex align-items-center gap-2">
                          <lib-prime-profile-photo-single
                            [width]="32" [height]="32"
                            [src]="student.profilePhotoUrl!">
                          </lib-prime-profile-photo-single>
                          <div class="student-info">
                            <h6 class="text-900 font-medium m-0 mb-1 text-xs">
                              {{ student.firstName }} {{ student.lastName }}
                            </h6>
                            <p class="text-600 text-xs m-0">
                              {{ student.timeZoneDisplayName }}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  }
                </div>
              </div>
            } @else {
              <div class="no-students text-center p-3">
                <p class="text-600 text-sm m-0">No students found</p>
              </div>
            }
          </div>
        </p-accordionTab>

        <!-- Affected Lessons Panel -->
        <p-accordionTab>
          <ng-template pTemplate="header">
            <div class="flex align-items-center gap-2 w-full">
              <i class="pi pi-calendar text-primary-600"></i>
              <span class="font-medium">Affected Lessons</span>
              <p-tag [value]="totalAffectedLessons().toString()" severity="info" styleClass="ml-auto text-xs"></p-tag>
            </div>
          </ng-template>

          <div class="lessons-content">
            @if (affectedLessons().length > 0) {
              <div class="lessons-list max-h-20rem overflow-y-auto">
                @for (lesson of affectedLessons(); track lesson.lessonId) {
                  <div class="lesson-item flex align-items-center justify-content-between p-2 border-bottom-1 surface-border">
                    <div class="lesson-info">
                      <h6 class="text-900 font-medium m-0 mb-1 text-xs">
                        {{ lesson.teachingLanguageName }} - Level {{ lesson.languageLevel }}
                      </h6>
                      <div class="flex gap-2 align-items-center">
                        <p-tag
                          [value]="getLessonTypeLabel(lesson.lessonType)"
                          [severity]="'info'"
                          styleClass="text-xs">
                        </p-tag>
                        <p-tag
                          [value]="getLessonStatusLabel(lesson.lessonStatus)"
                          [severity]="getLessonStatusSeverity(lesson.lessonStatus)"
                          styleClass="text-xs">
                        </p-tag>
                      </div>
                    </div>
                    <div class="lesson-students">
                      <span class="text-600 text-xs">{{ lesson.students.length }} student(s)</span>
                    </div>
                  </div>
                }
              </div>
            } @else {
              <div class="no-lessons text-center p-3">
                <p class="text-600 text-sm m-0">No lessons found</p>
              </div>
            }
          </div>
        </p-accordionTab>
      </p-accordion>


    </div>
  } @else {
    <!-- Error State -->
    <div class="error-content text-center p-4">
      <i class="pi pi-exclamation-triangle text-4xl text-orange-500 mb-3"></i>
      <h5 class="text-900 mb-2">Unable to Load Review Data</h5>
      <p class="text-600 text-sm mb-4">
        There was an issue loading the review information. Please go back and try again.
      </p>

    </div>
  }
</div>
