<!-- ============================================================================ -->
<!-- TEACHER CHANGE REQUESTS LIST COMPONENT TEMPLATE -->
<!-- ============================================================================ -->

<div class="teacher-change-requests-list-container h-full flex flex-column">


  <!-- Applied Filters -->
  <app-applied-filters-tags
    [filters]="appliedFilters()"
    (filterRemoved)="onAppliedFilterRemove($event)"
    (clearAllClicked)="onAppliedFiltersClearAll($event)"
    class="mb-3">
            <p-button extraButton
          icon="pi pi-filter"
          label="Filters"
          severity="secondary"
          size="small"
          (click)="onFiltersButtonClick()"
          [badge]="appliedFilters().length > 0 ? appliedFilters().length.toString() : undefined"
          badgeClass="p-badge-info"
        />
  </app-applied-filters-tags>

  <!-- Data Grid -->
  <div class="flex-1 overflow-hidden">
    <!-- Table Container -->
    <div class="table-container surface-card p-3 border-round">
      <p-table #table [value]="dataResponse()?.pageData || []" dataKey="requestId" [lazy]="true" [paginator]="true"
        [rows]="queryParams().pageSize" [totalRecords]="dataResponse()?.totalRecords || 0" [loading]="isLoading()"
        [rowsPerPageOptions]="[10, 25, 50, 100]" [showCurrentPageReport]="true"
        [sortField]="queryParams().sortColumn" [sortOrder]="queryParams().sortDirection === 'asc' ? 1 : -1"
        [reorderableColumns]="true" currentPageReportTemplate="Showing {first} to {last} of {totalRecords} teacher change requests"
        (onPage)="onPageChange($event)" (onSort)="onSortChange($event)" (onColReorder)="onColumnReorder($event)"
        [scrollable]="true" scrollHeight="flex"
        [resizableColumns]="true" showGridlines stripedRows
        [tableStyle]="{'min-width': '1000px; min-height: calc(100% + 300px)'}" [columns]="selectedColumns()">

        <ng-template pTemplate="caption">
          <div class="flex flex-column md:flex-row align-items-center md:justify-content-between gap-3 p-0">
            <!-- Search Row - Full width on all screens -->
            <div class="flex align-items-center">
               <div class="field" style="width: 300px">
              <div class="search-input-container">
                <input type="text" pInputText id="searchTerm" styleClass="w-full"
                  [value]="queryParams().searchTerm || ''" (input)="updateSearchTerm($event)"
                  placeholder="Search teacher change requests" />
                <button *ngIf="queryParams().searchTerm" class="search-clear-button" type="button"
                  (click)="clearSearchTerm()">
                  <i class="pi pi-times"></i>
                </button>
              </div>
            </div>
            </div>

            <!-- Actions Row - Responsive layout -->
            <div class="flex flex-column sm:flex-row align-items-stretch sm:align-items-center justify-content-between gap-3">
              <!-- Column Selection and Export - Stack on mobile, inline on desktop -->
              <div class="flex flex-column sm:flex-row align-items-stretch sm:align-items-center gap-2 sm:gap-3">
            
                <p-multiSelect
                [appendTo]="'body'"
                  [options]="availableColumns()"
                  [ngModel]="selectedColumns()"
                  (ngModelChange)="onColumnsChange($event)"
                  [filter]="true"
                  optionLabel="header"
                  placeholder="Choose Columns"
                  [selectedItemsLabel]="'{0}/' + availableColumns().length + ' columns shown'"
                  scrollHeight="400px"
                  class="w-full sm:w-auto" />
                <p-button
                  icon="pi pi-download"
                  label="Export"
                  severity="secondary"
                  (click)="exportTable()"
                  class="p-button-sm w-full sm:w-auto" />
              </div>
            </div>
          </div>
        </ng-template>

        <!-- Table Header -->
      <ng-template pTemplate="header">
        <app-data-grid-header-footer
          [columns]="selectedColumns()"
          [sortable]="true"
          [reorderable]="true"
          [showHeader]="true"
          [showFooter]="false"
          [showActionsColumn]="false"
          [showExpansionColumn]="false">
        </app-data-grid-header-footer>
      </ng-template>

      <!-- Table Body -->
      <ng-template pTemplate="body" let-request>
        <tr>
          <td *ngFor="let col of selectedColumns()" [ngClass]="'col-' + col.field">
            <!-- Request ID Column -->
            <ng-container *ngIf="col.field === 'requestId'">
              <span class="font-medium text-primary">{{ request.requestId }}</span>
            </ng-container>

            <!-- Parent Name Column -->
            <ng-container *ngIf="col.field === 'parentFirstName'">
              <div class="flex flex-column gap-1">
                <span class="font-medium">{{ getFullName(request.parentFirstName, request.parentLastName) }}</span>
                <small class="text-color-secondary">{{ request.parentEmail }}</small>
              </div>
            </ng-container>

            <!-- Current Teacher Column -->
            <ng-container *ngIf="col.field === 'teacherToChangeFirstName'">
              <div class="flex flex-column gap-1" *ngIf="request.teacherToChangeFirstName || request.teacherToChangeLastName">
                <span class="font-medium">{{ getFullName(request.teacherToChangeFirstName, request.teacherToChangeLastName) }}</span>
                <small class="text-color-secondary" *ngIf="request.teacherToChangeTeacherEmail">{{ request.teacherToChangeTeacherEmail }}</small>
              </div>
              <span *ngIf="!request.teacherToChangeFirstName && !request.teacherToChangeLastName" class="text-color-secondary">-</span>
            </ng-container>

            <!-- New Teacher Column -->
            <ng-container *ngIf="col.field === 'newTeacherFirstName'">
              <div class="flex flex-column gap-1" *ngIf="request.newTeacherFirstName || request.newTeacherLastName">
                <span class="font-medium">{{ getFullName(request.newTeacherFirstName, request.newTeacherLastName) }}</span>
              </div>
              <span *ngIf="!request.newTeacherFirstName && !request.newTeacherLastName" class="text-color-secondary">-</span>
            </ng-container>

            <!-- Student Column -->
            <ng-container *ngIf="col.field === 'studentFirstName'">
              <span *ngIf="request.studentFirstName || request.studentLastName" class="font-medium">
                {{ getFullName(request.studentFirstName, request.studentLastName) }}
              </span>
              <span *ngIf="!request.studentFirstName && !request.studentLastName" class="text-color-secondary">-</span>
            </ng-container>

            <!-- Group Column -->
            <ng-container *ngIf="col.field === 'groupName'">
              <span *ngIf="request.groupName" class="font-medium">{{ request.groupName }}</span>
              <span *ngIf="!request.groupName" class="text-color-secondary">-</span>
            </ng-container>

            <!-- Teaching Language Column -->
            <ng-container *ngIf="col.field === 'teachingLanguageName'">
              <span *ngIf="request.teachingLanguageName" class="font-medium">{{ request.teachingLanguageName }}</span>
              <span *ngIf="!request.teachingLanguageName" class="text-color-secondary">-</span>
            </ng-container>

            <!-- Reason Column -->
            <ng-container *ngIf="col.field === 'reason'">
              <span *ngIf="request.reason" [pTooltip]="request.reason" tooltipPosition="top" class="cursor-pointer">
                {{ request.reason.length > 50 ? (request.reason | slice:0:50) + '...' : request.reason }}
              </span>
              <span *ngIf="!request.reason" class="text-color-secondary">-</span>
            </ng-container>

            <!-- Date Created Column -->
            <ng-container *ngIf="col.field === 'dateCreated'">
              <span class="font-medium">{{ formatUtcDateToAdminLocalized(request.dateCreated) }}</span>
            </ng-container>

            <!-- Last Modified Date Column -->
            <ng-container *ngIf="col.field === 'lastModifiedDate'">
              <span *ngIf="request.lastModifiedDate" class="font-medium">
                {{ formatUtcDateToAdminLocalized(request.lastModifiedDate) }}
              </span>
              <span *ngIf="!request.lastModifiedDate" class="text-color-secondary">-</span>
            </ng-container>
          </td>
        </tr>
      </ng-template>

      <!-- Empty State -->
      <ng-template pTemplate="emptymessage">
        <tr>
          <td [attr.colspan]="selectedColumns().length" class="text-center py-6">
            <div class="flex flex-column align-items-center gap-3">
              <i class="pi pi-inbox text-4xl text-color-secondary"></i>
              <div class="text-xl font-medium text-color-secondary">No teacher change requests found</div>
              <div class="text-color-secondary">Try adjusting your search or filter criteria</div>
            </div>
          </td>
        </tr>
      </ng-template>
      </p-table>
    </div>
  </div>
</div>

<!-- Filters Drawer -->
<app-filters-drawer-sidebar
  #filtersDrawer
  [(visible)]="isFiltersDrawerVisible"
  [config]="filtersDrawerConfig"
  [filterContentTemplate]="filterContentTemplate"
  (actionClicked)="onFiltersDrawerAction($event)"
>
</app-filters-drawer-sidebar>

<!-- Filter Content Template -->
<ng-template #filterContentTemplate>
  <app-teacher-change-requests-list-filters
    #teacherChangeRequestsFilters
    [filterState]="filtersState()"
    [config]="filtersConfig()"
    (filterChanged)="onFilterChange($event)"
    (filterAction)="onFilterAction($event)"
  />
</ng-template>
