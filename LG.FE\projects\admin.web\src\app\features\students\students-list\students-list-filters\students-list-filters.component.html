<!-- Students List Filters -->
<div class="students-filters-container col-12">
  


  <!-- Teaching Language Section -->
  <div class="filter-section mb-4">
    <label class="block text-900 font-medium mb-2">
      <i class="pi pi-globe mr-2"></i>
      Learning Language
    </label>
    <p-select
      [options]="teachingLanguages()"
      [ngModel]="teachingLanguage()"
      (ngModelChange)="onTeachingLanguageChange($event)"
      optionLabel="name"
      optionValue="id"
      placeholder="Select teaching language"
      [showClear]="true"
      class="w-full">
    </p-select>
  </div>

  <!-- Teaching Language Level Section -->
  <div class="filter-section mb-4">
    <label class="block text-900 font-medium mb-2">
      <i class="pi pi-star mr-2"></i>
      Learning Language Level
    </label>
    <p-select
      [options]="teachingLanguageLevelOptions()"
      [ngModel]="teachingLanguageLevel()"
      (ngModelChange)="onTeachingLanguageLevelChange($event)"
      optionLabel="label"
      optionValue="value"
      placeholder="Select language level"
      [disabled]="isTeachingLanguageLevelDisabled()"
      [showClear]="true"
      class="w-full">
    </p-select>
  </div>

  <!-- Account Status Section -->
  <div class="filter-section mb-4">
    <label class="block text-900 font-medium mb-2">
      <i class="pi pi-user mr-2"></i>
      Account Status
    </label>
    <p-select
      [options]="accountStatusOptions()"
      [ngModel]="accountStatus()"
      (ngModelChange)="onAccountStatusChange($event)"
      optionLabel="label"
      optionValue="value"
      placeholder="Select account status"
      [showClear]="true"
      class="w-full">
    </p-select>
  </div>

  <!-- Gender Section -->
  <div class="filter-section mb-4">
    <label class="block text-900 font-medium mb-2">
      <i class="pi pi-users mr-2"></i>
      Gender
    </label>
    <p-select
      [options]="genderOptions()"
      [ngModel]="gender()"
      (ngModelChange)="onGenderChange($event)"
      optionLabel="label"
      optionValue="value"
      placeholder="Select gender"
      class="w-full">
    </p-select>
  </div>

  <!-- Student Ages Range Section -->
  <div class="filter-section mb-4">
    <label class="block text-900 font-medium mb-2">
      <i class="pi pi-calendar mr-2"></i>
      Student Age Range: {{ studentAgesRange()[0] }} - {{ studentAgesRange()[1] }} years
    </label>
    <p-slider
      [ngModel]="studentAgesRange()"
      (ngModelChange)="onStudentAgesRangeChange($event)"
      [range]="true"
      [min]="2"
      [max]="17"
      [step]="1"
      class="w-full">
    </p-slider>
  </div>

  <!-- Registration Date Range Section -->
  <div class="filter-section mb-4">
    <label class="block text-900 font-medium mb-2">
      <i class="pi pi-calendar-plus mr-2"></i>
      Registration Date Range
    </label>
    
    <div class="grid">
      <div class="col-12 md:col-6">
        <label class="block text-600 font-medium mb-1">From</label>
        <p-datePicker
          [ngModel]="registeredFrom()"
          (ngModelChange)="onRegisteredFromChange($event)"
          placeholder="Select start date"
          [showIcon]="true"
          [showClear]="true"
          class="w-full">
        </p-datePicker>
      </div>
      
      <div class="col-12 md:col-6">
        <label class="block text-600 font-medium mb-1">To</label>
        <p-datePicker
          [ngModel]="registeredTo()"
          (ngModelChange)="onRegisteredToChange($event)"
          placeholder="Select end date"
          [showIcon]="true"
          [showClear]="true"
          class="w-full">
        </p-datePicker>
      </div>
    </div>
  </div>

  <!-- Include Blocked Section -->
  <div class="filter-section mb-4">
    <div class="flex align-items-center">
      <p-checkbox
        [binary]="true"
        [ngModel]="includeBlocked()"
        (ngModelChange)="onIncludeBlockedChange($event)"
        inputId="includeBlocked">
      </p-checkbox>
      <label for="includeBlocked" class="ml-2 text-900 font-medium">
        <i class="pi pi-ban mr-2"></i>
        Include Blocked Students
      </label>
    </div>
  </div>

</div>
