import { CommonModule } from '@angular/common';
import { Component, computed, inject, Inject, OnInit, signal, ChangeDetectionStrategy } from '@angular/core';
import { RouterModule } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { OtpInputComponent } from '../../otp-input/otp-input.component';
import { ActionableAlertComponent } from '../../actionable-alert/actionable-alert.component';
import { PopupStateService } from '../../../services/popup-state.service';
import { HandleApiResponseService } from '../../../services/handle-api-response.service';
import { AuthStateService } from '../../../services/auth-state.service';
import { EventBusService } from '../../../services/event-bus.service';
import { ToastService } from '../../../services/toast.service';
import { IRequestOtpRequest, IProfileInfo } from '../../../GeneratedTsFiles';
import { getToastMessage, ToastMessages } from '../../../models/toast-messages';
import { EmitEvent, Events } from '../../../services/event-bus.service';
import { CustomDialogPopupComponent } from '../../custom-dialog-popup/custom-dialog-popup.component';

@Component({
  selector: 'app-request-otp-confirm-dialog',
  imports: [
    CommonModule,
    RouterModule,
    ButtonModule,
    CustomDialogPopupComponent,
    OtpInputComponent,
    ActionableAlertComponent,
  ],
  templateUrl: './request-otp-confirm-dialog.component.html',
  styleUrl: './request-otp-confirm-dialog.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RequestOtpConfirmDialogComponent implements OnInit {
  dialogVisible = signal(true);
  dialogStyleClass = signal('p-dialog p-dialog-md purple-dialog-header');
  dialogStyle = signal({ width: '40vw' });
  dialogHeader = computed(() => `Buy Included Text`);
  dialogStyle$ = computed(() => this.dialogStyle());
  parameters: any;
  pricePackage = signal({} as any);

  emailAddress = signal('');
  handleApiService = inject(HandleApiResponseService);
  popupStateService = inject(PopupStateService);
  authStateService = inject(AuthStateService);
  eventBusService = inject(EventBusService);
  toastService = inject(ToastService);
  canStartTimer = signal(true);
  sendOtpRequest = signal(true);

  user = computed(() => {
    return this.authStateService.getUserClaims();
  });

  constructor(@Inject('dialogParameters') parameters: any) {
    this.parameters = (parameters);
  }

  ngOnInit(): void {
    console.log(this.parameters);
    this.emailAddress.set(this.parameters.emailAddress);
    this.canStartTimer.set(this.parameters.canStartTimer ?? true);
    this.sendOtpRequest.set(this.parameters.sendOtpRequest ?? true);
    this.dialogStyleClass.set('p-dialog p-dialog-sm purple-dialog-header');
    this.dialogStyle.set({ width: '410px' });
    if (this.sendOtpRequest()) {
      this.otpRequest();
    }
  }

  closeDialog() {
    this.dialogVisible.set(false);
  }


  otpRequest(showResendToast = false) {
    // this.isLoading.set(true);
    this.handleApiService.getApiData<IRequestOtpRequest>(
      {
        url: IProfileInfo.postRequestOtpForNonPrimaryEmail,
        method: 'POST',
      },
      {
        'emailAddress': this.emailAddress()
      },
    ).subscribe({
      next: (response) => {
        // Add unique id to each packagePrice
        console.log(response);
        if (showResendToast) {
          this.toastService.show(getToastMessage(ToastMessages.OtpResend.success, {
            data: this.emailAddress()
          }));
          this.canStartTimer.set(true);
        }
        // this.isLoading.set(false);
      },
      error: (error) => console.error('Login failed:', error)
    });
  }

  onOtpCompleted(event: string) {
    console.log(event);
    if (!event) {
      return;
    }
    this.handleApiService.getApiData<any>(
      {
        url: IProfileInfo.postVerifyOtpForNonPrimaryEmail,
        method: 'POST',
      },
      {
        'otp': event,
        'emailAddress': this.emailAddress()
      },
    ).subscribe({
      next: (response) => {
        // Add unique id to each packagePrice
        console.log(response);
        // this.isLoading.set(false);
        this.onDialogVisibleChange(false);
        const userId = this.user().id;
        if (userId) {
          this.eventBusService.emit(new EmitEvent(Events.StateLoadProfileInfo, 
            {
              userId: this.user().id,
            }

          ));
        }
        this.toastService.show(getToastMessage(ToastMessages.ConfirmEmailAddress.success, {
          data: this.emailAddress()
        }));
      },
      error: (error) => {
        this.toastService.show(getToastMessage(ToastMessages.ConfirmEmailAddress.error, {
          data: this.emailAddress()
        })); 
      }
    });
  }

  onDialogVisibleChange(isShown: boolean) {
    console.log(this.parameters);
    if (!isShown) {
      this.popupStateService.clearPopupState(this.parameters.dialogId);
      // this.popupStateService.clearAllPopupStates();
      this.closeDialog();
    }
  }
}