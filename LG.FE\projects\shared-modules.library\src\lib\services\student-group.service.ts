import { Injectable } from '@angular/core';
import { Severity } from '../models/severity';
import { IAvailabilityDto, IBasicProfileInfoDto, ICreateStudentGroupRequest, ILanguageLevelsEnum, IRegisterStudentToTeachingLanguageDto, ISearchStudentDto, IStudentGroupDto, IStudentTeachingLanguageDto, ITeachingLanguageDto } from '../GeneratedTsFiles';


export enum StoudentGroupRuleType {
  Error = 'error',
  Warning = 'warning',
  Info = 'info',
}

export interface StoudentGroupRuleMessage {
  type: Severity;
  message: string;
}

export interface StoudentGroupRule {
  type: Severity;
  message: string;
  validate: (existingGroups: IStudentGroupDto[], newGroup: IStudentGroupDto, isEditMode: boolean) => boolean;
}

@Injectable({
  providedIn: 'root'
})
export class StudentGroupService {

  currentGroupStudents: ISearchStudentDto[] = []; // Students currently in the group
  studentsToAdd: string[] = []; // IDs of students to add
  studentsToRemove: string[] = []; // IDs of students to remove

  private createStudentGroupRequest: ICreateStudentGroupRequest = {} as ICreateStudentGroupRequest;
  private editStudentGroupRequest: { availability: IAvailabilityDto } = { availability: {} as IAvailabilityDto };

  private rules: StoudentGroupRule[] = [
    {
      type: Severity.Info,
      message: 'At least two students must be selected to create a group class.',
      validate: (existingGroups: IStudentGroupDto[], newGroup: IStudentGroupDto) => {
        console.log(newGroup);
        return newGroup.studentInfo!.length < 2
      },
    },
    {
      type: Severity.Danger,
      message: 'A group with the same language and level already exists for the selected students.',
      validate: (existingGroups: IStudentGroupDto[], newGroup: IStudentGroupDto) => {

        return this.groupExists(existingGroups, newGroup);
      }
      ,
    },
    {
      type: Severity.Warning,
      message: '',
      validate: (existingGroups: IStudentGroupDto[], newGroup: IStudentGroupDto, isEditMode: boolean) => {
        if (isEditMode) {
          // Get the IDs of students already in the group being edited
          // Create a set of existing student IDs in the current group
          const existingStudentIds = new Set(this.currentGroupStudents.map(student => student.userId));

          console.log(existingStudentIds)
          // Filter to find new students being added (those not already in the current group)
          const newStudentsToAdd = newGroup.studentInfo!.filter((student: IBasicProfileInfoDto) => !existingStudentIds.has(student.userId));

          // Create a temporary object to match the IStudentGroupDto interface
          const tempStudentGroup: IStudentGroupDto = {
            id: '', // Provide a temporary ID if needed
            groupName: newGroup.groupName,
            groupLevel: newGroup.groupLevel,
            groupStatus: 1,
            studentInfo: newStudentsToAdd,
            studentLevelEnum: newGroup.studentLevelEnum,
            availabilityId: '',
            teachingLanguageId: newGroup.teachingLanguageId,
            teachingLanguageName: newGroup.teachingLanguageName,
          };

          // Check for overlapping students in the other groups
          const overlappingNames = this.studentsBelongToOtherGroupWithSameLanguage(existingGroups, tempStudentGroup);
          // Check for overlapping students in the other groups, excluding existing students
          const newStudentsToCheck = newGroup.studentInfo!.filter((student: IBasicProfileInfoDto) => !existingStudentIds.has(student.userId));
          console.error(this.currentGroupStudents)
          console.error(newStudentsToCheck)
          if (overlappingNames.length > 0) {
            this.rules[2].message = `Some selected students (${overlappingNames.join(', ')}) are already in another group for the same language.`;
            return true; // Warning condition met
          }
          return false; // No overlapping students
        } else {
          // Original logic for new groups
          const overlappingNames = this.studentsBelongToOtherGroupWithSameLanguage(existingGroups, newGroup);
          if (overlappingNames.length > 0) {
            this.rules[2].message = `Some selected students (${overlappingNames.join(', ')}) are already in another group for the same language.`;
            return true; // Warning condition met
          }
          return false; // No overlapping students
        }
      },
    },
    {
      type: Severity.Danger,
      message: 'This group is identical to an existing group (same language, level, and students).',
      validate: (existingGroups: IStudentGroupDto[], newGroup: IStudentGroupDto, isEditMode: boolean) => {
        if (isEditMode) {
          // Skip this validation in edit mode since we're editing an existing group
          return false;
        }
        return existingGroups.some(existingGroup =>
          existingGroup.groupName === newGroup.groupName &&
          existingGroup.groupLevel === newGroup.groupLevel &&
          this.hasSameStudents(existingGroup.studentInfo!, newGroup.studentInfo!) &&
          existingGroup.id !== newGroup.id // Ensure we're not comparing the group with itself if it has an ID
        );
      },
    },
  ];

  constructor() {

    this.createStudentGroupRequest = {
      parentId: '',
      teachingLanguageId: '',
      studentsToAdd: [],
      availabilityDto: {} as IAvailabilityDto
    };

    this.editStudentGroupRequest = {
      availability: {} as IAvailabilityDto
    }
  }


  // Call this method to initialize the current group students
  setCurrentGroupStudents(students: ISearchStudentDto[]) {
    this.currentGroupStudents = students;
  }

  onStudentSelected(
    event: ISearchStudentDto[],
    preselectedStudents: ISearchStudentDto[] = []
  ) {
    // Initialize the current group students
    this.setCurrentGroupStudents(preselectedStudents);

    // Reset arrays to track changes
    this.studentsToAdd = [];
    this.studentsToRemove = [];

    const selectedStudentIds = event.map((element) => element.userId);
    const preselectedStudentIds = preselectedStudents.map(student => student.userId);

    selectedStudentIds.forEach(studentId => {
      if (!preselectedStudentIds.includes(studentId)) {
        this.selectStudent(studentId);
      }
    });

    preselectedStudentIds.forEach(studentId => {
      if (!selectedStudentIds.includes(studentId)) {
        this.deselectStudent(studentId);
      }
    });

    // Debugging logs
    console.log('Students to add:', this.studentsToAdd);
    console.log('Students to remove:', this.studentsToRemove);
  }


  private selectStudent(studentId: string) {
    if (!this.currentGroupStudents.some(student => student.userId === studentId) &&
      !this.studentsToAdd.includes(studentId)) {
      this.studentsToAdd.push(studentId);
    }
    this.studentsToRemove = this.studentsToRemove.filter(id => id !== studentId);
  }

  private deselectStudent(studentId: string) {
    if (this.currentGroupStudents.some(student => student.userId === studentId) &&
      !this.studentsToRemove.includes(studentId)) {
      this.studentsToRemove.push(studentId);
    }
    this.studentsToAdd = this.studentsToAdd.filter(id => id !== studentId);
  }


  /**
  * Finds a group object by its group ID.
  * @param existingGroups - Array of existing groups.
  * @param groupId - The ID of the group to find.
  * @returns The group object if found, otherwise null.
  */
  findGroupById(existingGroups: IStudentGroupDto[], groupId: string): IStudentGroupDto {
    return existingGroups.find(group => group.id === groupId) as IStudentGroupDto; // Assuming group has an 'id' property
  }

  /**
    * Checks if a student ID exists in a given group of students.
    * @param studentId - The ID of the student to check.
    * @param groupStudents - The array of students representing the group.
    * @returns True if the student ID exists in the group; otherwise, false.
    */
  studentExistsInGroup(studentId: string, groupStudents: ISearchStudentDto[]): boolean {
    console.log("🚀 ~ StudentGroupService ~ studentExistsInGroup ~ groupStudents:", groupStudents)
    if (!groupStudents) {
      return false;
    }
    return groupStudents.some(student => student.userId === studentId);
  }

  /**
   * Checks if a group with the same language, level, and students already exists.
   * @param existingGroups - An array of existing student groups.
   * @param newGroup - The new group to check for existence.
   * @returns True if a matching group exists, false otherwise.
   */
  groupExists(existingGroups: IStudentGroupDto[], newGroup: IStudentGroupDto): boolean {
    return existingGroups.some(existingGroup =>
      existingGroup.groupName === newGroup.groupName &&
      existingGroup.groupLevel === newGroup.groupLevel &&
      this.hasSameStudents(existingGroup.studentInfo!, newGroup.studentInfo!)
    );
  }

  /**
   * Helper function to compare two arrays of basic profile info DTOs by their userId.
   * Returns true if both arrays contain the same students (order does not matter).
   * @param students1 - The first array of basic profile info DTOs.
   * @param students2 - The second array of basic profile info DTOs.
   * @returns True if the arrays contain the same students based on userId, false otherwise.
   */
  private hasSameStudents(students1: IBasicProfileInfoDto[], students2: IBasicProfileInfoDto[]): boolean {
    if (students1.length !== students2.length) return false;

    // Get the sorted arrays of student user IDs
    const studentUserIds1 = students1.map(student => student.userId).sort();
    const studentUserIds2 = students2.map(student => student.userId).sort();

    // Check if every ID in the first list matches the corresponding ID in the second
    return studentUserIds1.every((id, index) => id === studentUserIds2[index]);
  }

  studentsBelongToOtherGroupWithSameLanguage(existingGroups: IStudentGroupDto[], newGroup: IStudentGroupDto): string[] {
    const overlappingNames: string[] = [];
    console.log("🚀 ~ StudentGroupService ~ studentsBelongToOtherGroupWithSameLanguage ~ studentsBelongToOtherGroupWithSameLanguage:",
      existingGroups, newGroup)

    existingGroups.forEach(existingGroup => {
      if (existingGroup.groupName === newGroup.groupName) { // Same language
        const overlappingStudents = this.getOverlappingStudentNames(existingGroup.studentInfo!, newGroup.studentInfo!);
        overlappingNames.push(...overlappingStudents);
      }
    });

    return overlappingNames; // Return array of names of overlapping students
  }

  /**
   * Helper function to get names of overlapping students between two groups.
   * @param students1 - The first array of basic profile info DTOs.
   * @param students2 - The second array of basic profile info DTOs.
   * @returns An array of first names of overlapping students.
   */
  private getOverlappingStudentNames(students1: IBasicProfileInfoDto[], students2: IBasicProfileInfoDto[]): string[] {
    const studentIds1 = new Set(students1.map(student => student.userId));
    return students2
      .filter(student => studentIds1.has(student.userId))
      .map(student => student.firstName); // Assuming students have a firstName property
  }

  /**
   * Maps a StudentBasicInfoDto to a simplified object with only the necessary properties to display in the student selection list.
   * @param student The student to map.
   * @returns A simplified object with the student's id, first and last name, timezone IANA, and timezone display name.
   */

  mapStudentToBasicInfo(student: ISearchStudentDto) {
    return {
      id: student.userId,
      firstName: student.firstName,
      lastName: student.lastName,
      timezoneIana: student.timeZoneIana,
      timezoneDisplayName: student.timeZoneDisplayName
    };
  }
  /**
  * Validates the selected students against all defined rules.
  * Returns an array of rule messages that were violated.
  * @param existingGroups - Existing student groups to validate against.
  * @param newGroup - The new group being considered for creation or edit.
  * @param isEditMode - A boolean indicating if the operation is in edit mode.
  * @returns An array of rule messages that were violated.
  */
  validateSelectedStudents(existingGroups: IStudentGroupDto[], newGroup: IStudentGroupDto, isEditMode: boolean = false): StoudentGroupRuleMessage[] {
    const messages: StoudentGroupRuleMessage[] = [];

    console.log(newGroup);
    // The existingStudentIds here refers to the students *within the newGroup object*, not global existing students.
    // This is used for internal logic within the rules, such as identifying new students being added in edit mode.
    const newGroupStudentUserIds = new Set(newGroup.studentInfo!.map((student: IBasicProfileInfoDto) => student.userId));

    // Check all rules when not in edit mode
    for (const rule of this.rules) {
      console.log(existingGroups);
      if (rule.validate(existingGroups, newGroup, isEditMode)) {
        messages.push({
          type: rule.type,
          message: rule.message,
        });
      }
    }

    return messages;
  }

  getTeachingLanguageObjectFromName(teachingLanguages: ITeachingLanguageDto[], teachingLanguageName: string) {
    const selectedLanguageObject = teachingLanguages.find(language => language.name === teachingLanguageName);

    return selectedLanguageObject;
  }


  // Sort language levels based on studentToTeachingLanguages
  sortLanguageLevels(languageLevels: { name: string, code: ILanguageLevelsEnum }[], students: ISearchStudentDto[]): any[] {
    const languageLevelCodes = new Set<number>();
    students.forEach(student => {
      student.studentTeachingLanguageDto!.forEach((lang: IStudentTeachingLanguageDto) => {
        // Ensure lang.languageLevel is a number before adding to the Set
        // Assuming ILanguageLevelsEnum is a numeric enum (e.g., Beginner = 0)
        // And lang.languageLevel from the DTO might be a string like "Beginner"
        let levelToAdd: ILanguageLevelsEnum | undefined;

        if (typeof lang.languageLevel === 'string') {
          // If it's a string, try to map it to the numeric enum value
          levelToAdd = ILanguageLevelsEnum[lang.languageLevel as keyof typeof ILanguageLevelsEnum];
        } else if (typeof lang.languageLevel === 'number') {
          // If it's already a number, use it directly
          levelToAdd = lang.languageLevel;
        }

        if (typeof levelToAdd === 'number' && !isNaN(levelToAdd)) { // Ensure it's a valid number from the enum
          languageLevelCodes.add(levelToAdd);
        } else {
          console.warn(`[StudentGroupService] Invalid language level received for student: ${lang.languageLevel}`);
        }
      });
    });

    console.log('[StudentGroupService] Available language level codes from students:', Array.from(languageLevelCodes)); // Debugging
    console.log('[StudentGroupService] All possible levels for comparison:', languageLevels); // Debugging

    return languageLevels.map(level => ({
      ...level,
      disabled: !languageLevelCodes.has(level.code)
    })).sort((a, b) => {
      const aIncluded = !a.disabled;
      const bIncluded = !b.disabled;
      if (aIncluded && !bIncluded) return -1;
      if (!aIncluded && bIncluded) return 1;
      return 0;
    });
  }

  sortTeachingLanguages(teachingLanguages: ITeachingLanguageDto[], students: ISearchStudentDto[]): any[] {
    const teachingLanguageIds = new Set<string>();
    students.forEach(student => {
      student.studentTeachingLanguageDto!.forEach((lang: IStudentTeachingLanguageDto) => teachingLanguageIds.add(lang.teachingLanguageId));
    });

    return teachingLanguages.map(language => ({
      ...language,
      disabled: !teachingLanguageIds.has(language.id!),
      selected: false,
    }))
      .sort((a, b) => {
        const aIncluded = !a.disabled;
        const bIncluded = !b.disabled;
        if (aIncluded && !bIncluded) return -1;
        if (!aIncluded && bIncluded) return 1;
        return 0;
      });
  }


  /**
   * Sorts students by disabled and match properties.
   * A disabled student comes after a non-disabled one.
   * A student with a match comes after a student without a match.
   * @param a The first student to compare.
   * @param b The second student to compare.
   * @returns A negative value if a should come first, a positive value if b should come first, and 0 if the order does not matter.
   */
  sortStudents(a: any, b: any): number {
    if (!a.disabled && b.disabled) return -1;
    if (a.disabled && !b.disabled) return 1;
    if (a.match && !b.match) return -1;
    if (!a.match && b.match) return 1;
    return 0;
  }

  // Method to get the current student group request
  getCreateStudentGroupRequest(): ICreateStudentGroupRequest {
    return { ...this.createStudentGroupRequest }; // Return a copy to prevent direct modification
  }

  // Method to set parent ID
  addStudentGroupRequestParentId(parentId: string): void {
    this.createStudentGroupRequest.parentId = parentId;
  }

  addCreateStudentGroupRequestStudents(studentIds: string[]): void {
    studentIds.forEach(studentId => {
      if (!this.createStudentGroupRequest.studentsToAdd.includes(studentId)) {
        this.createStudentGroupRequest.studentsToAdd.push(studentId);
      }
    });
  }

  // Method to update any key in the studentGroupRequest
  updateCreateStudentGroupRequest(partialRequest: Partial<ICreateStudentGroupRequest>): void {
    this.createStudentGroupRequest = {
      ...this.createStudentGroupRequest,
      ...partialRequest
    };
  }


  // Method to update language details
  updateStudentGroupRequestLanguageDetails(languageDto: IRegisterStudentToTeachingLanguageDto): void {
    // this.studentGroupRequest.registerStudentToTeachingLanguageDto = {
    //   ...this.studentGroupRequest.registerStudentToTeachingLanguageDto,
    //   ...languageDto
    // };
  }

  // Optional: Method to reset the request
  resetStudentGroupRequest(): void {
    this.createStudentGroupRequest = {
      parentId: '',
      studentsToAdd: [],
      teachingLanguageId: '',
      availabilityDto: {} as IAvailabilityDto
    };
  }

  updateEditStudentGroupRequest(partialRequest: Partial<{ availability: IAvailabilityDto }>): void {
    this.editStudentGroupRequest = {
      ...this.editStudentGroupRequest,
      availability: {
        ...this.editStudentGroupRequest.availability,
        ...(partialRequest.availability || {})
      }
    };
  }

  getEditStudentGroupRequest(): { availability: IAvailabilityDto } {
    return { ...this.editStudentGroupRequest }; // Return a copy to prevent direct modification
  }

  resetEditStudentGroupRequest(): void {
    this.editStudentGroupRequest = {
      availability: {} as IAvailabilityDto
    }
  }
}