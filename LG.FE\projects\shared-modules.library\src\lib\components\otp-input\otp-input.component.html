@if (requestOtpTitle()) {
    <div class="flex flex-column align-items-center justify-content-center">
        <h2 class="primary-purple-color my-1">{{ requestOtpTitle() }}</h2>
    </div>
}

<p class=" text-center align-items-center justify-content-center text-800">
    Please enter the secure 6-digit code we have sent to
    <b>{{ emailAddress() }}</b>. 
    <br>
    <b>Check your spam mail if it's not in your inbox.</b>
    
</p>

<div class="flex flex-column align-items-center">
    <p-inputOtp [(ngModel)]="value" [integerOnly]="true" [ngModelOptions]="{ standalone: true }" [length]="digitsSize"
        (onChange)="onOtpChange($event)"></p-inputOtp>
</div>

<div *ngIf="isResendDisabled() && canStartTimer()" class="mt-5 flex text-center align-items-center justify-content-center text-800">
    Resend code in {{ display() }}
</div>

<div class="flex flex-column sm:flex-row text-center align-items-center justify-content-center mt-2">
    <span class="text-gray-500">Not received your code?</span> <p-button label="Resend Code" [link]="true" 
    [disabled]="isResendDisabled() && resendDisabled()"
     (click)="resend()"></p-button>
</div>

<div class="w-full">
<app-form-field-validation-message [severity]="Severity.Danger" styleClass="mb-3 w-full" messageClass="mb-3"
    [text]="generalService.errorDataSignal"></app-form-field-validation-message>
</div>

@if (showSubmitButton()){

    <p-button [rounded]="true" class=" gradient-purple-btn"
    styleClass="w-full" icon="pi pi-chevron-right" iconPos="right"
              label="Submit" role="button"
              [disabled]="!this.isValidInputs()" (click)="onOtpCompleted(value()!)"
              type="button"></p-button>

}
<!-- 
<p class="flex text-800">
    {{display()}}
    <br>

    {{counter}}
</p> -->