<!-- user-info.component.html -->
<!-- {{profileInfo() | json}} -->

<div [class]="cardClass() + ' basic_info__container'" [appLoader]="generalService.divLoading()">
    <!-- Header with improved styling -->
    <div class="flex justify-content-between align-items-center border-bottom-1 pb-3 surface-border mb-3">
        <div class="flex align-items-center gap-2  text-lg lg:text-xl">
            <i class="pi pi-user-edit text-primary"></i>
            <span class="text-900 font-medium">Profile Settings</span>
        </div>
        @if (showEditButton()) {
            @if (isEditing()) {
                <p-button (click)="onEditProfileClick()" label="Edit Profile" icon="pi pi-pencil"
                    styleClass="p-button-rounded p-button-outlined" />
                }@else {
                <p-button (click)="onEditProfileClick()" label="Cancel edit" icon="pi pi-times" severity="warn"
                    styleClass="p-button-rounded p-button-outlined" />
                }
        }
     
    </div>
    @if (showSettings()) {
    @if (profileInfo() && profileInfo().basicProfileInfoDto) {
    <div>
        <app-settings-user-info-form [mainTitle]="mainTitle" [showTitleButton]="showTitleButton"
        [userRole]="finalRole()"
            [userInfo]="profileInfo()" [isParentEditingStudent]="isParentEditingStudent()"
            (titleButtonClick)="onEditProfileClick()" (formSubmitted)="onEditProfileClick()">
        </app-settings-user-info-form>
    </div>
    }


    } @else {


    <!-- Student notice with improved styling -->
    @if (authService.isStudent()){
    <app-actionable-alert iconUrl="/assets/images/graphic/card_id.svg" title="Account Info Notice"
        message="Your details can only be changed through your parent's account settings."
        alertClass="bg-cyan-50 my-3 border-round-xl shadow-1 border-left-3 border-cyan-500" imageClass="''"
        alertTextHeaderClass="text-cyan-900 font-medium" alertTextSubHeaderClass="text-cyan-800">
    </app-actionable-alert>
    }

    @if (profileInfo().basicProfileInfoDto) {
    <!-- Profile sections with visual grouping -->
    <div class="mb-3">


        <div class="mb-5">
            <div class="flex align-items-center mb-3">
                <span
                    class="inline-flex align-items-center justify-content-center bg-primary-100 border-round mr-3 p-2">
                    <i class="pi pi-user text-primary text-xl"></i>
                </span>
                <span class="font-medium text-lg text-primary-900">Personal Information</span>
            </div>
            <div class="grid formgrid p-fluid">
                <div class="col-12 md:col-6 mb-3">
                    <div class="p-2 border-round-lg surface-50 h-full">
                        <div class="text-500 text-sm mb-1">First Name</div>
                        <div class="text-900 font-medium">{{ userInfo$().basicProfileInfoDto?.firstName || '-' }}</div>
                    </div>
                </div>
                <div class="col-12 md:col-6 mb-3">
                    <div class="p-2 border-round-lg surface-50 h-full">
                        <div class="text-500 text-sm mb-1">Last Name</div>
                        <div class="text-900 font-medium">{{ userInfo$().basicProfileInfoDto?.lastName || '-' }}</div>
                    </div>
                </div>
                @if (this.permissionService.hasPermission(this.authService.getUserClaims(), ['settings', 'zoom',
                'view'])) {
                <div class="col-12 md:col-6 mb-3">
                    <div class="p-2 border-round-lg surface-50 h-full">
                        <div class="text-500 text-sm mb-1">Primary Email</div>
                        <div class="text-900 font-medium">
                            @for (email of userInfo$().emails; track email.email) {
                            @if (email.isPrimary) {
                            {{ email.email }}
                            }
                            }
                        </div>
                    </div>
                </div>
                }
                <div class="col-12 md:col-6 mb-3">
                    <div class="p-2 border-round-lg surface-50 h-full">
                        <div class="text-500 text-sm mb-1">Date of Birth</div>
                        <div class="text-900 font-medium">{{ userInfo$().basicProfileInfoDto?.dateOfBirth ?
                            (userInfo$().basicProfileInfoDto.dateOfBirth | date:'dd/MM/yyyy') : '-' }}</div>
                    </div>
                </div>
                <div class="col-12 md:col-6 mb-3">
                    <div class="p-2 border-round-lg surface-50 h-full">
                        <div class="text-500 text-sm mb-1">Gender</div>
                        <div class="text-900 font-medium">{{
                        this.generalService.getEnumName(IGenderEnum, userInfo$().basicProfileInfoDto?.gender!)  || '-' }}</div>
                    </div>
                </div>
                <div class="col-12 md:col-6 mb-3">
                    <div class="p-2 border-round-lg surface-50 h-full">
                        <div class="text-500 text-sm mb-1">Timezone</div>
                        <div class="text-900 font-medium">{{ userInfo$().basicProfileInfoDto?.timeZoneIana || '-' }}
                        </div>
                    </div>
                </div>
                <div class="col-12 md:col-6 mb-3">
                    <div class="p-2 border-round-lg surface-50 h-full">
                        <div class="text-500 text-sm mb-1">Phone Number</div>
                        <div class="text-900 font-medium">
                            @if (userInfo$().mobileNumberDto && userInfo$().mobileNumberDto.mobileNumber) {
                            ({{ userInfo$().mobileNumberDto.dialCodeData.dialCode }}) {{
                            userInfo$().mobileNumberDto.mobileNumber }}
                            } @else {
                            -
                            }
                        </div>
                    </div>
                </div>
                @if (this.permissionService.hasPermission(this.authService.getUserClaims(), ['settings', 'zoom',
                'view'])) {
                <div class="col-12 md:col-6 mb-3">
                    <div class="p-2 border-round-lg surface-50 h-full">
                        <div class="text-500 text-sm mb-1">Zoom Account Email or ID</div>
                        <div class="text-900 font-medium">{{ userInfo$().basicProfileInfoDto?.zoom || '-' }}</div>
                    </div>
                </div>
                }

                @if (this.permissionService.hasPermission(this.authService.getUserClaims(), ['settings',
                'msTeamsEmailAddress', 'view'])) {
                <div class="col-12 md:col-6 mb-3">
                    <div class="p-2 border-round-lg surface-50 h-full">
                        <div class="text-500 text-sm mb-1">Microsoft Teams Email Address</div>
                        <div class="text-900 font-medium">{{ userInfo$().basicProfileInfoDto?.msTeamsEmailAddress || '-'
                            }}</div>
                    </div>
                </div>
                }
                <div class="col-12 md:col-12 mb-3">
                    <div class="p-2 border-round-lg surface-50 h-full">
                        <div class="text-500 text-sm mb-1">Additional Emails</div>
                        <div class="text-900 font-medium">
                            @for (email of userInfo$().emails; track email.email) {
                            @if (!email.isPrimary) {
                            <div class="flex align-items-center">
                                <span>{{ email.email }}</span>
                                <span class="ml-auto flex align-items-center">
                                    @if (email.isConfirmed) {
                                    <span class="pi pi-check-circle text-green-500 mr-2"></span>
                                    Verified
                                    } @else {
                                    <span class="pi pi-times-circle text-red-500 mr-2"></span>
                                    Not Verified
                                    }
                                </span>
                            </div>
                            }
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mb-5">
            <div class="flex align-items-center mb-3">
                <span
                    class="inline-flex align-items-center justify-content-center bg-primary-100 border-round mr-3 p-2">
                    <i class="pi pi-home text-primary text-xl"></i>
                </span>
                <span class="font-medium text-lg text-primary-900">Address Information</span>
            </div>
            <div class="grid formgrid p-fluid">
                <div class="col-12 md:col-6 mb-3">
                    <div class="p-2 border-round-lg surface-50 h-full">
                        <div class="text-500 text-sm mb-1">Address Line 1</div>
                        <div class="text-900 font-medium">{{ userInfo$().userAddress?.addressLine1 || '-' }}</div>
                    </div>
                </div>
                <div class="col-12 md:col-6 mb-3">
                    <div class="p-2 border-round-lg surface-50 h-full">
                        <div class="text-500 text-sm mb-1">Address Line 2</div>
                        <div class="text-900 font-medium">{{ userInfo$().userAddress?.addressLine2 || '-' }}</div>
                    </div>
                </div>
                <div class="col-12 md:col-6 mb-3">
                    <div class="p-2 border-round-lg surface-50 h-full">
                        <div class="text-500 text-sm mb-1">City</div>
                        <div class="text-900 font-medium">{{ userInfo$().userAddress?.city || '-' }}</div>
                    </div>
                </div>
                <div class="col-12 md:col-6 mb-3">
                    <div class="p-2 border-round-lg surface-50 h-full">
                        <div class="text-500 text-sm mb-1">State/County</div>
                        <div class="text-900 font-medium">{{ userInfo$().userAddress?.state || '-' }}</div>
                    </div>
                </div>
                <div class="col-12 md:col-6 mb-3">
                    <div class="p-2 border-round-lg surface-50 h-full">
                        <div class="text-500 text-sm mb-1">Post Code</div>
                        <div class="text-900 font-medium">{{ userInfo$().userAddress?.postCode || '-' }}</div>
                    </div>
                </div>
                <div class="col-12 md:col-6 mb-3">
                    <div class="p-2 border-round-lg surface-50 h-full">
                        <div class="text-500 text-sm mb-1">Country</div>
                        <div class="text-900 font-medium">{{ userInfo$().userAddress?.country || '-' }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @if (this.finalRole() === IUserRole.TEACHER) {
        <div class="mb-5">
            <div class="flex align-items-center mb-3">
                <span
                    class="inline-flex align-items-center justify-content-center bg-primary-100 border-round mr-3 p-2">
                    <i class="pi pi-comment text-primary text-xl"></i>
                </span>
                <span class="font-medium text-lg text-primary-900">Speaking Languages</span>
            </div>
            <div class="grid formgrid p-fluid">
                @if (userInfo$().basicProfileInfoDto?.speakingLanguages &&
                userInfo$().basicProfileInfoDto.speakingLanguages.length > 0) {
                @for (lang of userInfo$().basicProfileInfoDto.speakingLanguages; track lang.language) {
                <div class="col-12 md:col-6 mb-3">
                    <div class="p-2 border-round-lg surface-50 h-full">
                        <div class="text-500 text-sm mb-1">Language</div>
                        <div class="text-900 font-medium">
                            {{ lang.language }} ({{ lang.languageLevel || 'N/A' }})
                            @if (lang.isNative) {
                            <span
                                class="inline-flex align-items-center px-2 py-1 border-round-xl font-bold text-xs bg-cyan-100 text-cyan-800 ml-2">Native</span>
                            }
                        </div>
                    </div>
                </div>
                }
                } @else {
                <div class="col-12">
                    <p class="text-600">No speaking languages added.</p>
                </div>
                }
            </div>
        </div>
        }
    </div>
    }

    }

</div>