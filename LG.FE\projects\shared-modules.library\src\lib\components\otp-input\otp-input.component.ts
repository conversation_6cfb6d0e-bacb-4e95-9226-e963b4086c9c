
import { CommonModule } from '@angular/common';
import {
  Component,
  ElementRef,
  EventEmitter,
  inject,
  Injector,
  input,
  model,
  OnDestroy,
  OnInit,
  Output,
  QueryList,
  signal,
  ViewChildren
} from '@angular/core';
import {
  AbstractControl,
  FormArray,
  FormControl,
  FormsModule,
  ReactiveFormsModule,
  ValidationErrors,
  Validators
} from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { InputOtpModule } from 'primeng/inputotp';
import { Router } from '@angular/router';
import { UserService } from '../../services/user.service';
import { RegisterService } from '../../services/register.service';
import { FormFieldValidationMessageComponent } from '../prime/form-field-validation-message/form-field-validation-message.component';
import { AuthStateService } from '../../services/auth-state.service';
import { GeneralService } from '../../services/general.service';
import { Severity } from '../../models/severity';
import { untilDestroyed } from '../../helpers/until-destroyed';
import { EmitEvent, EventBusService, Events } from '../../services/event-bus.service';
import { IdentityRoutes, ILoginRequest, ILoginResponse, ILoginTypeEnum } from '../../GeneratedTsFiles';

interface GenericObject {
  [key: string]: unknown;
}

function getFormArray(size: number): FormArray {
  const arr = [];
  for (let i = 0; i < size; i++) {
    arr.push(new FormControl('', Validators.required));
  }
  return new FormArray(arr);
}

@Component({
  selector: 'app-otp-input',
  imports: [
    CommonModule,
    FormsModule,
    InputOtpModule,
    ReactiveFormsModule,
    ButtonModule,
    FormFieldValidationMessageComponent,
  ],
  templateUrl: './otp-input.component.html',
  styleUrl: './otp-input.component.scss'
})
export class OtpInputComponent implements OnInit, OnDestroy {
  emailAddress = input('');
  authService = inject(AuthStateService);
  userService = inject(UserService);
  registerService = inject(RegisterService);
  generalService = inject(GeneralService);
  eventBusService = inject(EventBusService);
  router = inject(Router);
  requestOtpTitle = input('Almost there! ');
  requestOtp = input(true);
  sendOtpOnLoad = input(true);
  showSubmitButton = input(true);
  canStartTimer = input(true);
  shouldSaveTimerKey = input(true);
  resendDisabled = input(false);
  requiresNewPasswordAfterOtp = input(false);
  timerValueSeconds = input(120); // Initial timer value in seconds
  isResendDisabled = model(false);
  @Output() otpCompleted = new EventEmitter<string>();
  @Output() otpResendRequested = new EventEmitter<unknown>();
  resendTimer: any = null;
  otpClear = model(false);
  private injector = inject(Injector);
  private isRequestingOtp = signal(false);

  Severity = Severity;

  digitsSize = 6;
  value = model<string | undefined>(undefined);
  display = signal('');
  @ViewChildren('inputEl') inputEls!: QueryList<ElementRef<HTMLInputElement>>;
  seconds = signal(this.timerValueSeconds());
  private untilDestroyed = untilDestroyed();
  private timerInterval: any;

  ngOnInit() {
    this.seconds.set(this.timerValueSeconds()); // Set initial timer value
    if (!this.sendOtpOnLoad()) {

    } else {
      this.restoreState(); // Restore timer and OTP state

      // Only request OTP if needed and not already in progress
      if (this.isOtpRequestNeeded() && !this.isRequestingOtp()) {
        this.onOtpRequestSend();
      } else if (this.isTimerStateInitial() && this.isOtpRequestNeeded()) {
        this.onOtpRequestSend();
      }
    }


    if (this.canStartTimer()) {
      this.startTimer(this.seconds()); // Start or resume timer
    }

    this.listenForOtpEvent();
  }

  private isTimerStateInitial(): boolean {
    const loginState = this.authService.getOtpResendTimerKey();
    const otpRequestedAt = loginState.otpRequestedAt || 0;
    const remainingSeconds = loginState.remainingSeconds || 0;

    // TimerState is initial if OTP has never been requested and there are no remaining seconds
    return otpRequestedAt === 0 && remainingSeconds === 0;
  }

  ngOnDestroy() {
    clearInterval(this.timerInterval);
    // this.saveTimerState(); // Save timer state before destruction
    this.generalService.setErrorDataSignal('');
    this.generalService.otpRequest$.next(false);
    this.generalService.otpClear$.next(false);
    if (this.canStartTimer()) {
        clearInterval(this.timerInterval);
        this.isResendDisabled.set(false);
        this.authService.removeOtpResendTimerKey();
    }
  }

  onOtpChange(event: { value: string }) {
    if (event.value.length === this.digitsSize) {
      this.otpCompleted.emit(event.value);
    }
  }

  onOtpCompleted(inputs: string) {
    const data: ILoginRequest = {
      password: inputs,
      googleIdToken: '',
      loginType: ILoginTypeEnum.Otp,
      emailAddress: this.getTrimmedEmailAddress(),
    };
    this.userService.login(data).pipe(this.untilDestroyed()).subscribe({
      next: (data: ILoginResponse) => {
        this.authService.handleUserDataAndDecodeJWT(data);
        this.registerService.clearRegisterParentRequest();
        const returnUrl = this.authService.getReturnUrl();
        this.eventBusService.emit(new EmitEvent(Events.UserLoggedIn,
          {
            user: this.authService.getUserClaims(),
            hasSetPassword: this.requiresNewPasswordAfterOtp(),
            redirectUrl: returnUrl || undefined
          } ));

      },
      error: (error) => {
        this.generalService.handleRouteError(error, IdentityRoutes.postLogin);
        this.isResendDisabled.set(false);
        this.value.set('');
      }
    });
  }

  resend() {
    this.generalService.setErrorDataSignal('');
    this.otpResendRequested.emit(true);
    if (this.sendOtpOnLoad()) {
      this.onOtpRequestSend(); // Request new OTP
    }
    clearInterval(this.timerInterval);
    this.startTimer(this.timerValueSeconds()); // Restart timer with initial value
  }

  private onOtpRequestSend() {
    if (this.isRequestingOtp()) return; // Prevent duplicate requests
    this.isRequestingOtp.set(true);

    const data: { emailAddress: string } = {
      emailAddress: this.getTrimmedEmailAddress() || this.registerService.getRegisterParentRequest().emailAddress?.trim() || '',
    };
    this.userService.requestOtp(data).pipe(this.untilDestroyed()).subscribe({
      next: () => {
        this.updateParentState(); // Update loginFormState with new timestamp
        this.startTimer(this.timerValueSeconds()); // Start timer after request
        this.isRequestingOtp.set(false); // Reset flag on success
      },
      error: (error) => {
        this.generalService.handleRouteError(error, IdentityRoutes.postRequestOtp);
        this.startTimer(this.timerValueSeconds()); // Retry timer even on error
        this.isRequestingOtp.set(false); // Reset flag on error
      }
    });
  }

  private updateParentState(): void {
    let loginState = this.authService.getOtpResendTimerKey();

    // If loginState is not an object (e.g., false), initialize it with defaults
    if (!loginState || typeof loginState !== 'object') {
      loginState = {
        otpRequestedAt: 0,
        remainingSeconds: 0
      };
    }

    // Update the timestamp
    loginState.otpRequestedAt = Date.now();

    // Save the updated state
    this.authService.setOtpResendTimerKey(loginState);
  }

  private isOtpRequestNeeded(): boolean {

    if (!this.requestOtp()) {
      return false;
    }

    const loginState = this.authService.getOtpResendTimerKey();
    const otpRequestedAt = loginState.otpRequestedAt || 0;
    const remainingSeconds = loginState.remainingSeconds || 0;

    // If OTP was never requested, request it
    if (otpRequestedAt === 0) {
      return true;
    }

    // Calculate elapsed time since OTP was requested
    const elapsedMs = Date.now() - otpRequestedAt;
    const timerDurationMs = this.timerValueSeconds() * 1000;

    // Request OTP only if the previous OTP has expired (elapsed time exceeds timer duration)
    // and there are no remaining seconds
    return elapsedMs >= timerDurationMs && remainingSeconds <= 0;
  }

  private restoreState(): void {
    const loginState = this.authService.getOtpResendTimerKey();
    const otpRequestedAt = loginState.otpRequestedAt || 0;
    const remainingSeconds = loginState.remainingSeconds || 0;

    if (otpRequestedAt > 0) {
      const elapsedMs = Date.now() - otpRequestedAt;
      const timerDurationMs = this.timerValueSeconds() * 1000;
      const remainingMs = timerDurationMs - elapsedMs;

      if (remainingMs > 0 && remainingSeconds > 0) {
        // Restore the timer with the remaining time if OTP is still valid
        this.seconds.set(Math.max(Math.floor(remainingMs / 1000), remainingSeconds));
        this.startTimer(this.seconds());
      } else {
        // OTP has expired, reset state and allow a new request
        this.seconds.set(this.timerValueSeconds());
        this.authService.removeOtpResendTimerKey();
      }
    } else {
      // No previous OTP request, set initial timer value
      this.seconds.set(this.timerValueSeconds());
    }
  }

  private saveTimerState(): void {
    const otpState = {
      otpRequestedAt: this.authService.getOtpResendTimerKey().otpRequestedAt || Date.now(),
      remainingSeconds: this.seconds(),
    };
    this.authService.setOtpResendTimerKey(otpState);
  }

  private startTimer(seconds: number): void {
    this.seconds.set(seconds);
    this.isResendDisabled.set(true);
    this.display.set(this.formatTimerValue(this.seconds()));

    clearInterval(this.timerInterval); // Ensure no duplicate intervals
    this.timerInterval = setInterval(() => {
      this.seconds.update(v => v - 1);
      this.display.set(this.formatTimerValue(this.seconds()));
      if (this.shouldSaveTimerKey()) {
        this.saveTimerState(); // Save timer state on each tick
      }


      if (this.seconds() <= 0) {
        clearInterval(this.timerInterval);
        this.isResendDisabled.set(false);
        this.authService.removeOtpResendTimerKey();
      }
    }, 1000);
  }

  private formatTimerValue(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const formattedSeconds = seconds % 60 < 10 ? `0${seconds % 60}` : `${seconds % 60}`;
    return `${minutes}:${formattedSeconds}`;
  }

  private listenForOtpEvent() {
    this.generalService.otpClear$.pipe(this.untilDestroyed()).subscribe((clear) => {
      if (clear) {
        this.value.set('');
      }
    });
    this.generalService.otpRequest$.pipe(this.untilDestroyed()).subscribe((requested) => {
      if (requested && !this.isRequestingOtp() && this.isOtpRequestNeeded()) {
        this.onOtpRequestSend();
      }
    });
  }

  isValidInputs(): boolean {
    return this.value()?.length === this.digitsSize;
  }

  /**
   * Gets the trimmed email address from the input
   * Handles null/undefined values and ensures a clean string is returned
   * @returns Trimmed email address string
   */
  private getTrimmedEmailAddress(): string {
    const emailValue = this.emailAddress();
    if (!emailValue || typeof emailValue !== 'string') {
      return '';
    }
    return emailValue.trim();
  }
}