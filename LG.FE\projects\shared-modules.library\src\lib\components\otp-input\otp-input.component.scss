@use "mixins";

:host {
  display: block;
}

input[type="text"] {
  text-align: center;
  border-radius: 5px;
  background: #fff;
  border: 1px solid #7454c7;
  color: var(--deep-lavender);
  &:active,
  &:focus {
    outline: 2px solid #7454c7;
  }
}

input[type="text"]:disabled {
  opacity: 0.67;
  background-color: rgba(0, 0, 0, 0.1);
}

.flex-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.flex-container>div,
input {
  background-color: #f1f1f1;
  width: 28px;
  margin: 8px;
  text-align: center;
  line-height: 28px;
  font-size: 22px;
  
  @include mixins.breakpoint(tablet) {
    margin: 10px;
    line-height: 36px;
    width: 36px;
    font-size: 28px;
  }
}